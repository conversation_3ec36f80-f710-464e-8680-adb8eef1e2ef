# SOCKS5代理工具生态系统 - 接口标准化规范

## 概述

本文档定义了三个SOCKS5代理工具之间的标准化接口，确保工具间的数据流畅通和格式兼容。

## 工具链数据流

```
┌─────────────────┐    标准格式1     ┌─────────────────┐    标准格式2     ┌─────────────────┐
│   main-18889    │ ──────────────→ │  2to3 18889     │ ──────────────→ │ 2to3 rebuild    │
│   (扫描工具)     │                 │  (验证工具)     │                 │  (管理平台)     │
└─────────────────┘                 └─────────────────┘                 └─────────────────┘
```

## 接口标准化规范

### 接口1: main-18889 → 2to3 18889

#### 输出格式 (main-18889)
**文件格式**: 纯文本文件 (.txt)
**编码**: UTF-8
**行分隔符**: \n (Unix格式)

**数据格式**:
```
IP PORT REGION
```

**字段说明**:
- `IP`: IPv4地址，标准点分十进制格式
- `PORT`: 端口号，1-65535范围内的整数
- `REGION`: 地理位置信息，当前固定为"unknown"

**示例**:
```
************* 7890 unknown
********* 8080 unknown
********** 1080 unknown
************ 7891 unknown
```

**文件命名约定**:
- 默认: `ip_output.txt`
- 带时间戳: `ip_output_YYYYMMDD_HHMMSS.txt`
- 批次标识: `ip_output_batch_001.txt`

#### 输入处理 (2to3 18889)
**支持格式**: 
- 标准格式: `IP PORT REGION`
- 简化格式: `IP PORT` (忽略第三列)
- 兼容格式: `IP:PORT` (自动分割)

**解析规则**:
```go
// 标准解析逻辑
func parseInputLine(line string) (ip, port string, err error) {
    parts := strings.Fields(strings.TrimSpace(line))
    if len(parts) < 2 {
        return "", "", fmt.Errorf("invalid format: %s", line)
    }
    
    ip = parts[0]
    port = parts[1]
    
    // 验证IP地址格式
    if net.ParseIP(ip) == nil {
        return "", "", fmt.Errorf("invalid IP: %s", ip)
    }
    
    // 验证端口范围
    if portNum, err := strconv.Atoi(port); err != nil || portNum < 1 || portNum > 65535 {
        return "", "", fmt.Errorf("invalid port: %s", port)
    }
    
    return ip, port, nil
}
```

### 接口2: 2to3 18889 → 2to3 rebuild

#### 输出格式 (2to3 18889)
**多种格式支持**:

1. **原始格式 (3)** - `original.txt`
```
socks5://*************:7890
socks5://user:pass@*********:8080
```

2. **延迟格式 (30)** - `delay.txt`
```
socks5://*************:7890 [100ms]
socks5://user:pass@*********:8080 [250ms]
```

3. **认证格式 (multi)** - `duplicate.txt`
```
socks5://user1:pass1@*************:7890
socks5://user2:pass2@*********:8080
```

4. **无认证格式 (noauth)** - `noauth.txt`
```
socks5://*************:7890
socks5://**********:1080
```

**URL格式规范**:
```
协议://[用户名:密码@]IP地址:端口号[ [延迟]]

- 协议: 固定为 "socks5"
- 用户名: 可选，仅在需要认证时出现
- 密码: 可选，与用户名配对出现
- IP地址: IPv4地址，点分十进制格式
- 端口号: 1-65535范围内的整数
- 延迟: 可选，毫秒单位，格式为 [XXXms]
```

#### 输入处理 (2to3 rebuild)

##### API导入接口
**端点**: `POST /api/proxies`
**Content-Type**: `application/json`

**请求格式**:
```json
{
  "proxies": [
    {
      "ip": "*************",
      "port": "7890",
      "username": "",
      "password": "",
      "auth_type": "noauth",
      "source": "2to3-18889"
    },
    {
      "ip": "*********", 
      "port": "8080",
      "username": "user",
      "password": "pass",
      "auth_type": "auth",
      "source": "2to3-18889"
    }
  ],
  "batch_info": {
    "total_count": 2,
    "source_file": "original.txt",
    "import_time": "2025-01-01T00:00:00Z"
  }
}
```

**响应格式**:
```json
{
  "success": true,
  "imported_count": 2,
  "duplicated_count": 0,
  "failed_count": 0,
  "errors": [],
  "message": "成功导入2个代理"
}
```

##### 文件导入接口
**支持的URL解析**:
```go
// URL解析函数
func parseProxyURL(urlStr string) (*ProxyInfo, error) {
    u, err := url.Parse(urlStr)
    if err != nil {
        return nil, err
    }
    
    if u.Scheme != "socks5" {
        return nil, fmt.Errorf("unsupported scheme: %s", u.Scheme)
    }
    
    proxy := &ProxyInfo{
        IP:   u.Hostname(),
        Port: u.Port(),
    }
    
    // 解析认证信息
    if u.User != nil {
        proxy.Username = u.User.Username()
        proxy.Password, _ = u.User.Password()
        proxy.AuthType = "auth"
        proxy.AuthRequired = true
    } else {
        proxy.AuthType = "noauth"
        proxy.AuthRequired = false
    }
    
    return proxy, nil
}
```

## 数据验证规范

### 通用验证规则

#### IP地址验证
```go
func validateIP(ip string) error {
    if net.ParseIP(ip) == nil {
        return fmt.Errorf("invalid IP address: %s", ip)
    }
    
    // 检查是否为私有地址（可选）
    parsedIP := net.ParseIP(ip)
    if parsedIP.IsLoopback() || parsedIP.IsMulticast() {
        return fmt.Errorf("invalid IP type: %s", ip)
    }
    
    return nil
}
```

#### 端口验证
```go
func validatePort(port string) error {
    portNum, err := strconv.Atoi(port)
    if err != nil {
        return fmt.Errorf("invalid port format: %s", port)
    }
    
    if portNum < 1 || portNum > 65535 {
        return fmt.Errorf("port out of range: %d", portNum)
    }
    
    return nil
}
```

#### 认证信息验证
```go
func validateCredentials(username, password string) error {
    if username == "" && password != "" {
        return fmt.Errorf("username cannot be empty when password is provided")
    }
    
    if len(username) > 255 || len(password) > 255 {
        return fmt.Errorf("username or password too long")
    }
    
    return nil
}
```

## 错误处理规范

### 错误代码标准

| 错误代码 | 错误类型 | 描述 | 处理建议 |
|---------|----------|------|----------|
| E001 | 格式错误 | 输入数据格式不正确 | 跳过该条记录，记录错误日志 |
| E002 | 验证失败 | 数据验证不通过 | 跳过该条记录，记录详细错误 |
| E003 | 重复数据 | 数据已存在 | 更新现有记录或跳过 |
| E004 | 网络错误 | 网络连接失败 | 重试或标记为不可用 |
| E005 | 系统错误 | 系统资源不足 | 降级处理或暂停操作 |

### 错误响应格式
```json
{
  "success": false,
  "error_code": "E001",
  "error_message": "Invalid data format",
  "details": {
    "line_number": 15,
    "raw_data": "invalid_line_content",
    "expected_format": "IP PORT REGION"
  },
  "suggestions": [
    "Check input file format",
    "Ensure each line contains IP and PORT"
  ]
}
```

## 性能优化规范

### 批处理建议
- **文件读取**: 使用流式读取，避免一次性加载大文件
- **批处理大小**: 推荐每批处理100-1000条记录
- **并发控制**: 根据系统资源调整并发数
- **内存管理**: 及时释放已处理的数据

### 示例批处理代码
```go
func processBatch(filename string, batchSize int) error {
    file, err := os.Open(filename)
    if err != nil {
        return err
    }
    defer file.Close()
    
    scanner := bufio.NewScanner(file)
    batch := make([]string, 0, batchSize)
    
    for scanner.Scan() {
        line := strings.TrimSpace(scanner.Text())
        if line == "" {
            continue
        }
        
        batch = append(batch, line)
        
        if len(batch) >= batchSize {
            if err := processBatchData(batch); err != nil {
                log.Printf("Batch processing error: %v", err)
            }
            batch = batch[:0] // 重置批次
        }
    }
    
    // 处理剩余数据
    if len(batch) > 0 {
        return processBatchData(batch)
    }
    
    return scanner.Err()
}
```

## 兼容性保证

### 向后兼容性
- 新版本必须支持旧格式的数据输入
- 配置文件格式变更需要提供迁移工具
- API接口变更需要保持版本兼容

### 格式演进
- 通过版本号管理格式变更
- 提供格式转换工具
- 支持多版本格式并存

### 示例版本标识
```json
{
  "format_version": "1.0",
  "tool_version": "main-18889-v0.1.2",
  "export_time": "2025-01-01T00:00:00Z",
  "data": [
    // 实际数据
  ]
}
```

## 测试验证

### 接口测试用例

#### 测试数据集
```
# 正常数据
************* 7890 unknown
********* 8080 unknown

# 边界情况
******* 1 unknown          # 最小端口
*************** 65535 unknown  # 最大端口

# 异常数据
256.1.1.1 7890 unknown     # 无效IP
*********** 70000 unknown  # 端口超范围
*********** abc unknown    # 无效端口格式
```

#### 自动化测试
```bash
# 接口1测试
./test_interface_1.sh input_samples/ expected_output/

# 接口2测试  
./test_interface_2.sh proxy_urls/ expected_api_response/

# 端到端测试
./test_end_to_end.sh cidr_config.ini final_database.db
```

这个接口标准化规范确保了三个工具之间的无缝集成和数据流畅通。