package main

import (
	"bufio"
	"fmt"
	"net"
	"net/url"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"
)

// InputAdapter 输入适配器，处理来自main-18889的数据
type InputAdapter struct {
	filename string
}

// NewInputAdapter 创建输入适配器
func NewInputAdapter(filename string) *InputAdapter {
	return &InputAdapter{filename: filename}
}

// ParseInputFile 解析输入文件，支持多种格式
func (ia *InputAdapter) ParseInputFile() ([]ProxyInfo, error) {
	file, err := os.Open(ia.filename)
	if err != nil {
		return nil, fmt.Errorf("failed to open input file: %v", err)
	}
	defer file.Close()
	
	var proxies []ProxyInfo
	scanner := bufio.NewScanner(file)
	lineNum := 0
	
	for scanner.Scan() {
		lineNum++
		line := strings.TrimSpace(scanner.Text())
		
		// 跳过空行和注释
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}
		
		proxy, err := ia.parseLine(line, lineNum)
		if err != nil {
			fmt.Printf("Warning: line %d parse failed: %v\n", lineNum, err)
			continue
		}
		
		proxies = append(proxies, *proxy)
	}
	
	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("error reading file: %v", err)
	}
	
	return proxies, nil
}

// parseLine 解析单行输入，支持多种格式
func (ia *InputAdapter) parseLine(line string, lineNum int) (*ProxyInfo, error) {
	// 格式1: IP PORT REGION (标准格式)
	if proxy, err := ia.parseStandardFormat(line); err == nil {
		return proxy, nil
	}
	
	// 格式2: IP PORT (简化格式)
	if proxy, err := ia.parseSimpleFormat(line); err == nil {
		return proxy, nil
	}
	
	// 格式3: IP:PORT (紧凑格式)
	if proxy, err := ia.parseCompactFormat(line); err == nil {
		return proxy, nil
	}
	
	return nil, fmt.Errorf("unsupported format at line %d: %s", lineNum, line)
}

// parseStandardFormat 解析标准格式: IP PORT REGION
func (ia *InputAdapter) parseStandardFormat(line string) (*ProxyInfo, error) {
	parts := strings.Fields(line)
	if len(parts) != 3 {
		return nil, fmt.Errorf("expected 3 fields, got %d", len(parts))
	}
	
	return ia.createProxy(parts[0], parts[1])
}

// parseSimpleFormat 解析简化格式: IP PORT
func (ia *InputAdapter) parseSimpleFormat(line string) (*ProxyInfo, error) {
	parts := strings.Fields(line)
	if len(parts) != 2 {
		return nil, fmt.Errorf("expected 2 fields, got %d", len(parts))
	}
	
	return ia.createProxy(parts[0], parts[1])
}

// parseCompactFormat 解析紧凑格式: IP:PORT
func (ia *InputAdapter) parseCompactFormat(line string) (*ProxyInfo, error) {
	if !strings.Contains(line, ":") {
		return nil, fmt.Errorf("no colon separator found")
	}
	
	parts := strings.Split(line, ":")
	if len(parts) != 2 {
		return nil, fmt.Errorf("invalid IP:PORT format")
	}
	
	return ia.createProxy(parts[0], parts[1])
}

// createProxy 创建代理信息对象并验证
func (ia *InputAdapter) createProxy(ip, port string) (*ProxyInfo, error) {
	// 验证IP地址
	if net.ParseIP(ip) == nil {
		return nil, fmt.Errorf("invalid IP address: %s", ip)
	}
	
	// 验证端口
	portNum, err := strconv.Atoi(port)
	if err != nil || portNum < 1 || portNum > 65535 {
		return nil, fmt.Errorf("invalid port: %s", port)
	}
	
	return &ProxyInfo{
		IP:   ip,
		Port: port,
	}, nil
}

// OutputAdapter 输出适配器，生成标准化的输出格式
type OutputAdapter struct {
	formats map[string]*os.File
	config  *Config
}

// NewOutputAdapter 创建输出适配器
func NewOutputAdapter(config *Config) (*OutputAdapter, error) {
	adapter := &OutputAdapter{
		formats: make(map[string]*os.File),
		config:  config,
	}
	
	// 初始化输出文件
	for format, filename := range config.Files.Outputs {
		if config.Options.OutputFormats[format] {
			file, err := os.OpenFile(filename, os.O_CREATE|os.O_WRONLY|os.O_TRUNC, 0644)
			if err != nil {
				return nil, fmt.Errorf("failed to create output file %s: %v", filename, err)
			}
			adapter.formats[format] = file
		}
	}
	
	return adapter, nil
}

// WriteResults 写入测试结果到各种格式的文件
func (oa *OutputAdapter) WriteResults(results *TestResults) error {
	// 写入原始格式 (3)
	if file, exists := oa.formats["3"]; exists {
		if err := oa.writeOriginalFormat(file, results.Normal); err != nil {
			return fmt.Errorf("failed to write original format: %v", err)
		}
	}
	
	// 写入带延迟格式 (30)
	if file, exists := oa.formats["30"]; exists {
		if err := oa.writeDelayFormat(file, results.Normal); err != nil {
			return fmt.Errorf("failed to write delay format: %v", err)
		}
	}
	
	// 写入多账号格式 (multi)
	if file, exists := oa.formats["multi"]; exists {
		if err := oa.writeMultiFormat(file, results.Multi); err != nil {
			return fmt.Errorf("failed to write multi format: %v", err)
		}
	}
	
	// 写入无认证格式 (noauth)
	if file, exists := oa.formats["noauth"]; exists {
		if err := oa.writeNoAuthFormat(file, results.NoAuth); err != nil {
			return fmt.Errorf("failed to write noauth format: %v", err)
		}
	}
	
	return nil
}

// writeOriginalFormat 写入原始格式 socks5://[user:pass@]ip:port
func (oa *OutputAdapter) writeOriginalFormat(file *os.File, proxies []ProxyInfo) error {
	writer := bufio.NewWriter(file)
	defer writer.Flush()
	
	// 写入文件头
	header := fmt.Sprintf("# SOCKS5 Proxy URLs - Original Format\n# Generated at: %s\n",
		time.Now().Format("2006-01-02 15:04:05"))
	writer.WriteString(header)
	
	for _, proxy := range proxies {
		url := oa.formatProxyURL(proxy, false)
		fmt.Fprintln(writer, url)
	}
	
	return nil
}

// writeDelayFormat 写入带延迟格式 socks5://[user:pass@]ip:port [XXXms]
func (oa *OutputAdapter) writeDelayFormat(file *os.File, proxies []ProxyInfo) error {
	writer := bufio.NewWriter(file)
	defer writer.Flush()
	
	// 写入文件头
	header := fmt.Sprintf("# SOCKS5 Proxy URLs with Latency\n# Generated at: %s\n",
		time.Now().Format("2006-01-02 15:04:05"))
	writer.WriteString(header)
	
	for _, proxy := range proxies {
		url := oa.formatProxyURL(proxy, true)
		fmt.Fprintln(writer, url)
	}
	
	return nil
}

// writeMultiFormat 写入多账号格式
func (oa *OutputAdapter) writeMultiFormat(file *os.File, proxies []ProxyInfo) error {
	writer := bufio.NewWriter(file)
	defer writer.Flush()
	
	// 写入文件头
	header := fmt.Sprintf("# SOCKS5 Proxies with Authentication\n# Generated at: %s\n",
		time.Now().Format("2006-01-02 15:04:05"))
	writer.WriteString(header)
	
	for _, proxy := range proxies {
		if proxy.User != "" && proxy.Password != "" {
			url := oa.formatProxyURL(proxy, false)
			fmt.Fprintln(writer, url)
		}
	}
	
	return nil
}

// writeNoAuthFormat 写入无认证格式
func (oa *OutputAdapter) writeNoAuthFormat(file *os.File, proxies []ProxyInfo) error {
	writer := bufio.NewWriter(file)
	defer writer.Flush()
	
	// 写入文件头
	header := fmt.Sprintf("# SOCKS5 Proxies without Authentication\n# Generated at: %s\n",
		time.Now().Format("2006-01-02 15:04:05"))
	writer.WriteString(header)
	
	for _, proxy := range proxies {
		if proxy.User == "" && proxy.Password == "" {
			url := oa.formatProxyURL(proxy, false)
			fmt.Fprintln(writer, url)
		}
	}
	
	return nil
}

// formatProxyURL 格式化代理URL
func (oa *OutputAdapter) formatProxyURL(proxy ProxyInfo, includeLatency bool) string {
	var urlStr string
	
	if proxy.User != "" && proxy.Password != "" {
		// 带认证的URL
		urlStr = fmt.Sprintf("socks5://%s:%s@%s:%s",
			url.QueryEscape(proxy.User),
			url.QueryEscape(proxy.Password),
			proxy.IP,
			proxy.Port)
	} else {
		// 无认证的URL
		urlStr = fmt.Sprintf("socks5://%s:%s", proxy.IP, proxy.Port)
	}
	
	// 添加延迟信息
	if includeLatency && proxy.Latency > 0 {
		latencyMs := proxy.Latency / time.Millisecond
		urlStr += fmt.Sprintf(" [%dms]", latencyMs)
	}
	
	return urlStr
}

// Close 关闭所有输出文件
func (oa *OutputAdapter) Close() error {
	var lastErr error
	
	for format, file := range oa.formats {
		if err := file.Close(); err != nil {
			fmt.Printf("Warning: failed to close %s format file: %v\n", format, err)
			lastErr = err
		}
	}
	
	return lastErr
}

// ValidateProxyURL 验证代理URL格式是否符合标准
func ValidateProxyURL(urlStr string) error {
	// 基本URL解析
	u, err := url.Parse(urlStr)
	if err != nil {
		return fmt.Errorf("invalid URL format: %v", err)
	}
	
	// 验证协议
	if u.Scheme != "socks5" {
		return fmt.Errorf("unsupported scheme: %s, expected socks5", u.Scheme)
	}
	
	// 验证主机和端口
	host := u.Hostname()
	port := u.Port()
	
	if net.ParseIP(host) == nil {
		return fmt.Errorf("invalid IP address: %s", host)
	}
	
	if port == "" {
		return fmt.Errorf("port is required")
	}
	
	portNum, err := strconv.Atoi(port)
	if err != nil || portNum < 1 || portNum > 65535 {
		return fmt.Errorf("invalid port: %s", port)
	}
	
	// 验证延迟格式（如果存在）
	if strings.Contains(urlStr, " [") && strings.Contains(urlStr, "ms]") {
		re := regexp.MustCompile(`\s+\[(\d+)ms\]$`)
		if !re.MatchString(urlStr) {
			return fmt.Errorf("invalid latency format")
		}
	}
	
	return nil
}

// ConvertFromMainOutput 将main-18889的输出转换为标准输入格式
func ConvertFromMainOutput(mainOutputFile string) ([]ProxyInfo, error) {
	adapter := NewInputAdapter(mainOutputFile)
	return adapter.ParseInputFile()
}

// GenerateAPIImportData 生成用于API导入的JSON数据
func GenerateAPIImportData(proxies []ProxyInfo, sourceFile string) map[string]interface{} {
	apiProxies := make([]map[string]interface{}, len(proxies))
	
	for i, proxy := range proxies {
		authType := "noauth"
		if proxy.User != "" && proxy.Password != "" {
			authType = "auth"
		}
		
		apiProxies[i] = map[string]interface{}{
			"ip":       proxy.IP,
			"port":     proxy.Port,
			"username": proxy.User,
			"password": proxy.Password,
			"auth_type": authType,
			"source":   "2to3-18889",
		}
	}
	
	return map[string]interface{}{
		"proxies": apiProxies,
		"batch_info": map[string]interface{}{
			"total_count":  len(proxies),
			"source_file":  sourceFile,
			"import_time":  time.Now().Format(time.RFC3339),
		},
	}
}

// GetSupportedInputFormats 返回支持的输入格式说明
func GetSupportedInputFormats() string {
	return `Supported Input Formats:
1. Standard format: IP PORT REGION
   Example: ************* 7890 unknown

2. Simple format: IP PORT  
   Example: ************* 7890

3. Compact format: IP:PORT
   Example: *************:7890

Input file requirements:
- UTF-8 encoding
- Unix line endings (\n)
- Comments start with #
- Empty lines are ignored`
}

// GetSupportedOutputFormats 返回支持的输出格式说明  
func GetSupportedOutputFormats() string {
	return `Supported Output Formats:
1. Original format (3): socks5://[user:pass@]ip:port
2. Delay format (30): socks5://[user:pass@]ip:port [XXXms]  
3. Multi format (multi): socks5://user:pass@ip:port
4. NoAuth format (noauth): socks5://ip:port

All formats:
- Use socks5:// scheme
- Include authentication when available
- Follow standard URL encoding rules`
}