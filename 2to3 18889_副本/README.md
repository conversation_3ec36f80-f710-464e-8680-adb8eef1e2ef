# SOCKS5代理验证工具

**项目定位**: 代理测试验证器  
**语言**: Go  
**复杂度**: 中  
**维护性**: 高  

## 项目概述

这是一个专门用于验证SOCKS5代理可用性的工具，作为代理管理工作流的第二环节。本工具接收IP:PORT列表，通过标准SOCKS5协议测试代理的连通性和认证方式，并按不同类别输出验证结果。

## 核心功能

- **无认证代理测试**: 验证无需认证即可使用的SOCKS5代理
- **认证代理测试**: 使用配置文件中的认证信息测试需要认证的代理
- **批量并发测试**: 支持多线程并发测试，提升验证效率
- **多格式输出**: 支持多种输出格式，便于后续处理
- **延迟测量**: 记录代理连接延迟，评估代理性能
- **去重处理**: 自动去除重复的代理，避免重复测试

## 技术特点

- **验证逻辑独立**: 专注于代理验证，与发现和管理分离
- **多认证方式支持**: 支持无认证和用户名密码认证
- **配置灵活**: 支持自定义测试参数和认证信息
- **输出标准化**: 多种格式便于与其他工具集成

## 项目结构

```
.
├── main.go          # 主程序入口和命令行处理
├── config.go        # 配置文件解析和管理
├── tester.go        # 代理测试器核心逻辑
├── proxy.go         # 代理连接测试实现
├── thread.go        # 并发控制和线程管理
├── types.go         # 数据结构定义
├── config.json      # 配置文件
└── README.md        # 项目文档
```

## 安装

### 编译安装
```shell
git clone [repository-url]
cd "2to3 18889_副本"
go build -buildvcs=false -o proxy_tester
```

### 依赖说明
- Go 1.20+
- golang.org/x/net (SOCKS5代理支持)

## 配置文件

配置文件采用JSON格式：

```json
{
  "files": {
    "input": "ip_output.txt",
    "outputs": {
      "3": "original.txt",
      "30": "delay.txt", 
      "multi": "duplicate.txt",
      "noauth": "noauth.txt"
    }
  },
  "options": {
    "thread_limit": 10,
    "timeout_seconds": 5,
    "output_formats": {
      "3": true,
      "30": true,
      "multi": true,
      "noauth": true
    }
  },
  "credentials": [
    ["user1", "pass1"],
    ["user2", "pass2"]
  ]
}
```

## 使用方法

### 基础用法
```shell
# 使用默认配置测试代理
./proxy_tester -config config.json -infile ip_list.txt

# 自定义输出格式
./proxy_tester -config config.json -infile ip_list.txt -output "3,30,noauth"

# 调整并发和超时
./proxy_tester -timeout 10 -limit 20 -infile ip_list.txt
```

### 命令行参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| -config | string | config.json | 配置文件路径 |
| -infile | string | ip_output2.txt | 输入文件路径 |
| -output | string | 3,30,multi,noauth | 输出格式选择 |
| -timeout | int | 3 | 代理测试超时时间(秒) |
| -limit | int | 10 | 最大并发线程数 |
| -outputOriginal | string | original.txt | 原始格式输出文件 |
| -outputWithDelay | string | delay.txt | 带延迟输出文件 |
| -duplicate | string | duplicate.txt | 多账号代理输出文件 |
| -noauth | string | noauth.txt | 无认证代理输出文件 |

### 输入格式

输入文件每行包含一个代理信息：
```
************* 7890
********* 8080 
********** 1080
```

## 输出格式

### 1. 原始格式 (3)
标准SOCKS5 URL格式：
```
socks5://*************:7890
socks5://user:pass@*********:8080
```

### 2. 带延迟格式 (30)
包含连接延迟信息：
```
socks5://*************:7890 [100ms]
socks5://user:pass@*********:8080 [250ms]
```

### 3. 多账号格式 (multi)
支持认证的代理：
```
socks5://user1:pass1@*************:7890
socks5://user2:pass2@*********:8080
```

### 4. 无认证格式 (noauth)
无需认证的代理：
```
socks5://*************:7890
socks5://**********:1080
```

## 与工作流集成

本工具在SOCKS5代理管理工作流中的位置：

```
1. 扫描发现 (main-18889工具)
   ↓ IP:PORT列表
2. 代理验证 (本工具) ← 当前环节
   ↓ 验证结果
3. 数据管理 (2to3 rebuild工具)
```

### 数据流标准化

- **输入**: 兼容main-18889的输出格式 `IP PORT REGION`
- **输出**: 标准SOCKS5 URL格式，便于下游工具处理

## 测试流程

### 验证逻辑
1. **无认证测试**: 首先尝试无认证连接
2. **认证测试**: 如果无认证失败，使用配置的认证信息
3. **连通性验证**: 通过连接ipinfo.io:80验证代理功能
4. **延迟测量**: 记录从连接建立到响应的时间
5. **结果分类**: 根据认证方式和测试结果分类保存

### 性能优化
- 并发控制避免资源耗尽
- 超时机制防止长时间等待
- 内存清理避免内存泄漏
- 去重机制避免重复测试

## 故障排除

### 常见问题

1. **网络连接失败**
   - 检查网络连接和防火墙设置
   - 确认测试目标(ipinfo.io)可访问

2. **大量超时**
   - 适当增加timeout参数
   - 降低并发数(limit)减少网络压力

3. **配置文件错误**
   - 检查JSON格式是否正确
   - 确认文件路径是否存在

### 调试方法
```shell
# 单线程测试便于调试
./proxy_tester -limit 1 -timeout 10

# 测试少量代理
head -10 ip_list.txt > test_input.txt
./proxy_tester -infile test_input.txt
```

## 开发说明

### 核心组件

1. **ProxyTester**: 测试器主体，管理整个测试流程
2. **配置管理**: 解析和验证配置文件
3. **并发控制**: 管理工作线程和资源限制
4. **结果管理**: 收集、去重和分类测试结果

### 扩展开发

- 支持更多认证方式(如NTLM)
- 添加更多测试目标
- 支持IPv6代理测试
- 增加代理质量评分

## 更新日志

### 当前版本
- ✅ 修复网络依赖下载问题
- ✅ 移除硬编码的认证信息
- ✅ 改进配置文件中认证信息的使用
- ✅ 优化代码结构，移除重复逻辑
- ✅ 完善错误处理和超时控制
- ✅ 标准化输入输出格式

### 后续计划
- [ ] 支持更多代理协议(HTTP/HTTPS)
- [ ] 添加代理质量评分机制
- [ ] 实现结果缓存和增量测试
- [ ] 支持自定义测试目标配置

## 与其他工具的集成

### 从main-18889接收数据
```shell
# main-18889输出 -> 本工具输入
./scanSocks5 -c config.ini -o discovered_proxies.txt
./proxy_tester -infile discovered_proxies.txt -output "3,30,noauth"
```

### 向2to3 rebuild传递数据
```shell
# 本工具输出 -> 2to3 rebuild输入
./proxy_tester -output "3" -outputOriginal validated_proxies.txt
# 然后通过API或文件导入到2to3 rebuild数据库
```

这种设计确保了各工具间的松耦合，每个工具都可以独立使用，同时又能很好地集成到完整的工作流中。