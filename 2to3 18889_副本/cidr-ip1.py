import ipaddress

# Global variables
TRAVERSAL_DIRECTION = "left_to_right"  # Options: "right_to_left", "left_to_right"
ORDER = "descending"  # Options: "ascending", "descending"

def validate_cidr(cidr):
    """Validate if the input is a valid CIDR."""
    try:
        network = ipaddress.IPv4Network(cidr, strict=True)
        return network
    except ValueError:
        return None

def generate_ips_from_cidr(network, f):
    """Generate IPs for a single CIDR block and write them to the file."""
    ips = list(network.hosts())  # Exclude network and broadcast addresses

    # Apply sorting based on ORDER
    if ORDER == "descending":
        ips = list(reversed(ips))

    # Generate IPs based on TRAVERSAL_DIRECTION
    if TRAVERSAL_DIRECTION == "right_to_left":
        # Right-to-left: Iterate over least significant segments first
        for fourth_octet in range(256):
            for ip in ips:
                if ip.packed[3] == fourth_octet:
                    f.write(str(ip) + "\n")
    elif TRAVERSAL_DIRECTION == "left_to_right":
        # Left-to-right: Default order
        for ip in ips:
            f.write(str(ip) + "\n")
    else:
        print(f"Unknown traversal direction: {TRAVERSAL_DIRECTION}")

def generate_ips_from_cidrs(cidrs, output_file):
    """Generate IPs from multiple CIDRs and write them to a file."""
    with open(output_file, "w") as f:
        for cidr in cidrs:
            network = validate_cidr(cidr)
            if not network:
                print(f"Skipping invalid CIDR: {cidr}")
                continue

            print(f"Generating IPs for CIDR: {cidr}")
            generate_ips_from_cidr(network, f)

def main():
    print("Enter CIDR blocks one by one (e.g., *************/19).")
    print("Type 'done' when you are finished.\n")

    cidrs = []
    while True:
        user_input = input("Enter CIDR block (or 'done' to finish): ").strip()
        if user_input.lower() == "done":
            break
        if validate_cidr(user_input):
            cidrs.append(user_input)
        else:
            print(f"Invalid CIDR format: {user_input}. Please try again.")

    if not cidrs:
        print("No valid CIDR blocks provided. Exiting program.")
        return

    output_file = "cidr1.txt"
    generate_ips_from_cidrs(cidrs, output_file)
    print(f"IPs from all valid CIDRs have been generated and saved to {output_file}.")

if __name__ == "__main__":
    main()
