# SOCKS5代理验证工具 - 项目规格说明

## 项目定位

### 核心定位
- **类型**: 代理测试验证器
- **定位**: 中等复杂度工具，专注验证逻辑
- **角色**: 代理管理工作流的第二环节
- **复杂度**: 中（平衡功能和简洁性）
- **维护性**: 高（模块化设计）

### 设计理念
- **验证专业化**: 专注于代理可用性和性能验证
- **多认证支持**: 支持多种SOCKS5认证机制
- **配置灵活性**: 高度可配置的测试参数
- **结果标准化**: 多格式输出便于集成

## 详细需求分析

### 功能性需求

#### FR-001 无认证代理验证
- **需求描述**: 测试无需认证的SOCKS5代理
- **测试机制**: 直接建立SOCKS5连接
- **验证方法**: 通过代理连接测试目标
- **超时控制**: 可配置的连接超时
- **结果标准**: 连接成功即为可用

#### FR-002 认证代理验证
- **需求描述**: 测试需要用户名密码认证的代理
- **认证机制**: SOCKS5用户名密码认证
- **凭据管理**: 从配置文件读取认证信息
- **批量测试**: 支持多组认证信息测试
- **失败处理**: 认证失败优雅降级

#### FR-003 批量并发验证
- **需求描述**: 高效的批量代理验证
- **并发控制**: 可配置的线程池大小
- **资源管理**: 防止连接泄漏和资源耗尽
- **进度跟踪**: 实时显示验证进度
- **异常隔离**: 单个失败不影响其他验证

#### FR-004 连通性验证
- **需求描述**: 通过实际网络请求验证代理功能
- **测试目标**: 默认使用ipinfo.io:80
- **请求机制**: 发送HTTP请求验证连通性
- **响应验证**: 检查是否能收到响应
- **延迟测量**: 记录连接建立到响应的时间

#### FR-005 多格式输出
- **需求描述**: 支持多种输出格式满足不同需求
- **格式类型**:
  - 原始格式(3): 标准SOCKS5 URL
  - 延迟格式(30): 包含性能信息
  - 认证格式(multi): 需要认证的代理
  - 无认证格式(noauth): 无需认证的代理
- **文件管理**: 分类保存到不同文件
- **格式一致**: 标准化的输出格式

### 非功能性需求

#### NFR-001 性能要求
- **验证速度**: 支持大批量代理快速验证
- **内存效率**: 流式处理，避免内存积累
- **网络优化**: 合理的超时和重试机制
- **资源复用**: 高效的连接池管理

#### NFR-002 可靠性要求
- **错误隔离**: 单个代理失败不影响整体
- **超时处理**: 防止长时间等待
- **重试机制**: 针对网络波动的重试策略
- **数据完整性**: 确保结果数据的准确性

#### NFR-003 可扩展性要求
- **认证扩展**: 支持新的认证机制
- **协议扩展**: 为其他代理协议预留接口
- **输出扩展**: 支持新的输出格式
- **配置扩展**: 灵活的配置选项

#### NFR-004 兼容性要求
- **输入兼容**: 兼容上游工具的输出格式
- **输出标准**: 便于下游工具处理
- **平台支持**: 跨平台运行支持
- **版本兼容**: 向后兼容的配置格式

## 技术规格

### 架构设计
```
┌─────────────────────────────────────────┐
│              Main程序入口                 │
├─────────────────────────────────────────┤
│  配置管理  │  参数解析  │  结果管理    │
├─────────────────────────────────────────┤
│           ProxyTester核心引擎             │
├─────────────────────────────────────────┤
│  输入处理  │  并发控制  │  结果收集    │
├─────────────────────────────────────────┤
│           代理测试模块                    │
├─────────────────────────────────────────┤
│  无认证测试 │ 认证测试  │  连通性验证  │
├─────────────────────────────────────────┤
│        网络层 (SOCKS5 + HTTP)           │
└─────────────────────────────────────────┘
```

### 数据流设计
```
输入文件(IP:PORT列表) + 配置文件
            ↓
        配置解析和验证
            ↓
        创建工作任务队列
            ↓
        并发工作线程处理
            ↓
      无认证测试 → 认证测试
            ↓
        连通性和延迟验证
            ↓
        结果分类和去重
            ↓
      多格式文件输出
```

### 接口规格

#### 输入接口
1. **代理列表文件**
   ```
   ************* 7890
   ********* 8080
   ********** 1080
   ```

2. **配置文件接口**
   ```json
   {
     "files": {
       "input": "ip_list.txt",
       "outputs": {
         "3": "original.txt",
         "30": "delay.txt",
         "multi": "auth_proxies.txt",
         "noauth": "noauth_proxies.txt"
       }
     },
     "options": {
       "thread_limit": 10,
       "timeout_seconds": 5,
       "output_formats": {
         "3": true,
         "30": true,
         "multi": true,
         "noauth": true
       }
     },
     "credentials": [
       ["user1", "pass1"],
       ["user2", "pass2"]
     ]
   }
   ```

#### 输出接口
1. **原始格式 (3)**
   ```
   socks5://*************:7890
   socks5://user:pass@*********:8080
   ```

2. **延迟格式 (30)**
   ```
   socks5://*************:7890 [100ms]
   socks5://user:pass@*********:8080 [250ms]
   ```

3. **认证格式 (multi)**
   ```
   socks5://user1:pass1@*************:7890
   socks5://user2:pass2@*********:8080
   ```

4. **无认证格式 (noauth)**
   ```
   socks5://*************:7890
   socks5://**********:1080
   ```

### 核心算法

#### 代理验证算法
```
function validateProxy(ip, port):
    1. 尝试无认证连接
       if success:
           return result as "noauth"
    
    2. 遍历认证凭据列表
       for each credential:
           if testWithAuth(ip, port, credential):
               return result as "multi"
           if timeout:
               break // 避免继续测试
    
    3. return failure
```

#### 连通性测试算法
```
function testConnectivity(proxy):
    1. 建立SOCKS5连接
    2. 通过代理连接测试目标
    3. 发送HTTP请求
    4. 等待响应并测量延迟
    5. 关闭连接
    6. 返回结果和延迟
```

## 验证策略

### 测试流程
1. **预连接检查**: 验证IP地址和端口有效性
2. **无认证测试**: 优先测试无认证连接
3. **认证测试**: 使用配置的认证信息
4. **连通性验证**: 通过实际HTTP请求验证
5. **性能测量**: 记录连接延迟
6. **结果分类**: 根据认证类型分类

### 错误处理策略

#### 网络错误
- **连接超时**: 记录为测试失败，继续下一个
- **连接拒绝**: 记录为不可用，继续测试
- **DNS解析失败**: 跳过该IP，记录错误
- **网络不可达**: 记录警告，继续其他测试

#### 认证错误
- **认证失败**: 尝试下一组认证信息
- **认证超时**: 停止该代理的认证测试
- **协议错误**: 记录为不支持，跳过

#### 系统错误
- **内存不足**: 降低并发数，继续运行
- **文件写入失败**: 切换到标准输出
- **配置错误**: 使用默认值，记录警告

## 质量指标

### 性能指标
- **验证速度**: 每秒验证代理数量
- **准确率**: 验证结果的准确性
- **资源利用**: CPU和内存使用效率
- **网络效率**: 网络带宽利用率

### 可靠性指标
- **错误恢复率**: 从错误中恢复的能力
- **稳定性**: 长时间运行的稳定性
- **数据完整性**: 结果数据的完整性
- **异常处理**: 异常情况的处理能力

## 集成接口

### 与上游工具集成
- **输入兼容**: 支持main-18889的输出格式
- **格式解析**: 自动识别输入文件格式
- **错误容忍**: 对输入格式异常的容忍度

### 与下游工具集成
- **输出标准**: 标准化的SOCKS5 URL格式
- **格式选择**: 根据下游需求选择输出格式
- **数据传递**: 便于下游工具处理的数据格式

## 扩展规划

### 近期扩展
- **认证协议**: 支持更多SOCKS5认证方法
- **测试目标**: 支持自定义测试目标
- **质量评分**: 基于延迟和稳定性的质量评分
- **批量优化**: 进一步优化大批量验证性能

### 中期扩展
- **协议支持**: 支持HTTP/HTTPS代理验证
- **智能重试**: 基于错误类型的智能重试
- **结果缓存**: 避免重复验证相同代理
- **监控集成**: 与监控系统的集成

### 长期扩展
- **机器学习**: 基于历史数据优化验证策略
- **分布式验证**: 支持多节点协作验证
- **API服务**: 提供验证服务API
- **实时验证**: 支持实时代理质量监控

这个规格文档详细定义了代理验证工具的定位、功能需求、技术实现和发展方向，为工具的持续改进提供了清晰的指导。