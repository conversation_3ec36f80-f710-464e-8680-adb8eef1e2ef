import socket
import socks
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

def check_socks5_proxy(proxy):
    """验证单个SOCKS5代理"""
    # 去除socks5://前缀并分割ip和port
    proxy = proxy.strip().replace('socks5://', '')
    ip, port = proxy.split(':')
    try:
        # 设置SOCKS5代理
        socks.set_default_proxy(socks.SOCKS5, ip, int(port))
        socket.socket = socks.socksocket
        
        # 测试连接
        start_time = time.time()
        s = socket.socket()
        s.settimeout(5)
        s.connect(('www.baidu.com', 80))
        s.close()
        
        latency = int((time.time() - start_time) * 1000)  # 计算延迟
        return True, latency
    except Exception as e:
        return False, 0

def process_proxy_file(filename):
    """处理代理文件"""
    valid_proxies = []
    
    with open(filename, 'r') as f:
        proxies = f.readlines()
    
    with ThreadPoolExecutor(max_workers=10) as executor:
        futures = {executor.submit(check_socks5_proxy, proxy): proxy for proxy in proxies}
        
        for future in as_completed(futures):
            proxy = futures[future]
            try:
                is_valid, latency = future.result()
                if is_valid:
                    valid_proxies.append(f"{proxy.strip()} (latency: {latency}ms)")
            except Exception as e:
                continue
    
    return valid_proxies

def main():
    import os
    import glob
    
    # 查找所有以noauth开头的文件
    proxy_files = glob.glob('noauth*.txt')
    
    if not proxy_files:
        print("未找到以noauth开头的代理文件")
        return
    
    for file in proxy_files:
        print(f"\n正在验证文件: {file}")
        valid_proxies = process_proxy_file(file)
        
        if valid_proxies:
            print("\n有效代理列表:")
            for proxy in valid_proxies:
                print(proxy)
        else:
            print("未找到有效代理")
            
        # 保存结果到新文件
        output_file = f"valid_{file}"
        with open(output_file, 'w') as f:
            f.write('\n'.join(valid_proxies))
        print(f"\n验证结果已保存到: {output_file}")

if __name__ == '__main__':
    main()
