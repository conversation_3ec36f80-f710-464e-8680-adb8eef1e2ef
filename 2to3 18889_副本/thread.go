package main

// Thread 线程控制
type Thread struct {
	semaphore chan struct{}
}

// NewThread 创建新的线程控制器
func NewThread(limit int) *Thread {
	return &Thread{
		semaphore: make(chan struct{}, limit),
	}
}

// Run 运行一个任务
func (t *Thread) Run(task func(map[string]any), arg map[string]any) {
	t.semaphore <- struct{}{}
	go func() {
		defer func() { <-t.semaphore }()
		task(arg)
	}()
}

// Wait 等待所有任务完成
func (t *Thread) Wait() {
	for i := 0; i < cap(t.semaphore); i++ {
		t.semaphore <- struct{}{}
	}
}
