package main

import (
	"flag"
	"fmt"
	"os"
	"strconv"
	"strings"
)

func init() {
	// 自定义帮助信息
	flag.Usage = func() {
		exeName := "main"
		if len(os.Args) > 0 {
			// 从完整路径中提取文件名
			parts := strings.Split(os.Args[0], string(os.PathSeparator))
			if len(parts) > 0 {
				exeName = parts[len(parts)-1]
			}
		}

		fmt.Fprintf(os.<PERSON>der<PERSON>, "\nSOCKS5代理测试工具\n")
		fmt.Fprintf(os.Stderr, "----------------------------------------\n\n")
		fmt.Fprintf(os.Stderr, "用法:\n  %s [选项]\n\n", exeName)
		fmt.Fprintf(os.Stderr, "示例:\n")
		fmt.Fprintf(os.Stderr, "  # 测试代理并输出原始格式和延迟信息\n")
		fmt.Fprintf(os.Stderr, "  %s -config config.json -infile proxies.txt -output 3,30\n\n", exeName)
		fmt.Fprintf(os.Stderr, "  # 仅测试多账号和无认证代理，增加并发和超时\n")
		fmt.Fprintf(os.Stderr, "  %s -timeout 5 -limit 20 -output multi,noauth\n\n", exeName)
		fmt.Fprintf(os.Stderr, "选项:\n")
		// 移除任何具有目录信息的错误消息
		flag.VisitAll(func(f *flag.Flag) {
			name := "-" + f.Name
			usage := strings.Split(f.Usage, ";")[0]
			if _, err := strconv.Atoi(f.DefValue); err == nil {
				fmt.Fprintf(os.Stderr, "  %-25s %s (default %s)\n", name, usage, f.DefValue)
			} else if strings.Contains(f.Usage, "输出格式") {
				fmt.Fprintf(os.Stderr, "  %-25s %s (default \"%s\")\n", name, usage, "3,30,multi,noauth")
			} else {
				fmt.Fprintf(os.Stderr, "  %-25s %s (default \"%s\")\n", name, usage, f.DefValue)
			}
		})
	}
}

var (
	configFile             = flag.String("config", "config.json", "配置文件路径，包含认证信息和允许的端口")
	timeoutT               = flag.Int("timeout", 3, "代理测试超时时间（秒）")
	threadLimit            = flag.Int("limit", 10, "最大并发测试线程数")
	infile                 = flag.String("infile", "ip_output2.txt", "输入文件，每行格式：IP 端口")
	outputs                = flag.String("output", "3,30,multi,noauth", "输出格式（支持：3=原格式，30=带延迟，multi=多账号，noauth=无认证）")
	outputOriginal         = flag.String("outputOriginal", "original.txt", "原始格式输出文件（格式：socks5://user:pass@ip:port）")
	outputWithDelay        = flag.String("outputWithDelay", "delay.txt", "带延迟信息输出文件（包含连接延迟时间）")
	outputMultiCredentials = flag.String("duplicate", "duplicate.txt", "多账号代理输出文件（支持多组认证的代理）")
	outputNoAuth           = flag.String("noauth", "noauth.txt", "无认证代理输出文件（无需认证即可使用的代理）")
)

func main() {
	flag.Parse()

	// 加载配置
	cfg, err := LoadConfig(*configFile)
	if err != nil {
		cfg = LoadDefaultConfig()
		fmt.Printf("使用默认配置: %v\n", err)
	}

	// 更新配置
	cfg.Files.Input = *infile
	cfg.Files.Outputs = map[string]string{
		"3":      *outputOriginal,
		"30":     *outputWithDelay,
		"multi":  *outputMultiCredentials,
		"noauth": *outputNoAuth,
	}
	cfg.Options.ThreadLimit = *threadLimit
	cfg.Options.TimeoutSeconds = *timeoutT

	// 初始化并设置输出格式
	cfg.Options.OutputFormats = make(map[string]bool)
	for _, format := range strings.Split(*outputs, ",") {
		cfg.Options.OutputFormats[strings.TrimSpace(format)] = true
	}

	// 创建测试器并运行
	tester := NewProxyTester(cfg)
	results, err := tester.Run()
	if err != nil {
		fmt.Printf("测试过程中出错: %v\n", err)
		return
	}

	// 保存结果
	if err := SaveResults(cfg, results); err != nil {
		fmt.Printf("保存结果时出错: %v\n", err)
		return
	}

	fmt.Println("测试完成")
}
