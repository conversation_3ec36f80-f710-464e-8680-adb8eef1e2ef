package main

import (
	"context"
	"fmt"
	"net"
	"time"

	"golang.org/x/net/proxy"
)

// testProxy 测试单个代理
func (pt *ProxyTester) testProxy(ip, port string) {
	// 测试无认证访问
	if pt.testNoAuth(ip, port) {
		proxyInfo := ProxyInfo{
			IP:       ip,
			Port:     port,
			Original: fmt.Sprintf("%s %s", ip, port),
		}
		pt.addResult(&proxyInfo, "noauth")
		return
	}

	// 测试有认证访问 - 使用固定的账号密码
	latency, valid, timeout := pt.testWithAuth(ip, port, "xxx", "yyy")
	if valid {
		proxy := ProxyInfo{
			IP:       ip,
			Port:     port,
			User:     "xxx",
			Password: "yyy",
			Latency:  latency,
			Original: fmt.Sprintf("%s %s", ip, port),
		}
		pt.addResult(&proxy, "multi")
		return
	} else if timeout {
		// 如果超时，直接返回，不再测试其他认证
		return
	}

	// 测试认证库中的账号密码
	for _, cred := range pt.config.Credentials {
		latency, valid, timeout := pt.testWithAuth(ip, port, cred[0], cred[1])
		if valid {
			proxy := ProxyInfo{
				IP:       ip,
				Port:     port,
				User:     cred[0],
				Password: cred[1],
				Latency:  latency,
				Original: fmt.Sprintf("%s %s", ip, port),
			}
			pt.addResult(&proxy, "normal")
		} else if timeout {
			// 如果超时，停止测试剩余的认证
			break
		}
	}
}

// testNoAuth 测试无认证代理
func (pt *ProxyTester) testNoAuth(ip, port string) bool {
	timeout := time.Duration(pt.config.Options.TimeoutSeconds) * time.Second
	dialAddr := fmt.Sprintf("%s:%s", ip, port)
	dialer, err := proxy.SOCKS5("tcp", dialAddr, nil, proxy.Direct)
	if err != nil {
		fmt.Printf("代理测试失败：%s：%v\n", dialAddr, err)
		return false
	}

	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	connChan := make(chan net.Conn, 1)
	errChan := make(chan error, 1)

	go func() {
		conn, err := dialer.Dial("tcp", "ipinfo.io:80")
		if err != nil {
			errChan <- err
			return
		}
		connChan <- conn
	}()

	select {
	case conn := <-connChan:
		if conn != nil {
			conn.Close()
			fmt.Printf("发现无认证代理：%s\n", dialAddr)
			return true
		}
		return false
	case <-errChan:
		fmt.Printf("代理测试失败：%s\n", dialAddr)
		return false
	case <-ctx.Done():
		fmt.Printf("代理超时：%s\n", dialAddr)
		return false
	}
}

// testWithAuth 测试带认证的代理
// 返回值：延迟时间，是否验证成功，是否超时
func (pt *ProxyTester) testWithAuth(ip, port, user, password string) (time.Duration, bool, bool) {
	timeout := time.Duration(pt.config.Options.TimeoutSeconds) * time.Second
	dialAddr := fmt.Sprintf("%s:%s", ip, port)
	auth := &proxy.Auth{
		User:     user,
		Password: password,
	}

	dialer, err := proxy.SOCKS5("tcp", dialAddr, auth, proxy.Direct)
	if err != nil {
		return 0, false, false
	}

	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	start := time.Now()
	connChan := make(chan net.Conn, 1)
	errChan := make(chan error, 1)

	go func() {
		conn, err := dialer.Dial("tcp", "ipinfo.io:80")
		if err != nil {
			errChan <- err
			return
		}
		connChan <- conn
	}()

	select {
	case conn := <-connChan:
		if conn == nil {
			return 0, false, false
		}
		latency := time.Since(start)
		conn.Close()
		fmt.Printf("发现有效代理：%s（用户：%s，密码：%s）\n", dialAddr, user, password)
		return latency, true, false
	case <-errChan:
		return 0, false, false
	case <-ctx.Done():
		fmt.Printf("代理超时：%s（用户：%s）\n", dialAddr, user)
		return 0, false, true // 最后一个参数表示是否超时
	}
}
