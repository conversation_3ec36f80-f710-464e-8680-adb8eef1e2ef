# 🚀 使用代理安装开发环境指南

## 重要提示
- ❌ **不要使用 root 用户安装 Homebrew**
- ✅ **请先退出 root 用户** (`exit` 命令)
- ✅ **使用普通用户 konan 进行安装**

## 第一步：退出 root 用户
```bash
exit  # 退出 root 用户，回到 konan 用户
```

## 第二步：设置代理环境变量
```bash
export http_proxy=socks5://127.0.0.1:12334
export https_proxy=socks5://127.0.0.1:12334
export HTTP_PROXY=socks5://127.0.0.1:12334
export HTTPS_PROXY=socks5://127.0.0.1:12334
```

## 第三步：测试代理连接
```bash
curl -x socks5://127.0.0.1:12334 --connect-timeout 5 -s -o /dev/null -w "%{http_code}" http://www.google.com
# 应该返回 200 或 302
```

## 第四步：安装 Homebrew
```bash
/bin/bash -c "$(curl -x socks5://127.0.0.1:12334 -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
```

## 第五步：配置 Homebrew 环境
```bash
# 添加到 PATH
echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zprofile
eval "$(/opt/homebrew/bin/brew shellenv)"

# 配置 Homebrew 使用代理
export HOMEBREW_CURLRC=1
echo "proxy = socks5://127.0.0.1:12334" > ~/.curlrc
```

## 第六步：安装其他工具
```bash
# 安装 Go
brew install go

# 安装 Python
brew install python@3.11

# 安装 Node.js
brew install node@18

# 安装数据库工具
brew install sqlite
brew install --cask db-browser-for-sqlite

# 安装开发工具
brew install --cask docker
brew install --cask postman
```

## 第七步：配置开发环境
```bash
# 配置 Go 环境
export GOPATH=$HOME/go
export PATH=$PATH:$GOPATH/bin
echo 'export GOPATH=$HOME/go' >> ~/.zprofile
echo 'export PATH=$PATH:$GOPATH/bin' >> ~/.zprofile

# 配置 Go 代理
go env -w GOPROXY=https://goproxy.cn,direct
go env -w GOSUMDB=sum.golang.google.cn

# 配置 npm 镜像
npm config set registry https://registry.npmmirror.com

# 安装 Vue.js 工具
npm install -g create-vite
npm install -g @vue/cli
```

## 第八步：验证安装
```bash
# 检查版本
brew --version
go version
python3 --version
node --version
npm --version
sqlite3 --version
```

## 故障排除

### 如果代理失败
```bash
# 取消代理设置
unset http_proxy https_proxy HTTP_PROXY HTTPS_PROXY

# 使用国内镜像安装 Homebrew
/bin/bash -c "$(curl -fsSL https://gitee.com/ineo6/homebrew-install/raw/master/install.sh)"
```

### 如果权限问题
```bash
# 修复 Homebrew 权限
sudo chown -R $(whoami) /opt/homebrew
```

## 完成后的清理
```bash
# 删除临时代理配置
rm ~/.curlrc

# 重新加载环境变量
source ~/.zprofile
```

---

**注意：** 请按顺序执行这些命令，每个步骤完成后再进行下一步。如果遇到问题，请告诉我具体的错误信息。
