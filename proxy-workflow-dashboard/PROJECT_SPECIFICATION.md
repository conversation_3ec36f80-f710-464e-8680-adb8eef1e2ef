# SOCKS5代理工作流管理平台 - 项目规格说明

## 项目定位

### 核心定位
- **类型**: 前端工作流编排平台
- **定位**: 统一管理界面和工作流编排器
- **角色**: 整个代理管理生态系统的用户入口
- **复杂度**: 中高（平衡功能丰富性和易用性）
- **维护性**: 高（现代化技术栈和架构）

### 设计理念
- **用户体验优先**: 直观易用的Web界面
- **工作流自动化**: 自动化的多工具协调
- **实时监控**: 全程实时状态跟踪
- **数据可视化**: 丰富的图表和分析功能

## 详细需求分析

### 功能性需求

#### FR-001 工作流编排引擎
- **需求描述**: 核心的工作流定义和执行引擎
- **工作流定义**: 
  - 支持可视化工作流设计
  - 阶段间依赖关系管理
  - 条件分支和循环控制
  - 参数传递和变量替换
- **执行引擎**:
  - 异步任务执行
  - 错误处理和重试机制
  - 进度跟踪和状态管理
  - 资源调度和并发控制

#### FR-002 工具集成适配器
- **需求描述**: 与三个SOCKS5工具的标准化集成
- **main-18889适配器**:
  - 配置文件生成
  - 命令行参数构建
  - 执行状态监控
  - 输出结果解析
- **2to3 18889适配器**:
  - 输入文件准备
  - 测试参数配置
  - 进度实时监控
  - 多格式结果处理
- **2to3 rebuild适配器**:
  - API接口调用
  - 数据库操作代理
  - 批量导入管理
  - 状态同步机制

#### FR-003 用户界面系统
- **需求描述**: 现代化的Web用户界面
- **仪表板**:
  - 系统状态概览
  - 关键指标展示
  - 最近活动记录
  - 快速操作入口
- **工作流管理**:
  - 可视化流程设计器
  - 模板库管理
  - 执行历史查看
  - 参数配置界面
- **工具管理**:
  - 工具状态监控
  - 配置参数管理
  - 日志实时查看
  - 性能指标展示
- **数据分析**:
  - 统计图表展示
  - 地理分布可视化
  - 趋势分析报告
  - 自定义报表生成

#### FR-004 实时监控系统
- **需求描述**: 全程实时监控和状态跟踪
- **执行监控**:
  - 工作流执行状态
  - 各阶段进度跟踪
  - 实时日志流
  - 性能指标监控
- **系统监控**:
  - 工具运行状态
  - 资源使用情况
  - 网络连接状态
  - 错误告警机制
- **WebSocket通信**:
  - 实时状态推送
  - 双向消息通信
  - 连接状态管理
  - 断线重连机制

#### FR-005 数据管理与可视化
- **需求描述**: 全面的数据管理和可视化功能
- **数据存储**:
  - 工作流定义存储
  - 执行历史记录
  - 配置参数管理
  - 用户设置保存
- **数据可视化**:
  - 实时图表更新
  - 交互式地图展示
  - 时间序列分析
  - 多维度数据钻取
- **报表系统**:
  - 预定义报表模板
  - 自定义报表生成
  - 多格式导出支持
  - 定时报表生成

### 非功能性需求

#### NFR-001 性能要求
- **响应时间**: Web界面响应时间 < 2秒
- **并发支持**: 支持多用户同时使用
- **数据处理**: 支持大量代理数据的高效处理
- **实时性**: 状态更新延迟 < 1秒

#### NFR-002 可用性要求
- **界面友好**: 直观易用的用户界面
- **操作简便**: 最少点击完成常用操作
- **错误提示**: 清晰的错误信息和处理建议
- **帮助系统**: 完整的在线帮助和文档

#### NFR-003 可靠性要求
- **故障恢复**: 自动恢复机制
- **数据完整性**: 确保数据不丢失
- **错误处理**: 优雅的错误处理
- **状态一致性**: 保持系统状态一致

#### NFR-004 可扩展性要求
- **插件架构**: 支持功能插件扩展
- **API开放**: 提供开放的API接口
- **配置灵活**: 高度可配置的系统参数
- **模块化**: 松耦合的模块化设计

## 技术架构设计

### 系统架构图
```
┌─────────────────────────────────────────┐
│              Web浏览器                   │
├─────────────────────────────────────────┤
│            React前端应用                 │
│  ┌─────────────┐  ┌─────────────────────┐│
│  │    UI组件    │  │     状态管理        ││
│  └─────────────┘  └─────────────────────┘│
├─────────────────────────────────────────┤
│              HTTP/WebSocket              │
├─────────────────────────────────────────┤
│            Express.js API               │
│  ┌─────────────┐  ┌─────────────────────┐│
│  │   API路由    │  │    WebSocket服务    ││
│  └─────────────┘  └─────────────────────┘│
├─────────────────────────────────────────┤
│             工作流引擎                    │
│  ┌─────────────┐  ┌─────────────────────┐│
│  │  执行调度器   │  │     状态管理器      ││
│  └─────────────┘  └─────────────────────┘│
├─────────────────────────────────────────┤
│             工具适配器层                  │
│ ┌───────────┐┌───────────┐┌─────────────┐│
│ │main-18889 ││2to3 18889 ││2to3 rebuild ││
│ │  适配器   ││  适配器   ││   适配器    ││
│ └───────────┘└───────────┘└─────────────┘│
├─────────────────────────────────────────┤
│              数据存储层                   │
│  ┌─────────────┐  ┌─────────────────────┐│
│  │   SQLite    │  │      文件系统       ││
│  └─────────────┘  └─────────────────────┘│
└─────────────────────────────────────────┘
```

### 前端架构设计
```
src/
├── components/              # React组件库
│   ├── common/             # 通用组件
│   │   ├── Button/         # 按钮组件
│   │   ├── Table/          # 表格组件
│   │   ├── Chart/          # 图表组件
│   │   └── Modal/          # 弹窗组件
│   ├── workflow/           # 工作流相关组件
│   │   ├── Designer/       # 流程设计器
│   │   ├── Executor/       # 执行监控
│   │   └── Templates/      # 模板管理
│   ├── monitoring/         # 监控相关组件
│   │   ├── Dashboard/      # 仪表板
│   │   ├── Metrics/        # 指标展示
│   │   └── Logs/           # 日志查看
│   └── tools/              # 工具相关组件
│       ├── Config/         # 配置管理
│       ├── Status/         # 状态监控
│       └── Results/        # 结果展示
├── pages/                  # 页面组件
│   ├── Dashboard/          # 仪表板页面
│   ├── Workflow/           # 工作流页面
│   ├── Tools/              # 工具管理页面
│   ├── Analytics/          # 数据分析页面
│   └── Settings/           # 设置页面
├── services/               # API服务
│   ├── api.ts              # API客户端
│   ├── websocket.ts        # WebSocket客户端
│   └── auth.ts             # 认证服务
├── stores/                 # 状态管理
│   ├── workflow.ts         # 工作流状态
│   ├── monitoring.ts       # 监控状态
│   └── user.ts             # 用户状态
├── utils/                  # 工具函数
│   ├── formatters.ts       # 格式化函数
│   ├── validators.ts       # 验证函数
│   └── constants.ts        # 常量定义
├── types/                  # TypeScript类型
│   ├── workflow.ts         # 工作流类型
│   ├── monitoring.ts       # 监控类型
│   └── api.ts              # API类型
└── hooks/                  # 自定义Hooks
    ├── useWorkflow.ts      # 工作流Hooks
    ├── useMonitoring.ts    # 监控Hooks
    └── useWebSocket.ts     # WebSocket Hooks
```

### 后端架构设计
```
src/
├── controllers/            # 控制器层
│   ├── workflow.ts         # 工作流控制器
│   ├── tools.ts            # 工具控制器
│   ├── monitoring.ts       # 监控控制器
│   └── analytics.ts        # 分析控制器
├── services/               # 业务服务层
│   ├── WorkflowEngine.ts   # 工作流引擎
│   ├── ToolAdapter.ts      # 工具适配器
│   ├── DataProcessor.ts    # 数据处理器
│   ├── Scheduler.ts        # 任务调度器
│   └── NotificationService.ts # 通知服务
├── adapters/               # 工具适配器
│   ├── Main18889Adapter.ts # main-18889适配器
│   ├── Proxy18889Adapter.ts# 2to3 18889适配器
│   └── RebuildAdapter.ts   # 2to3 rebuild适配器
├── models/                 # 数据模型
│   ├── Workflow.ts         # 工作流模型
│   ├── Execution.ts        # 执行模型
│   ├── Tool.ts             # 工具模型
│   └── User.ts             # 用户模型
├── routes/                 # 路由定义
│   ├── workflow.ts         # 工作流路由
│   ├── tools.ts            # 工具路由
│   ├── monitoring.ts       # 监控路由
│   └── analytics.ts        # 分析路由
├── middleware/             # 中间件
│   ├── auth.ts             # 认证中间件
│   ├── validation.ts       # 验证中间件
│   ├── logging.ts          # 日志中间件
│   └── error.ts            # 错误处理中间件
├── database/               # 数据库层
│   ├── connection.ts       # 数据库连接
│   ├── migrations/         # 数据库迁移
│   └── seeds/              # 初始数据
├── utils/                  # 工具函数
│   ├── logger.ts           # 日志工具
│   ├── config.ts           # 配置管理
│   └── helpers.ts          # 辅助函数
├── types/                  # TypeScript类型
│   ├── workflow.ts         # 工作流类型
│   ├── tools.ts            # 工具类型
│   └── monitoring.ts       # 监控类型
└── websocket/              # WebSocket处理
    ├── handlers/           # 消息处理器
    ├── events.ts           # 事件定义
    └── server.ts           # WebSocket服务器
```

## 核心模块设计

### 工作流引擎
```typescript
interface WorkflowEngine {
  // 创建工作流
  createWorkflow(definition: WorkflowDefinition): Promise<Workflow>;
  
  // 执行工作流
  executeWorkflow(workflowId: string, parameters?: Record<string, any>): Promise<WorkflowExecution>;
  
  // 停止执行
  stopExecution(executionId: string): Promise<void>;
  
  // 获取执行状态
  getExecutionStatus(executionId: string): Promise<ExecutionStatus>;
  
  // 监听执行事件
  onExecutionEvent(callback: (event: ExecutionEvent) => void): void;
}

class WorkflowEngineImpl implements WorkflowEngine {
  private scheduler: TaskScheduler;
  private stateManager: StateManager;
  private eventEmitter: EventEmitter;
  
  async executeWorkflow(workflowId: string, parameters?: Record<string, any>): Promise<WorkflowExecution> {
    const workflow = await this.getWorkflow(workflowId);
    const execution = await this.createExecution(workflow, parameters);
    
    // 异步执行工作流
    this.scheduler.schedule(async () => {
      try {
        await this.runWorkflowStages(execution);
        await this.completeExecution(execution);
      } catch (error) {
        await this.handleExecutionError(execution, error);
      }
    });
    
    return execution;
  }
  
  private async runWorkflowStages(execution: WorkflowExecution): Promise<void> {
    for (const stage of execution.workflow.stages) {
      if (await this.shouldSkipStage(stage, execution)) {
        continue;
      }
      
      await this.executeStage(stage, execution);
    }
  }
}
```

### 工具适配器
```typescript
interface ToolAdapter {
  name: string;
  version: string;
  
  // 执行工具
  execute(config: ToolConfig): Promise<ToolResult>;
  
  // 获取状态
  getStatus(): Promise<ToolStatus>;
  
  // 停止执行
  stop(): Promise<void>;
  
  // 监听事件
  onEvent(callback: (event: ToolEvent) => void): void;
}

class Main18889Adapter implements ToolAdapter {
  name = 'main-18889';
  version = '0.1.2';
  
  async execute(config: ScanConfig): Promise<ScanResult> {
    // 生成配置文件
    const configFile = await this.generateConfigFile(config);
    
    // 构建命令行
    const command = this.buildCommand(configFile, config);
    
    // 执行扫描
    const process = spawn(command.cmd, command.args);
    
    // 监控执行过程
    return new Promise((resolve, reject) => {
      process.on('exit', (code) => {
        if (code === 0) {
          resolve(this.parseResults(config.outputFile));
        } else {
          reject(new Error(`Scan failed with code ${code}`));
        }
      });
      
      process.on('error', reject);
    });
  }
  
  private async parseResults(outputFile: string): Promise<ScanResult> {
    const content = await fs.readFile(outputFile, 'utf-8');
    const lines = content.split('\n').filter(line => line.trim());
    
    const proxies = lines.map(line => {
      const [ip, port, region] = line.split(' ');
      return { ip, port, region };
    });
    
    return {
      total: proxies.length,
      proxies,
      timestamp: new Date()
    };
  }
}
```

### 实时监控系统
```typescript
class MonitoringService {
  private wsServer: Server;
  private metrics: MetricsCollector;
  private logger: Logger;
  
  constructor() {
    this.wsServer = new Server();
    this.metrics = new MetricsCollector();
    this.setupWebSocketHandlers();
  }
  
  // 开始监控工作流执行
  startMonitoring(execution: WorkflowExecution): void {
    const monitor = new ExecutionMonitor(execution);
    
    monitor.on('progress', (progress) => {
      this.broadcast('execution:progress', {
        executionId: execution.id,
        progress
      });
    });
    
    monitor.on('stage-complete', (stage, result) => {
      this.broadcast('execution:stage-complete', {
        executionId: execution.id,
        stage: stage.id,
        result
      });
    });
    
    monitor.on('error', (error) => {
      this.broadcast('execution:error', {
        executionId: execution.id,
        error: error.message
      });
    });
  }
  
  // 广播消息到所有连接的客户端
  private broadcast(event: string, data: any): void {
    this.wsServer.emit(event, data);
  }
  
  // 设置WebSocket事件处理
  private setupWebSocketHandlers(): void {
    this.wsServer.on('connection', (socket) => {
      socket.on('subscribe:execution', (executionId) => {
        socket.join(`execution:${executionId}`);
      });
      
      socket.on('unsubscribe:execution', (executionId) => {
        socket.leave(`execution:${executionId}`);
      });
    });
  }
}
```

## 数据模型定义

### 工作流相关模型
```typescript
interface WorkflowDefinition {
  id: string;
  name: string;
  description?: string;
  version: string;
  stages: WorkflowStage[];
  created_at: Date;
  updated_at: Date;
}

interface WorkflowStage {
  id: string;
  name: string;
  tool: string;
  depends: string[];
  config: Record<string, any>;
  timeout?: number;
  retry?: RetryConfig;
  conditions?: StageCondition[];
}

interface WorkflowExecution {
  id: string;
  workflow_id: string;
  status: ExecutionStatus;
  start_time: Date;
  end_time?: Date;
  parameters: Record<string, any>;
  stages: StageExecution[];
  logs: ExecutionLog[];
  metrics: ExecutionMetrics;
}

interface StageExecution {
  stage_id: string;
  status: StageStatus;
  start_time?: Date;
  end_time?: Date;
  result?: any;
  error?: ExecutionError;
  metrics: StageMetrics;
}
```

### 监控相关模型
```typescript
interface SystemMetrics {
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  network_io: NetworkIO;
  timestamp: Date;
}

interface ExecutionMetrics {
  total_duration: number;
  stage_durations: Record<string, number>;
  resource_usage: ResourceUsage;
  success_rate: number;
  error_count: number;
}

interface ToolStatus {
  name: string;
  version: string;
  status: 'running' | 'idle' | 'error' | 'unavailable';
  last_execution: Date;
  metrics: ToolMetrics;
}
```

## API接口规范

### 工作流管理API
```typescript
// 创建工作流
POST /api/workflows
Request: {
  name: string;
  description?: string;
  stages: WorkflowStage[];
}
Response: {
  id: string;
  status: 'created';
  workflow: WorkflowDefinition;
}

// 执行工作流
POST /api/workflows/:id/execute
Request: {
  parameters?: Record<string, any>;
}
Response: {
  execution_id: string;
  status: 'started';
  estimated_duration?: number;
}

// 获取执行状态
GET /api/workflows/executions/:executionId
Response: {
  execution: WorkflowExecution;
  current_stage?: StageExecution;
  progress: {
    completed_stages: number;
    total_stages: number;
    percentage: number;
  };
}

// 停止执行
POST /api/workflows/executions/:executionId/stop
Response: {
  status: 'stopping' | 'stopped';
  message: string;
}
```

### 监控API
```typescript
// 获取系统状态
GET /api/monitoring/system
Response: {
  status: 'healthy' | 'warning' | 'error';
  metrics: SystemMetrics;
  tools: ToolStatus[];
  active_executions: number;
}

// 获取执行指标
GET /api/monitoring/executions/:executionId/metrics
Response: {
  metrics: ExecutionMetrics;
  stages: Record<string, StageMetrics>;
  real_time: boolean;
}

// 获取日志
GET /api/monitoring/logs?level=info&limit=100&offset=0
Response: {
  logs: LogEntry[];
  total: number;
  has_more: boolean;
}
```

## 部署和运维

### 开发环境部署
```bash
# 1. 克隆项目
git clone <repository-url>
cd proxy-workflow-dashboard

# 2. 安装依赖
npm run setup

# 3. 启动开发环境
npm run dev

# 4. 访问应用
# 前端: http://localhost:3000
# 后端: http://localhost:3001
```

### 生产环境部署
```bash
# 1. 构建应用
npm run build

# 2. 使用Docker部署
npm run docker:build
npm run docker:up

# 3. 配置反向代理 (Nginx)
# 配置SSL证书
# 配置域名解析
```

### 监控和维护
- **日志管理**: 使用Winston进行结构化日志
- **性能监控**: 集成Prometheus指标
- **错误追踪**: 集成错误追踪服务
- **数据备份**: 自动化数据库备份

这个平台将成为整个SOCKS5代理工具生态系统的核心，提供统一、直观、高效的管理体验。