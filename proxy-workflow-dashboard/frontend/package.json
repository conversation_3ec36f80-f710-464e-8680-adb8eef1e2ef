{"name": "proxy-workflow-frontend", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "antd": "^5.2.1", "echarts": "^5.4.1", "echarts-for-react": "^3.0.2", "@tanstack/react-query": "^4.24.6", "axios": "^1.3.3", "socket.io-client": "^4.6.1", "dayjs": "^1.11.7", "lodash": "^4.17.21", "react-beautiful-dnd": "^13.1.1", "react-flow-renderer": "^10.3.17", "zustand": "^4.3.6"}, "devDependencies": {"@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "@types/lodash": "^4.14.191", "@types/react-beautiful-dnd": "^13.1.4", "@typescript-eslint/eslint-plugin": "^5.52.0", "@typescript-eslint/parser": "^5.52.0", "@vitejs/plugin-react": "^3.1.0", "eslint": "^8.34.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "typescript": "^4.9.5", "vite": "^4.1.1", "vitest": "^0.28.4", "@vitest/ui": "^0.28.4"}}