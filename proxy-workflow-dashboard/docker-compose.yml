version: '3.8'

services:
  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:3001
      - REACT_APP_WS_URL=ws://localhost:3001
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - proxy-network

  # 后端API服务
  backend:
    build:
      context: ./api
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - PORT=3001
      - DATABASE_URL=sqlite:./data/workflow.db
      - TOOLS_DATA_PATH=/app/data
      - LOG_LEVEL=info
    volumes:
      - ./api:/app
      - ./data:/app/data
      - /app/node_modules
    networks:
      - proxy-network
    depends_on:
      - proxy-manager

  # main-18889 扫描工具服务
  scan-service:
    build:
      context: ../main-18889_副本
      dockerfile: Dockerfile
    environment:
      - CONFIG_PATH=/app/data/scan_config.ini
      - OUTPUT_PATH=/app/data
    volumes:
      - ./data:/app/data
      - ../main-18889_副本:/app/source:ro
    networks:
      - proxy-network
    command: ["sleep", "infinity"]  # 保持容器运行，等待调用

  # 2to3 18889 验证工具服务
  validate-service:
    build:
      context: ../2to3\ 18889_副本
      dockerfile: Dockerfile
    environment:
      - CONFIG_PATH=/app/data/validate_config.json
      - INPUT_PATH=/app/data
      - OUTPUT_PATH=/app/data
    volumes:
      - ./data:/app/data
      - ../2to3\ 18889_副本:/app/source:ro
    networks:
      - proxy-network
    command: ["sleep", "infinity"]  # 保持容器运行，等待调用

  # 2to3 rebuild 管理工具服务
  proxy-manager:
    build:
      context: ../2to3\ rebuild_副本
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - CONFIG_PATH=/app/data/manager_config.json
      - DATABASE_PATH=/app/data/proxies.db
      - API_PORT=8080
    volumes:
      - ./data:/app/data
      - ../2to3\ rebuild_副本:/app/source:ro
    networks:
      - proxy-network

  # Redis缓存服务 (可选)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - proxy-network
    command: redis-server --appendonly yes

  # 文件共享服务 (用于工具间数据交换)
  file-server:
    image: nginx:alpine
    ports:
      - "8081:80"
    volumes:
      - ./data:/usr/share/nginx/html:ro
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    networks:
      - proxy-network
    depends_on:
      - backend

volumes:
  redis_data:
  workflow_data:

networks:
  proxy-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16