{"name": "proxy-workflow-dashboard", "version": "0.1.0", "description": "SOCKS5代理工作流管理平台", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd api && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd api && npm run build", "start": "cd api && npm start", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm test", "test:backend": "cd api && npm test", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "setup": "npm run setup:frontend && npm run setup:backend", "setup:frontend": "cd frontend && npm install", "setup:backend": "cd api && npm install"}, "keywords": ["socks5", "proxy", "workflow", "dashboard", "automation"], "author": "SOCKS5 Proxy Tools Team", "license": "MIT", "devDependencies": {"concurrently": "^7.6.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/proxy-tools/workflow-dashboard.git"}, "bugs": {"url": "https://github.com/proxy-tools/workflow-dashboard/issues"}, "homepage": "https://github.com/proxy-tools/workflow-dashboard#readme"}