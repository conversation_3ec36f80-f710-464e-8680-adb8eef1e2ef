{"name": "proxy-workflow-api", "version": "0.1.0", "description": "SOCKS5代理工作流管理平台后端API", "main": "dist/index.js", "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "vitest", "test:coverage": "vitest --coverage", "lint": "eslint . --ext .ts --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext .ts --fix", "db:migrate": "node scripts/migrate.js", "db:seed": "node scripts/seed.js"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.6.1", "sqlite3": "^5.1.4", "cors": "^2.8.5", "helmet": "^6.0.1", "morgan": "^1.10.0", "winston": "^3.8.2", "joi": "^17.7.0", "bcrypt": "^5.1.0", "jsonwebtoken": "^9.0.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.2", "ws": "^8.12.1", "uuid": "^9.0.0", "axios": "^1.3.3", "child_process": "^1.0.2"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/morgan": "^1.9.4", "@types/bcrypt": "^5.0.0", "@types/jsonwebtoken": "^9.0.1", "@types/multer": "^1.4.7", "@types/node": "^18.14.0", "@types/uuid": "^9.0.0", "@types/ws": "^8.5.4", "@typescript-eslint/eslint-plugin": "^5.52.0", "@typescript-eslint/parser": "^5.52.0", "eslint": "^8.34.0", "tsx": "^3.12.3", "typescript": "^4.9.5", "vitest": "^0.28.4", "@vitest/coverage-c8": "^0.28.4"}, "keywords": ["socks5", "proxy", "workflow", "api", "automation"], "author": "SOCKS5 Proxy Tools Team", "license": "MIT"}