# SOCKS5代理工作流管理平台

**项目定位**: 前端工作流编排平台  
**技术栈**: React + Node.js + TypeScript  
**复杂度**: 中高  
**维护性**: 高  

## 项目概述

SOCKS5代理工作流管理平台是一个现代化的Web应用，用于统一管理和编排三个SOCKS5代理工具的完整工作流程。该平台提供直观的用户界面、自动化工作流编排、实时监控和数据可视化功能，使代理管理变得简单高效。

## 核心功能

### 工作流编排
- **可视化流程设计**: 拖拽式工作流设计器
- **自动化执行**: 一键启动完整的扫描-验证-管理流程
- **条件分支**: 基于结果的智能流程分支
- **错误处理**: 完善的错误处理和重试机制

### 统一管理界面
- **工具集成**: 统一管理三个SOCKS5工具
- **参数配置**: 可视化的参数配置界面
- **批量操作**: 支持批量导入、测试和导出
- **历史记录**: 完整的操作历史和审计日志

### 实时监控
- **进度跟踪**: 实时显示工作流执行进度
- **状态监控**: 各工具运行状态实时监控
- **性能指标**: 扫描速度、成功率等关键指标
- **告警通知**: 异常情况及时告警

### 数据可视化
- **统计图表**: 丰富的数据统计和图表展示
- **地理分布**: 代理地理位置分布图
- **趋势分析**: 代理质量变化趋势
- **报表导出**: 支持多种格式的报表导出

## 项目架构

### 总体架构
```
┌─────────────────────────────────────────┐
│              前端层 (React)              │
├─────────────────────────────────────────┤
│           API网关 (Express)             │
├─────────────────────────────────────────┤
│          工作流引擎 (Node.js)            │
├─────────────────────────────────────────┤
│              工具适配器                   │
├─────────────────────────────────────────┤
│    main-18889  │ 2to3-18889 │ 2to3-rebuild │
└─────────────────────────────────────────┘
```

### 前端架构
```
src/
├── components/          # React组件
│   ├── common/         # 通用组件
│   ├── workflow/       # 工作流组件
│   ├── monitoring/     # 监控组件
│   └── visualization/  # 可视化组件
├── pages/              # 页面组件
│   ├── Dashboard/      # 仪表板
│   ├── Workflow/       # 工作流管理
│   ├── Tools/          # 工具管理
│   └── Reports/        # 报表页面
├── services/           # API服务
├── stores/             # 状态管理
├── utils/              # 工具函数
└── types/              # TypeScript类型定义
```

### 后端架构
```
api/
├── controllers/        # 控制器
│   ├── workflow.js     # 工作流控制
│   ├── tools.js        # 工具管理
│   └── monitoring.js   # 监控接口
├── services/           # 业务服务
│   ├── WorkflowEngine.js    # 工作流引擎
│   ├── ToolAdapter.js       # 工具适配器
│   └── DataProcessor.js     # 数据处理
├── middleware/         # 中间件
├── routes/             # 路由定义
└── config/             # 配置文件
```

## 技术栈详情

### 前端技术栈
- **React 18**: 用户界面框架
- **TypeScript**: 类型安全的JavaScript
- **Ant Design**: UI组件库
- **ECharts**: 数据可视化图表
- **React Query**: 数据获取和状态管理
- **React Router**: 单页应用路由
- **Vite**: 现代化构建工具

### 后端技术栈
- **Node.js**: 服务器运行时
- **Express.js**: Web应用框架
- **Socket.io**: 实时通信
- **sqlite3**: 轻量级数据库
- **Winston**: 日志管理
- **Joi**: 数据验证
- **Docker**: 容器化部署

## 工作流引擎设计

### 工作流定义
```javascript
const proxyWorkflow = {
  name: "完整代理发现与验证流程",
  version: "1.0",
  stages: [
    {
      id: "scan",
      name: "代理扫描",
      tool: "main-18889",
      config: {
        cidrs: ["***********/24"],
        ports: "7890-7893,10810",
        threads: 20
      },
      outputs: ["scan_results.txt"]
    },
    {
      id: "validate", 
      name: "代理验证",
      tool: "2to3-18889",
      depends: ["scan"],
      config: {
        input: "${scan.outputs[0]}",
        timeout: 5,
        threads: 10,
        output_formats: ["3", "30", "noauth"]
      },
      outputs: ["original.txt", "delay.txt", "noauth.txt"]
    },
    {
      id: "manage",
      name: "数据管理", 
      tool: "2to3-rebuild",
      depends: ["validate"],
      config: {
        api_port: 8080,
        import_files: "${validate.outputs}"
      },
      outputs: ["database_updated"]
    }
  ],
  pipeline: "scan -> validate -> manage"
}
```

### 执行引擎
```javascript
class WorkflowEngine {
  async execute(workflow) {
    const context = new ExecutionContext();
    
    for (const stage of workflow.stages) {
      try {
        // 检查依赖
        await this.checkDependencies(stage, context);
        
        // 执行阶段
        const result = await this.executeStage(stage, context);
        
        // 保存结果
        context.setStageResult(stage.id, result);
        
        // 发送进度更新
        this.emitProgress(stage.id, 'completed', result);
        
      } catch (error) {
        this.handleStageError(stage, error);
        throw error;
      }
    }
    
    return context.getResults();
  }
}
```

## API接口设计

### 工作流管理API

```typescript
// 创建工作流
POST /api/workflows
{
  name: string;
  description?: string;
  stages: WorkflowStage[];
}

// 执行工作流
POST /api/workflows/:id/execute
{
  parameters?: Record<string, any>;
}

// 获取执行状态
GET /api/workflows/:id/executions/:executionId/status

// 停止执行
POST /api/workflows/:id/executions/:executionId/stop
```

### 工具管理API

```typescript
// 获取工具列表
GET /api/tools

// 获取工具配置
GET /api/tools/:toolId/config

// 更新工具配置
PUT /api/tools/:toolId/config

// 执行单个工具
POST /api/tools/:toolId/execute
```

### 监控API

```typescript
// 获取系统状态
GET /api/monitoring/status

// 获取性能指标
GET /api/monitoring/metrics

// 获取执行日志
GET /api/monitoring/logs?limit=100&level=info
```

## 数据模型

### 工作流模型
```typescript
interface Workflow {
  id: string;
  name: string;
  description?: string;
  version: string;
  stages: WorkflowStage[];
  created_at: Date;
  updated_at: Date;
}

interface WorkflowStage {
  id: string;
  name: string;
  tool: string;
  depends?: string[];
  config: Record<string, any>;
  timeout?: number;
  retry?: number;
}
```

### 执行模型
```typescript
interface WorkflowExecution {
  id: string;
  workflow_id: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  start_time: Date;
  end_time?: Date;
  stages: StageExecution[];
  logs: ExecutionLog[];
}

interface StageExecution {
  stage_id: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  start_time?: Date;
  end_time?: Date;
  result?: any;
  error?: string;
}
```

## 用户界面设计

### 主要页面

1. **仪表板 (Dashboard)**
   - 系统概览
   - 最近执行状态
   - 关键指标展示
   - 快速操作面板

2. **工作流管理 (Workflow)**
   - 工作流列表
   - 可视化设计器
   - 执行历史
   - 模板管理

3. **工具管理 (Tools)**
   - 工具状态监控
   - 配置管理
   - 日志查看
   - 性能指标

4. **数据分析 (Analytics)**
   - 代理统计
   - 地理分布
   - 质量趋势
   - 自定义报表

## 部署方案

### Docker Compose部署
```yaml
version: '3.8'
services:
  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:3001
      
  backend:
    build: ./api
    ports:
      - "3001:3001"
    environment:
      - DATABASE_URL=sqlite:./data/workflow.db
    volumes:
      - ./data:/app/data
      
  main-18889:
    build: ../main-18889_副本
    volumes:
      - ./data:/app/data
      
  proxy-tester:
    build: ../2to3\ 18889_副本
    volumes:
      - ./data:/app/data
      
  proxy-manager:
    build: ../2to3\ rebuild_副本
    ports:
      - "8080:8080"
    volumes:
      - ./data:/app/data
```

## 开发计划

### 第一阶段 (MVP - 2-3周)
- [x] 项目基础架构搭建
- [ ] 基础工作流引擎
- [ ] 简单的Web界面
- [ ] 工具适配器实现
- [ ] 基础API接口

### 第二阶段 (功能完善 - 3-4周)
- [ ] 可视化工作流设计器
- [ ] 实时监控和日志
- [ ] 数据可视化图表
- [ ] 用户权限管理
- [ ] 错误处理优化

### 第三阶段 (高级功能 - 4-6周)
- [ ] 工作流模板系统
- [ ] 高级数据分析
- [ ] 告警和通知
- [ ] 性能优化
- [ ] 文档和测试

## 项目状态

- **当前状态**: 架构设计完成，准备开始开发
- **技术选型**: 已确定
- **开发环境**: 需要搭建
- **团队准备**: 需要确认开发资源

这个平台将成为整个SOCKS5代理工具生态系统的统一入口，大大提升工具的易用性和管理效率。