#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
通过API导入代理工具

这个脚本用于将可能可用的代理从文本文件通过API导入到数据库中。
这样可以测试API的导入功能是否完善。

用法:
    python import_proxies_via_api.py [选项]

选项:
    --file FILE         指定代理文件路径，默认为 ip_output-18889.txt
    --api-port PORT     API服务器端口，默认为 8080
    --source SOURCE     指定代理来源，默认为 file_import
    --batch-size SIZE   每批导入的代理数量，默认为 20
    --limit LIMIT       限制导入数量，默认为全部导入
    --help              显示帮助信息
"""

import argparse
import json
import os
import re
import sys
import time
import urllib.request
import urllib.error
import urllib.parse


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="通过API导入代理工具")
    parser.add_argument("--file", default="ip_output-18889.txt", help="代理文件路径")
    parser.add_argument("--api-port", type=int, default=8080, help="API服务器端口")
    parser.add_argument("--source", default="file_import", help="代理来源")
    parser.add_argument("--batch-size", type=int, default=20, help="每批导入的代理数量")
    parser.add_argument("--limit", type=int, help="限制导入数量")
    return parser.parse_args()


def parse_proxy_file(file_path, limit=None):
    """解析代理文件"""
    if not os.path.exists(file_path):
        print(f"错误: 文件 {file_path} 不存在")
        sys.exit(1)
    
    proxies = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for i, line in enumerate(f):
                if limit and i >= limit:
                    break
                
                parts = line.strip().split(' ', 2)
                if len(parts) >= 2:
                    ip = parts[0]
                    port = parts[1]
                    location = parts[2] if len(parts) > 2 else ""
                    
                    # 验证IP格式
                    if re.match(r'^(\d{1,3}\.){3}\d{1,3}$', ip) and port.isdigit():
                        proxies.append({
                            'ip': ip,
                            'port': port,
                            'location': location
                        })
    except Exception as e:
        print(f"解析文件错误: {e}")
        sys.exit(1)
    
    return proxies


def import_proxies_via_api(proxies, api_port, source, batch_size):
    """通过API导入代理"""
    url = f"http://localhost:{api_port}/api/proxies"
    
    total_count = len(proxies)
    success_count = 0
    error_count = 0
    batch_count = 0
    
    # 分批导入
    for i in range(0, total_count, batch_size):
        batch = proxies[i:i+batch_size]
        batch_count += 1
        print(f"导入批次 {batch_count}/{(total_count + batch_size - 1) // batch_size}，共 {len(batch)} 个代理")
        
        # 构建请求数据
        request_data = {
            "proxies": []
        }
        
        for proxy in batch:
            request_data["proxies"].append({
                "ip": proxy["ip"],
                "port": proxy["port"],
                "source": source,
                "location": proxy["location"]
            })
        
        try:
            # 发送POST请求
            req = urllib.request.Request(
                url,
                data=json.dumps(request_data).encode('utf-8'),
                headers={'Content-Type': 'application/json'},
                method='POST'
            )
            
            with urllib.request.urlopen(req, timeout=30) as response:
                result = json.loads(response.read().decode('utf-8'))
                
                if result.get("success"):
                    batch_success = result.get("saved", 0)
                    batch_error = result.get("failed", 0)
                    
                    success_count += batch_success
                    error_count += batch_error
                    
                    print(f"批次导入结果: 成功 {batch_success} 个, 失败 {batch_error} 个")
                else:
                    print(f"批次导入失败: {result.get('message', '未知错误')}")
                    error_count += len(batch)
        
        except Exception as e:
            print(f"API请求失败: {e}")
            error_count += len(batch)
        
        # 避免请求过快
        if i + batch_size < total_count:
            print("等待1秒后继续下一批...")
            time.sleep(1)
    
    return success_count, error_count


def main():
    """主函数"""
    args = parse_args()
    
    # 解析代理文件
    proxies = parse_proxy_file(args.file, args.limit)
    print(f"从文件 {args.file} 中解析出 {len(proxies)} 个代理")
    
    if not proxies:
        print("没有找到有效的代理，退出")
        return
    
    # 通过API导入代理
    print(f"开始通过API导入代理到 http://localhost:{args.api_port}/api/proxies")
    success_count, error_count = import_proxies_via_api(
        proxies, args.api_port, args.source, args.batch_size
    )
    
    print(f"\n导入完成: 共 {len(proxies)} 个代理，成功 {success_count} 个，失败 {error_count} 个")
    print("如需测试这些代理，请运行: python test_potential_proxies.py")


if __name__ == "__main__":
    main()
