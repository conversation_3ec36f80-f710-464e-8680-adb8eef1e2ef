#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
可用代理导入工具

这个脚本用于将可用代理从文本文件直接导入到数据库中，
不需要API服务器。

用法:
    python import_working_proxies.py [选项]

选项:
    --file FILE         指定代理文件路径，默认为 working_proxies.txt
    --db DB             指定数据库文件路径，默认为 proxies.db
    --format FORMAT     指定文件格式，支持 basic(默认), url, delay
    --status STATUS     指定代理状态，默认为 可用
    --latency LATENCY   指定默认延迟，默认为 100
    --limit LIMIT       限制导入数量，默认为全部导入
    --help              显示帮助信息

文件格式:
    basic: IP PORT [USERNAME PASSWORD]
    url: socks5://[USERNAME:PASSWORD@]IP:PORT
    delay: IP:PORT 延迟:XXXms
"""

import argparse
import datetime
import os
import re
import sqlite3
import sys


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="可用代理导入工具")
    parser.add_argument("--file", default="working_proxies.txt", help="代理文件路径")
    parser.add_argument("--db", default="proxies.db", help="数据库文件路径")
    parser.add_argument("--format", default="basic", choices=["basic", "url", "delay"], help="文件格式")
    parser.add_argument("--status", default="可用", help="代理状态")
    parser.add_argument("--latency", type=int, default=100, help="默认延迟")
    parser.add_argument("--limit", type=int, help="限制导入数量")
    return parser.parse_args()


def connect_db(db_path):
    """连接到SQLite数据库"""
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        return conn
    except sqlite3.Error as e:
        print(f"数据库连接错误: {e}")
        sys.exit(1)


def ensure_tables_exist(conn):
    """确保必要的表存在"""
    cursor = conn.cursor()
    
    # 检查proxies表是否存在
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='proxies'")
    if not cursor.fetchone():
        print("创建proxies表...")
        cursor.execute('''
        CREATE TABLE proxies (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            ip VARCHAR(50) NOT NULL,
            port VARCHAR(10) NOT NULL,
            username VARCHAR(50),
            password VARCHAR(50),
            auth_type VARCHAR(20),
            auth_required BOOLEAN DEFAULT false,
            status VARCHAR(20),
            last_check TIMESTAMP,
            latency INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(ip, port)
        )
        ''')
    
    conn.commit()


def parse_proxy_file(file_path, format_type, limit=None):
    """解析代理文件"""
    if not os.path.exists(file_path):
        print(f"错误: 文件 {file_path} 不存在")
        sys.exit(1)
    
    proxies = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for i, line in enumerate(f):
                if limit and i >= limit:
                    break
                
                line = line.strip()
                if not line:
                    continue
                
                proxy = {}
                
                if format_type == "basic":
                    # 基本格式: IP PORT [USERNAME PASSWORD]
                    parts = line.split()
                    if len(parts) >= 2:
                        proxy["ip"] = parts[0]
                        proxy["port"] = parts[1]
                        if len(parts) >= 4:
                            proxy["username"] = parts[2]
                            proxy["password"] = parts[3]
                            proxy["auth_required"] = True
                        else:
                            proxy["auth_required"] = False
                
                elif format_type == "url":
                    # URL格式: socks5://[USERNAME:PASSWORD@]IP:PORT
                    match = re.match(r'socks5://(?:([^:]+):([^@]+)@)?([^:]+):(\d+)', line)
                    if match:
                        username, password, ip, port = match.groups()
                        proxy["ip"] = ip
                        proxy["port"] = port
                        if username and password:
                            proxy["username"] = username
                            proxy["password"] = password
                            proxy["auth_required"] = True
                        else:
                            proxy["auth_required"] = False
                
                elif format_type == "delay":
                    # 带延迟格式: IP:PORT 延迟:XXXms
                    match = re.match(r'([^:]+):(\d+)\s+延迟:(\d+)ms', line)
                    if match:
                        ip, port, latency = match.groups()
                        proxy["ip"] = ip
                        proxy["port"] = port
                        proxy["latency"] = int(latency)
                        proxy["auth_required"] = False
                
                # 验证IP格式
                if proxy.get("ip") and re.match(r'^(\d{1,3}\.){3}\d{1,3}$', proxy["ip"]) and proxy.get("port"):
                    proxies.append(proxy)
    
    except Exception as e:
        print(f"解析文件错误: {e}")
        sys.exit(1)
    
    return proxies


def import_working_proxies(conn, proxies, status, default_latency):
    """导入可用代理到数据库"""
    cursor = conn.cursor()
    now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    success_count = 0
    error_count = 0
    
    for proxy in proxies:
        try:
            auth_required = proxy.get("auth_required", False)
            auth_type = "auth" if auth_required else "noauth"
            latency = proxy.get("latency", default_latency)
            
            cursor.execute('''
            INSERT OR REPLACE INTO proxies 
            (ip, port, username, password, auth_type, auth_required, status, last_check, latency) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                proxy["ip"],
                proxy["port"],
                proxy.get("username", ""),
                proxy.get("password", ""),
                auth_type,
                auth_required,
                status,
                now,
                latency
            ))
            
            if cursor.rowcount > 0:
                success_count += 1
            else:
                print(f"代理已存在: {proxy['ip']}:{proxy['port']}")
        except sqlite3.Error as e:
            print(f"导入代理 {proxy['ip']}:{proxy['port']} 失败: {e}")
            error_count += 1
    
    conn.commit()
    return success_count, error_count


def main():
    """主函数"""
    args = parse_args()
    
    # 连接数据库
    conn = connect_db(args.db)
    ensure_tables_exist(conn)
    
    # 解析代理文件
    proxies = parse_proxy_file(args.file, args.format, args.limit)
    print(f"从文件 {args.file} 中解析出 {len(proxies)} 个代理")
    
    # 导入可用代理
    success_count, error_count = import_working_proxies(conn, proxies, args.status, args.latency)
    print(f"成功导入 {success_count} 个代理，失败 {error_count} 个")
    
    conn.close()
    print("操作完成")


if __name__ == "__main__":
    main()
