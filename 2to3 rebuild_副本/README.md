# SOCKS5代理测试与管理平台

**项目定位**: 企业级代理管理平台  
**版本**: v1.0.0  
**语言**: Go  
**复杂度**: 高  
**维护性**: 中  
**最后更新**: 2025-05-15  
**文档状态**: 稳定  

## 项目概述

SOCKS5代理测试与管理平台是一个功能完整的企业级代理管理解决方案，作为代理管理工作流的第三环节。本平台提供SQLite数据库存储、RESTful API接口、交互式命令行界面等功能，支持代理的持久化管理、批量操作和数据分析。

## 核心功能

- **数据库管理**: SQLite数据库存储代理信息、测试结果和认证数据
- **RESTful API**: 完整的API接口支持，便于集成和自动化
- **交互式界面**: 用户友好的命令行菜单系统
- **批量操作**: 支持批量导入、测试和导出代理
- **认证管理**: 灵活的认证信息管理和测试
- **数据持久化**: 完整的数据生命周期管理

## 文档导航

- **[README.md](README.md)** - 项目概述和快速入门指南
- **[docs/](docs/)** - 详细文档目录
  - **[项目文档.md](docs/项目文档.md)** - 详细的项目架构和技术细节
  - **[使用指南.md](docs/使用指南.md)** - 具体的使用方法和常见问题解答
  - **[开发指南.md](docs/开发指南.md)** - 开发环境设置、编码规范和数据库管理
  - **[progress_tracking.md](docs/progress_tracking.md)** - 项目开发进度和任务分解
  - **[sqlite_commands.md](docs/sqlite_commands.md)** - 数据库管理SQL命令参考
  - **[go文件行数统计.md](docs/go文件行数统计.md)** - 代码行数统计和分析
  - **[work_log.md](docs/work_log.md)** - 项目工作日志
  - **[项目结构分析.md](docs/项目结构分析.md)** - 项目结构分析和优化建议

## 项目结构

```
.
├── cmd/                    # 命令行工具
│   ├── import_credentials/ # 导入认证信息工具
│   │   └── main.go
│   ├── socks5tester/       # 主命令行工具
│   │   ├── flags.go
│   │   ├── main.go
│   │   └── test_tools/     # 测试工具
│   │       ├── main.go
│   │       ├── test_all_proxies.go
│   │       └── test_single_proxy.go
│   └── test_tools/         # 测试工具
│       ├── main.go
│       ├── test_all_proxies.go
│       └── test_single_proxy.go
├── internal/               # 内部包
│   ├── app/                # 应用程序核心
│   │   ├── adapters/       # 适配器
│   │   ├── core/           # 核心逻辑
│   │   └── types/          # 类型定义
│   ├── config/             # 配置相关
│   ├── database/           # 数据库抽象和实现
│   │   ├── migrations/     # 数据库迁移
│   │   └── sqlite/         # SQLite实现
│   ├── proxy/              # 代理测试核心逻辑
│   └── ui/                 # 交互式用户界面
│       ├── menu/           # 菜单实现
│       └── types/          # 类型定义
├── pkg/                    # 公共包
│   ├── errors/             # 错误处理
│   ├── logger/             # 日志处理
│   └── metrics/            # 性能指标
├── docs/                   # 文档
├── test/                   # 测试相关
│   ├── fixtures/           # 测试数据
│   └── unit/               # 单元测试
├── tools/                  # 工具
│   ├── scripts/            # 脚本
│   └── build/              # 构建工具
├── config.json             # 配置文件
├── main.go                 # 程序入口
└── proxies.db              # SQLite数据库
```

## 主要功能

1. **代理测试** - 无认证/带认证测试、超时重试、并发控制
2. **命令行工具** - 参数解析、配置管理、格式化输出
3. **数据管理** - SQLite存储、测试结果记录、数据导入导出
4. **用户界面** - 交互式菜单、数据库管理、配置设置

## 快速开始

### 安装

```bash
# 克隆仓库
git clone https://github.com/yourusername/socks5-proxy-tester.git
cd socks5-proxy-tester

# 编译
go build -o socks5tester.exe
```

### 使用方法

```bash
# 启动交互式菜单
socks5tester -i

# 测试单个代理
socks5tester -ip *********** -port 1080

# 使用自定义配置和数据库
socks5tester -config my-config.json -db my-proxies.db -i
```

### 标准配置文件

```json
{
  "database": {
    "path": "proxies.db"
  },
  "proxy": {
    "timeout_seconds": 5,
    "test_targets": [
      "ipinfo.io:80",
      "google.com:80"
    ],
    "credentials": [
      {
        "username": "user1",
        "password": "pass1"
      }
    ]
  }
}
```

## 技术栈

- 语言：Go 1.19+
- 数据库：SQLite 3
- 主要依赖：golang.org/x/net/proxy, database/sql, modernc.org/sqlite, github.com/AlecAivazis/survey/v2

## 技术特点

- **分层架构设计**: 采用清晰的分层架构，便于维护和扩展
- **模块化组件**: 高内聚低耦合的模块设计
- **适配器模式**: 灵活的适配器模式实现组件间通信
- **API服务集成**: 内置HTTP API服务器支持

## 与工作流集成

本平台在SOCKS5代理管理工作流中的位置：

```
1. 扫描发现 (main-18889工具)
   ↓ IP:PORT列表
2. 代理验证 (2to3 18889工具)
   ↓ 验证结果
3. 数据管理 (本平台) ← 当前环节
```

### 数据接收和处理

- **接收验证结果**: 通过API或文件导入经过验证的代理
- **持久化存储**: 将代理信息存储到SQLite数据库
- **生命周期管理**: 提供完整的代理数据管理功能

## 最近更新

### v1.0.0 (当前版本)
- ✅ 修复重复main函数编译错误
- ✅ 解决导入路径错误问题  
- ✅ 移除有问题的测试文件
- ✅ 完善项目定位和文档
- ✅ 确保项目可正常编译运行
- ✅ 优化项目结构和模块化设计

## 已修复的关键问题

1. **编译问题解决**：
   - 重命名重复的main函数文件避免冲突
   - 移除有导入错误的测试文件
   - 确保所有依赖正确下载和配置

2. **项目结构优化**：
   - 清理无效的测试引用
   - 简化模块依赖关系
   - 保持核心功能完整性

## 临时工具脚本

以下Python脚本是临时解决方案，用于在Go项目中实现相应功能之前使用。这些脚本将在Go功能完成后移除。

> **警告**：这些脚本需要在API服务器运行时使用。请确保先启动API服务器：`socks5tester -api 8080`

### import_proxies.py - 直接导入代理到数据库

将可能可用的代理从文本文件直接导入到数据库中，不使用API。

**参数**：
- `--file FILE`：指定代理文件路径，默认为 `ip_output-18889.txt`
- `--db DB`：指定数据库文件路径，默认为 `proxies.db`
- `--source SOURCE`：指定代理来源，默认为 `file_import`
- `--limit LIMIT`：限制导入数量，默认为全部导入

**使用示例**：
```bash
python import_proxies.py --file ip_output-18889.txt --db proxies.db
```

**输出示例**：
```
从文件 ip_output-18889.txt 中解析出 100 个代理
成功导入 95 个代理，失败 5 个
操作完成
如需测试这些代理，请运行: python test_potential_proxies.py --db proxies.db
```

### import_proxies_via_api.py - 通过API导入代理

将可能可用的代理从文本文件通过API导入到数据库中，可以测试API的导入功能是否完善。

**参数**：
- `--file FILE`：指定代理文件路径，默认为 `ip_output-18889.txt`
- `--api-port PORT`：API服务器端口，默认为 `8080`
- `--source SOURCE`：指定代理来源，默认为 `file_import`
- `--batch-size SIZE`：每批导入的代理数量，默认为 `20`
- `--limit LIMIT`：限制导入数量，默认为全部导入

**使用示例**：
```bash
python import_proxies_via_api.py --file ip_output-18889.txt --api-port 8080 --batch-size 20
```

**输出示例**：
```
从文件 ip_output-18889.txt 中解析出 100 个代理
开始通过API导入代理到 http://localhost:8080/api/proxies
导入批次 1/5，共 20 个代理
批次导入结果: 成功 20 个, 失败 0 个
等待1秒后继续下一批...
...
导入完成: 共 100 个代理，成功 98 个，失败 2 个
如需测试这些代理，请运行: python test_potential_proxies.py
```

### test_potential_proxies.py - 测试未测试的代理

测试数据库中未测试的可能可用代理，并将可用的代理保存到代理表中。

**参数**：
- `--db DB`：指定数据库文件路径，默认为 `proxies.db`
- `--api-port PORT`：API服务器端口，默认为 `8080`
- `--batch-size SIZE`：每批测试的代理数量，默认为 `10`
- `--concurrent NUM`：并发测试数量，默认为 `5`
- `--limit LIMIT`：限制测试数量，默认为全部测试

**使用示例**：
```bash
python test_potential_proxies.py --db proxies.db --batch-size 10 --concurrent 5
```

**输出示例**：
```
找到 50 个未测试的代理
开始测试代理...

测试批次 1/5，共 10 个代理
测试代理: ***********:1080
测试代理: 192.168.1.2:8080
...
代理 ***********:1080 可用，延迟: 100ms
批次完成: 可用 3 个, 不可用 7 个
等待5秒后继续下一批...
...
测试完成: 共测试 50 个代理，可用 12 个，不可用 38 个
操作完成
```
