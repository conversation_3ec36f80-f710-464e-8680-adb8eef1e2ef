# 测试目录

本目录包含SOCKS5代理测试工具的测试相关文件。

## 目录结构

- **fixtures/** - 测试数据
  - **proxies/** - 代理测试数据
  - **auth/** - 认证测试数据
  
- **unit/** - 单元测试
  - 各模块的单元测试文件

## 测试数据

测试数据用于单元测试和集成测试。这些数据不应包含敏感信息，仅用于测试目的。

### 代理测试数据

代理测试数据包含用于测试的代理列表，格式为：

```
IP PORT [USERNAME PASSWORD]
```

### 认证测试数据

认证测试数据包含用于测试的认证信息，格式为：

```
USERNAME PASSWORD
```

## 运行测试

### 运行所有测试

```bash
go test ./...
```

### 运行特定包的测试

```bash
go test ./internal/proxy
```

### 运行特定测试

```bash
go test ./internal/proxy -run TestProxyNoAuth
```

### 运行性能测试

```bash
go test ./internal/proxy -bench=.
```

## 测试覆盖率

```bash
go test ./... -cover
```

生成HTML格式的覆盖率报告：

```bash
go test ./... -coverprofile=coverage.out
go tool cover -html=coverage.out
```
