package main

import (
	"testing"
	"time"

	"github.com/2to3rebuild/internal/database"
	"github.com/2to3rebuild/internal/database/sqlite"
	_ "modernc.org/sqlite"
)

// TestSQLiteDB 测试SQLite数据库基本功能
func TestSQLiteDB(t *testing.T) {
	// 使用内存数据库进行测试
	db := sqlite.NewSQLiteDB(":memory:")
	err := db.Connect()
	if err != nil {
		t.Fatalf("连接数据库失败: %v", err)
	}
	defer db.Close()

	// 测试保存和获取代理
	t.Run("TestSaveAndGetProxy", func(t *testing.T) {
		// 创建测试代理
		proxy := &database.Proxy{
			IP:           "***********",
			Port:         "8080",
			Username:     "testuser",
			Password:     "testpass",
			AuthType:     "auth",
			AuthRequired: true,
			Status:       "可用",
			LastCheck:    time.Now(),
			Latency:      100,
			CreatedAt:    time.Now(),
		}

		// 保存代理
		err := db.SaveProxy(proxy)
		if err != nil {
			t.Fatalf("保存代理失败: %v", err)
		}

		// 获取代理
		savedProxy, err := db.GetProxy(proxy.IP, proxy.Port)
		if err != nil {
			t.Fatalf("获取代理失败: %v", err)
		}

		// 验证代理信息
		if savedProxy == nil {
			t.Fatal("获取的代理为空")
		}
		if savedProxy.IP != proxy.IP || savedProxy.Port != proxy.Port {
			t.Errorf("代理信息不匹配，期望 %s:%s，实际 %s:%s", 
				proxy.IP, proxy.Port, savedProxy.IP, savedProxy.Port)
		}
		if savedProxy.Username != proxy.Username || savedProxy.Password != proxy.Password {
			t.Errorf("代理认证信息不匹配，期望 %s:%s，实际 %s:%s", 
				proxy.Username, proxy.Password, savedProxy.Username, savedProxy.Password)
		}
		if savedProxy.Status != proxy.Status {
			t.Errorf("代理状态不匹配，期望 %s，实际 %s", proxy.Status, savedProxy.Status)
		}
	})

	// 测试更新代理状态
	t.Run("TestUpdateProxyStatus", func(t *testing.T) {
		ip := "***********"
		port := "8080"
		newStatus := "不可用"
		newLatency := int64(200)

		// 更新状态
		err := db.UpdateProxyStatus(ip, port, newStatus, newLatency)
		if err != nil {
			t.Fatalf("更新代理状态失败: %v", err)
		}

		// 获取更新后的代理
		updatedProxy, err := db.GetProxy(ip, port)
		if err != nil {
			t.Fatalf("获取代理失败: %v", err)
		}

		// 验证状态是否更新
		if updatedProxy.Status != newStatus {
			t.Errorf("代理状态未更新，期望 %s，实际 %s", newStatus, updatedProxy.Status)
		}
		if updatedProxy.Latency != newLatency {
			t.Errorf("代理延迟未更新，期望 %d，实际 %d", newLatency, updatedProxy.Latency)
		}
	})

	// 测试列出所有代理
	t.Run("TestListProxies", func(t *testing.T) {
		// 添加另一个代理
		proxy2 := &database.Proxy{
			IP:           "********",
			Port:         "1080",
			AuthType:     "noauth",
			AuthRequired: false,
			Status:       "可用",
			LastCheck:    time.Now(),
			Latency:      50,
			CreatedAt:    time.Now(),
		}
		err := db.SaveProxy(proxy2)
		if err != nil {
			t.Fatalf("保存第二个代理失败: %v", err)
		}

		// 列出所有代理
		proxies, err := db.ListProxies(nil)
		if err != nil {
			t.Fatalf("列出代理失败: %v", err)
		}

		// 验证代理数量
		if len(proxies) != 2 {
			t.Errorf("代理数量不匹配，期望 2，实际 %d", len(proxies))
		}
	})

	// 测试删除代理
	t.Run("TestDeleteProxy", func(t *testing.T) {
		ip := "***********"
		port := "8080"

		// 删除代理
		err := db.DeleteProxy(ip, port)
		if err != nil {
			t.Fatalf("删除代理失败: %v", err)
		}

		// 验证代理已删除
		deletedProxy, err := db.GetProxy(ip, port)
		if err != nil {
			t.Fatalf("获取代理失败: %v", err)
		}
		if deletedProxy != nil {
			t.Error("代理未被删除")
		}

		// 再次列出所有代理
		proxies, err := db.ListProxies(nil)
		if err != nil {
			t.Fatalf("列出代理失败: %v", err)
		}

		// 验证代理数量
		if len(proxies) != 1 {
			t.Errorf("代理数量不匹配，期望 1，实际 %d", len(proxies))
		}
	})

	// 测试认证信息操作
	t.Run("TestCredentialOperations", func(t *testing.T) {
		// 创建测试认证信息
		cred := &database.Credential{
			Username:  "testuser",
			Password:  "testpass",
			CreatedAt: time.Now(),
		}

		// 保存认证信息
		err := db.SaveCredential(cred)
		if err != nil {
			t.Fatalf("保存认证信息失败: %v", err)
		}

		// 获取所有认证信息
		creds, err := db.GetAllCredentials()
		if err != nil {
			t.Fatalf("获取认证信息失败: %v", err)
		}

		// 验证认证信息
		if len(creds) != 1 {
			t.Errorf("认证信息数量不匹配，期望 1，实际 %d", len(creds))
		}
		if creds[0].Username != cred.Username || creds[0].Password != cred.Password {
			t.Errorf("认证信息不匹配，期望 %s:%s，实际 %s:%s", 
				cred.Username, cred.Password, creds[0].Username, creds[0].Password)
		}

		// 删除认证信息
		err = db.DeleteCredential(cred.Username, cred.Password)
		if err != nil {
			t.Fatalf("删除认证信息失败: %v", err)
		}

		// 再次获取所有认证信息
		creds, err = db.GetAllCredentials()
		if err != nil {
			t.Fatalf("获取认证信息失败: %v", err)
		}

		// 验证认证信息已删除
		if len(creds) != 0 {
			t.Errorf("认证信息未被删除，剩余 %d 条", len(creds))
		}
	})

	// 测试事务处理
	t.Run("TestTransaction", func(t *testing.T) {
		// 开始事务
		tx, err := db.BeginTx()
		if err != nil {
			t.Fatalf("开始事务失败: %v", err)
		}

		// 提交事务
		err = tx.Commit()
		if err != nil {
			t.Fatalf("提交事务失败: %v", err)
		}

		// 开始另一个事务
		tx, err = db.BeginTx()
		if err != nil {
			t.Fatalf("开始事务失败: %v", err)
		}

		// 回滚事务
		err = tx.Rollback()
		if err != nil {
			t.Fatalf("回滚事务失败: %v", err)
		}
	})
}
