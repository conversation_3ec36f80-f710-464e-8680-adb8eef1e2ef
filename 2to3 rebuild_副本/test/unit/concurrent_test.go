package main

import (
	"bufio"
	"fmt"
	"os"
	"strings"
	"sync"
	"testing"
	"time"

	_ "modernc.org/sqlite"
)

type ProxyInfo struct {
	IP       string
	Port     string
	Username string
	Password string
}

// TestConcurrentProxy 测试并发代理验证
func TestConcurrentProxy(t *testing.T) {
	fmt.Println("=== 开始并发代理测试 ===")

	// 创建内存数据库
	db, err := openTestDB()
	if err != nil {
		t.Fatalf("创建测试数据库失败: %v", err)
	}
	defer db.Close()

	// 初始化数据库表
	fmt.Println("初始化数据库表...")
	_, err = db.Exec(`
-- 创建代理表
CREATE TABLE IF NOT EXISTS proxies (
id INTEGER PRIMARY KEY AUTOINCREMENT,
ip VARCHAR(50) NOT NULL,
port VARCHAR(10) NOT NULL,
username VARCHAR(50),
password VARCHAR(50),
auth_type VARCHAR(20),
auth_required BOOLEAN DEFAULT false,
status VARCHAR(20),
last_check TIMESTAMP,
latency INTEGER,
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
UNIQUE(ip, port)
);

-- 创建认证信息表
CREATE TABLE IF NOT EXISTS credentials (
id INTEGER PRIMARY KEY AUTOINCREMENT,
username VARCHAR(50) NOT NULL,
password VARCHAR(50) NOT NULL,
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
UNIQUE(username, password)
);`)
	if err != nil {
		t.Fatalf("创建表失败: %v", err)
	}

	// 初始化数据库（使用新的配置文件）
	fmt.Println("导入认证信息...")
	err = InitializeDatabase(db, "test/config-original.json")
	if err != nil {
		t.Fatalf("初始化数据库失败: %v", err)
	}

	// 验证认证信息是否导入成功
	creds, err := GetAllCredentialsFromDB(db)
	if err != nil {
		t.Fatalf("获取认证信息失败: %v", err)
	}
	fmt.Printf("成功导入 %d 条认证信息\n", len(creds))

	// 加载代理列表
	fmt.Println("加载代理列表...")
	proxies, err := loadProxyList("test/changsha.txt")
	if err != nil {
		t.Fatalf("加载代理列表失败: %v", err)
	}
	fmt.Printf("加载了 %d 个代理\n", len(proxies))

	// 创建测试配置
	fmt.Println("配置测试参数...")
	cfg := &Config{}
	cfg.Proxy.TimeoutSeconds = 10 // 增加超时时间
	cfg.Proxy.TestTargets = []string{"ipinfo.io:80"}

	// 创建代理测试器
	tester := NewProxyTester(cfg, db)
	if tester == nil {
		t.Fatal("创建代理测试器失败")
	}

	// 设置最大并发数
	maxConcurrent := 3
	sem := make(chan struct{}, maxConcurrent)
	var wg sync.WaitGroup

	// 记录开始时间
	startTime := time.Now()

	// 测试结果通道
	results := make(chan string, len(proxies))

	fmt.Printf("开始测试 %d 个代理，最大并发数: %d\n", len(proxies), maxConcurrent)

	// 并发测试所有代理
	for _, proxy := range proxies {
		wg.Add(1)
		sem <- struct{}{} // 获取信号量

		go func(p ProxyInfo) {
			defer func() {
				<-sem // 释放信号量
				wg.Done()
			}()

			fmt.Printf("启动测试协程 [%s:%s]\n", p.IP, p.Port)
			err := tester.TestProxy(p.IP, p.Port)
			if err != nil {
				results <- fmt.Sprintf("[失败] %s:%s - %v", p.IP, p.Port, err)
			} else {
				results <- fmt.Sprintf("[成功] %s:%s", p.IP, p.Port)
			}
		}(proxy)
	}

	// 等待所有测试完成
	go func() {
		wg.Wait()
		close(results)
	}()

	// 收集并打印结果
	successCount := 0
	failCount := 0
	for result := range results {
		fmt.Println(result)
		t.Log(result)
		if strings.Contains(result, "[成功]") {
			successCount++
		} else {
			failCount++
		}
	}

	duration := time.Since(startTime)
	summary := fmt.Sprintf("测试完成。总时间: %v, 成功: %d, 失败: %d", duration, successCount, failCount)
	fmt.Println(summary)
	t.Log(summary)
}

// loadProxyList 从文件加载代理列表
func loadProxyList(filename string) ([]ProxyInfo, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var proxies []ProxyInfo
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		parts := strings.Split(scanner.Text(), "/")
		if len(parts) != 4 {
			continue
		}
		proxy := ProxyInfo{
			IP:       parts[0],
			Port:     parts[1],
			Username: parts[2],
			Password: parts[3],
		}
		proxies = append(proxies, proxy)
	}

	return proxies, scanner.Err()
}
