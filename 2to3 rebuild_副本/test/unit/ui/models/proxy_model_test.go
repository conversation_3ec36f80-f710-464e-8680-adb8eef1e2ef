package models_test

import (
	"testing"
	"time"

	"github.com/2to3rebuild/internal/ui/models"
	"github.com/2to3rebuild/test/helpers"
)

func TestProxyModel(t *testing.T) {
	// 测试创建代理模型
	t.Run("创建代理模型", func(t *testing.T) {
		// 创建测试数据
		now := time.Now()
		model := &models.ProxyModel{
			IP:           "***********",
			Port:         "8080",
			Status:       "可用",
			Latency:      100,
			TestedAt:     now,
			Username:     "testuser",
			Password:     "testpass",
			AuthType:     "auth",
			AuthRequired: true,
			Error:        "",
			RetryCount:   0,
			CreatedAt:    now,
		}

		// 验证字段
		if model.IP != "***********" {
			t.<PERSON>rro<PERSON>("IP不匹配: expected %s, got %s", "***********", model.IP)
		}

		if model.Port != "8080" {
			t.<PERSON><PERSON><PERSON>("Port不匹配: expected %s, got %s", "8080", model.Port)
		}

		if model.Status != "可用" {
			t.Errorf("Status不匹配: expected %s, got %s", "可用", model.Status)
		}

		if model.Latency != 100 {
			t.Errorf("Latency不匹配: expected %d, got %d", 100, model.Latency)
		}

		if !model.TestedAt.Equal(now) {
			t.Errorf("TestedAt不匹配: expected %v, got %v", now, model.TestedAt)
		}

		if model.Username != "testuser" {
			t.Errorf("Username不匹配: expected %s, got %s", "testuser", model.Username)
		}

		if model.Password != "testpass" {
			t.Errorf("Password不匹配: expected %s, got %s", "testpass", model.Password)
		}

		if model.AuthType != "auth" {
			t.Errorf("AuthType不匹配: expected %s, got %s", "auth", model.AuthType)
		}

		if !model.AuthRequired {
			t.Errorf("AuthRequired不匹配: expected %t, got %t", true, model.AuthRequired)
		}

		if model.Error != "" {
			t.Errorf("Error不匹配: expected %s, got %s", "", model.Error)
		}

		if model.RetryCount != 0 {
			t.Errorf("RetryCount不匹配: expected %d, got %d", 0, model.RetryCount)
		}

		if !model.CreatedAt.Equal(now) {
			t.Errorf("CreatedAt不匹配: expected %v, got %v", now, model.CreatedAt)
		}
	})

	// 测试格式化函数
	t.Run("格式化函数", func(t *testing.T) {
		// 创建测试数据
		model := helpers.CreateTestProxyModel()

		// 执行格式化
		formatted := models.FormatProxyModel(model)

		// 验证格式化结果
		if formatted.IP != model.IP {
			t.Errorf("IP不匹配: expected %s, got %s", model.IP, formatted.IP)
		}

		if formatted.Port != model.Port {
			t.Errorf("Port不匹配: expected %s, got %s", model.Port, formatted.Port)
		}

		if formatted.Status != model.Status {
			t.Errorf("Status不匹配: expected %s, got %s", model.Status, formatted.Status)
		}

		if formatted.Latency != model.Latency {
			t.Errorf("Latency不匹配: expected %d, got %d", model.Latency, formatted.Latency)
		}

		if formatted.Username != model.Username {
			t.Errorf("Username不匹配: expected %s, got %s", model.Username, formatted.Username)
		}

		if formatted.Password != model.Password {
			t.Errorf("Password不匹配: expected %s, got %s", model.Password, formatted.Password)
		}

		if formatted.AuthType != model.AuthType {
			t.Errorf("AuthType不匹配: expected %s, got %s", model.AuthType, formatted.AuthType)
		}

		if formatted.AuthRequired != model.AuthRequired {
			t.Errorf("AuthRequired不匹配: expected %t, got %t", model.AuthRequired, formatted.AuthRequired)
		}

		// 验证格式化的URL
		expectedOriginal := model.IP + " " + model.Port
		if formatted.Original != expectedOriginal {
			t.Errorf("Original不匹配: expected %s, got %s", expectedOriginal, formatted.Original)
		}

		if model.AuthRequired {
			expectedWithAuth := "socks5://" + model.Username + ":" + model.Password + "@" + model.IP + ":" + model.Port
			if formatted.WithAuth != expectedWithAuth {
				t.Errorf("WithAuth不匹配: expected %s, got %s", expectedWithAuth, formatted.WithAuth)
			}

			expectedWithInfo := expectedWithAuth + " latency:" + helpers.FormatInt(int(model.Latency)) + "ms"
			if formatted.WithInfo != expectedWithInfo {
				t.Errorf("WithInfo不匹配: expected %s, got %s", expectedWithInfo, formatted.WithInfo)
			}
		} else {
			expectedNoAuth := "socks5://" + model.IP + ":" + model.Port
			if formatted.NoAuth != expectedNoAuth {
				t.Errorf("NoAuth不匹配: expected %s, got %s", expectedNoAuth, formatted.NoAuth)
			}

			expectedWithInfo := expectedNoAuth + " latency:" + helpers.FormatInt(int(model.Latency)) + "ms"
			if formatted.WithInfo != expectedWithInfo {
				t.Errorf("WithInfo不匹配: expected %s, got %s", expectedWithInfo, formatted.WithInfo)
			}
		}
	})

	// 测试格式化函数 - 无认证
	t.Run("格式化函数 - 无认证", func(t *testing.T) {
		// 创建测试数据
		model := helpers.CreateTestProxyModel()
		model.AuthRequired = false
		model.AuthType = "noauth"
		model.Username = ""
		model.Password = ""

		// 执行格式化
		formatted := models.FormatProxyModel(model)

		// 验证格式化结果
		expectedNoAuth := "socks5://" + model.IP + ":" + model.Port
		if formatted.NoAuth != expectedNoAuth {
			t.Errorf("NoAuth不匹配: expected %s, got %s", expectedNoAuth, formatted.NoAuth)
		}

		expectedWithInfo := expectedNoAuth + " latency:" + helpers.FormatInt(int(model.Latency)) + "ms"
		if formatted.WithInfo != expectedWithInfo {
			t.Errorf("WithInfo不匹配: expected %s, got %s", expectedWithInfo, formatted.WithInfo)
		}

		// WithAuth应该为空
		if formatted.WithAuth != "" {
			t.Errorf("WithAuth应该为空，但得到了 %s", formatted.WithAuth)
		}
	})

	// 测试格式化结果
	t.Run("格式化结果验证", func(t *testing.T) {
		// 创建测试数据
		ip := "***********"
		port := "8080"
		username := "testuser"
		password := "testpass"
		latency := int64(100)

		// 创建代理模型
		model := &models.ProxyModel{
			IP:           ip,
			Port:         port,
			Username:     username,
			Password:     password,
			Latency:      latency,
			AuthRequired: true,
			AuthType:     "auth",
		}

		// 执行格式化
		formatted := models.FormatProxyModel(model)

		// 验证带认证的URL
		expectedWithAuth := "socks5://" + username + ":" + password + "@" + ip + ":" + port
		if formatted.WithAuth != expectedWithAuth {
			t.Errorf("WithAuth不匹配: expected %s, got %s", expectedWithAuth, formatted.WithAuth)
		}

		// 验证无认证的URL
		expectedNoAuth := "socks5://" + ip + ":" + port
		if formatted.NoAuth != expectedNoAuth {
			t.Errorf("NoAuth不匹配: expected %s, got %s", expectedNoAuth, formatted.NoAuth)
		}

		// 验证原始格式
		expectedOriginal := ip + " " + port
		if formatted.Original != expectedOriginal {
			t.Errorf("Original不匹配: expected %s, got %s", expectedOriginal, formatted.Original)
		}

		// 验证带延迟信息的URL
		expectedWithInfo := expectedWithAuth + " latency:" + helpers.FormatInt(int(latency)) + "ms"
		if formatted.WithInfo != expectedWithInfo {
			t.Errorf("WithInfo不匹配: expected %s, got %s", expectedWithInfo, formatted.WithInfo)
		}
	})
}
