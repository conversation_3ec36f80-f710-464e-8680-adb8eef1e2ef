package models_test

import (
	"testing"

	"github.com/2to3rebuild/internal/ui/models"
	"github.com/2to3rebuild/test/helpers"
)

func TestConfigModel(t *testing.T) {
	// 测试创建配置模型
	t.Run("创建配置模型", func(t *testing.T) {
		// 创建测试数据
		model := &models.ConfigModel{
			TimeoutSeconds: 5,
			TestTargets:    []string{"ipinfo.io:80", "google.com:80"},
			DatabasePath:   "test.db",
			Credentials: []models.CredentialModel{
				{Username: "user1", Password: "pass1"},
				{Username: "user2", Password: "pass2"},
			},
		}

		// 验证字段
		if model.TimeoutSeconds != 5 {
			t.<PERSON><PERSON><PERSON>("TimeoutSeconds不匹配: expected %d, got %d", 5, model.TimeoutSeconds)
		}

		if len(model.TestTargets) != 2 {
			t.<PERSON>("TestTargets长度不匹配: expected %d, got %d", 2, len(model.TestTargets))
		} else {
			if model.TestTargets[0] != "ipinfo.io:80" {
				t.<PERSON><PERSON><PERSON>("TestTargets[0]不匹配: expected %s, got %s", "ipinfo.io:80", model.TestTargets[0])
			}
			if model.TestTargets[1] != "google.com:80" {
				t.Errorf("TestTargets[1]不匹配: expected %s, got %s", "google.com:80", model.TestTargets[1])
			}
		}

		if model.DatabasePath != "test.db" {
			t.Errorf("DatabasePath不匹配: expected %s, got %s", "test.db", model.DatabasePath)
		}

		if len(model.Credentials) != 2 {
			t.Errorf("Credentials长度不匹配: expected %d, got %d", 2, len(model.Credentials))
		} else {
			if model.Credentials[0].Username != "user1" {
				t.Errorf("Credentials[0].Username不匹配: expected %s, got %s", "user1", model.Credentials[0].Username)
			}
			if model.Credentials[0].Password != "pass1" {
				t.Errorf("Credentials[0].Password不匹配: expected %s, got %s", "pass1", model.Credentials[0].Password)
			}
			if model.Credentials[1].Username != "user2" {
				t.Errorf("Credentials[1].Username不匹配: expected %s, got %s", "user2", model.Credentials[1].Username)
			}
			if model.Credentials[1].Password != "pass2" {
				t.Errorf("Credentials[1].Password不匹配: expected %s, got %s", "pass2", model.Credentials[1].Password)
			}
		}
	})

	// 测试配置验证结果
	t.Run("配置验证结果", func(t *testing.T) {
		// 创建有效的验证结果
		validResult := &models.ConfigValidationResult{
			Valid:   true,
			Errors:  map[string]string{},
			Summary: "配置验证成功",
		}

		// 验证字段
		if !validResult.Valid {
			t.Errorf("Valid不匹配: expected %t, got %t", true, validResult.Valid)
		}

		if len(validResult.Errors) != 0 {
			t.Errorf("Errors长度不匹配: expected %d, got %d", 0, len(validResult.Errors))
		}

		if validResult.Summary != "配置验证成功" {
			t.Errorf("Summary不匹配: expected %s, got %s", "配置验证成功", validResult.Summary)
		}

		// 创建无效的验证结果
		invalidResult := &models.ConfigValidationResult{
			Valid: false,
			Errors: map[string]string{
				"TimeoutSeconds": "超时时间必须大于0",
				"TestTargets":    "至少需要一个测试目标",
			},
			Summary: "配置验证失败，请修复以下错误",
		}

		// 验证字段
		if invalidResult.Valid {
			t.Errorf("Valid不匹配: expected %t, got %t", false, invalidResult.Valid)
		}

		if len(invalidResult.Errors) != 2 {
			t.Errorf("Errors长度不匹配: expected %d, got %d", 2, len(invalidResult.Errors))
		} else {
			if invalidResult.Errors["TimeoutSeconds"] != "超时时间必须大于0" {
				t.Errorf("Errors[TimeoutSeconds]不匹配: expected %s, got %s", "超时时间必须大于0", invalidResult.Errors["TimeoutSeconds"])
			}
			if invalidResult.Errors["TestTargets"] != "至少需要一个测试目标" {
				t.Errorf("Errors[TestTargets]不匹配: expected %s, got %s", "至少需要一个测试目标", invalidResult.Errors["TestTargets"])
			}
		}

		if invalidResult.Summary != "配置验证失败，请修复以下错误" {
			t.Errorf("Summary不匹配: expected %s, got %s", "配置验证失败，请修复以下错误", invalidResult.Summary)
		}
	})

	// 测试随机生成的配置模型
	t.Run("随机生成的配置模型", func(t *testing.T) {
		// 生成随机配置模型
		model := helpers.GenerateRandomConfigModel()

		// 验证字段不为空
		if model.TimeoutSeconds <= 0 {
			t.Errorf("TimeoutSeconds应该大于0，但得到了 %d", model.TimeoutSeconds)
		}

		if len(model.TestTargets) == 0 {
			t.Errorf("TestTargets不应该为空")
		}

		if model.DatabasePath == "" {
			t.Errorf("DatabasePath不应该为空")
		}

		if len(model.Credentials) == 0 {
			t.Errorf("Credentials不应该为空")
		}

		// 验证凭据字段不为空
		for i, cred := range model.Credentials {
			if cred.Username == "" {
				t.Errorf("Credentials[%d].Username不应该为空", i)
			}
			if cred.Password == "" {
				t.Errorf("Credentials[%d].Password不应该为空", i)
			}
		}
	})

	// 测试辅助函数
	t.Run("辅助函数", func(t *testing.T) {
		// 测试NewConfigModel函数
		model := models.NewConfigModel()

		// 验证默认值
		if model.TimeoutSeconds != 5 {
			t.Errorf("默认TimeoutSeconds不匹配: expected %d, got %d", 5, model.TimeoutSeconds)
		}

		if len(model.TestTargets) != 1 || model.TestTargets[0] != "ipinfo.io:80" {
			t.Errorf("默认TestTargets不匹配: expected %v, got %v", []string{"ipinfo.io:80"}, model.TestTargets)
		}

		if model.DatabasePath != "proxies.db" {
			t.Errorf("默认DatabasePath不匹配: expected %s, got %s", "proxies.db", model.DatabasePath)
		}

		if len(model.Credentials) != 0 {
			t.Errorf("默认Credentials不匹配: expected empty, got %v", model.Credentials)
		}
	})
}
