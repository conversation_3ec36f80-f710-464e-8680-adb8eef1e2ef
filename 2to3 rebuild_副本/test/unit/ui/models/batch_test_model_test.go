package models_test

import (
	"testing"
	"time"

	"github.com/2to3rebuild/internal/ui/common"
	"github.com/2to3rebuild/internal/ui/models"
	"github.com/2to3rebuild/test/helpers"
)

func TestBatchTestModel(t *testing.T) {
	// 测试创建批量测试结果
	t.Run("创建批量测试结果", func(t *testing.T) {
		// 创建测试数据
		now := time.Now()
		result := &models.BatchTestResult{
			SuccessCount: 2,
			FailCount:    1,
			Results: []*models.ProxyModel{
				{
					IP:           "***********",
					Port:         "8080",
					Status:       "可用",
					Latency:      100,
					TestedAt:     now,
					Username:     "testuser",
					Password:     "testpass",
					AuthType:     "auth",
					AuthRequired: true,
					Error:        "",
					RetryCount:   0,
					CreatedAt:    now,
				},
				{
					IP:           "***********",
					Port:         "8080",
					Status:       "可用",
					Latency:      150,
					TestedAt:     now,
					Username:     "",
					Password:     "",
					AuthType:     "noauth",
					AuthRequired: false,
					Error:        "",
					RetryCount:   0,
					CreatedAt:    now,
				},
				{
					IP:           "***********",
					Port:         "8080",
					Status:       "不可用",
					Latency:      0,
					TestedAt:     now,
					Username:     "",
					Password:     "",
					AuthType:     "noauth",
					AuthRequired: false,
					Error:        "连接超时",
					RetryCount:   3,
					CreatedAt:    now,
				},
			},
		}

		// 验证字段
		if result.SuccessCount != 2 {
			t.Errorf("SuccessCount不匹配: expected %d, got %d", 2, result.SuccessCount)
		}

		if result.FailCount != 1 {
			t.Errorf("FailCount不匹配: expected %d, got %d", 1, result.FailCount)
		}

		if len(result.Results) != 3 {
			t.Errorf("Results长度不匹配: expected %d, got %d", 3, len(result.Results))
		}

		// 验证第一个结果
		if result.Results[0].IP != "***********" {
			t.Errorf("Results[0].IP不匹配: expected %s, got %s", "***********", result.Results[0].IP)
		}

		if result.Results[0].Status != "可用" {
			t.Errorf("Results[0].Status不匹配: expected %s, got %s", "可用", result.Results[0].Status)
		}

		// 验证第三个结果
		if result.Results[2].IP != "***********" {
			t.Errorf("Results[2].IP不匹配: expected %s, got %s", "***********", result.Results[2].IP)
		}

		if result.Results[2].Status != "不可用" {
			t.Errorf("Results[2].Status不匹配: expected %s, got %s", "不可用", result.Results[2].Status)
		}

		if result.Results[2].Error != "连接超时" {
			t.Errorf("Results[2].Error不匹配: expected %s, got %s", "连接超时", result.Results[2].Error)
		}
	})

	// 测试批量测试参数
	t.Run("批量测试参数", func(t *testing.T) {
		// 创建测试数据
		params := &models.BatchTestParams{
			FilePath:   "test.txt",
			Concurrent: 5,
			TestAuth:   true,
		}

		// 验证字段
		if params.FilePath != "test.txt" {
			t.Errorf("FilePath不匹配: expected %s, got %s", "test.txt", params.FilePath)
		}

		if params.Concurrent != 5 {
			t.Errorf("Concurrent不匹配: expected %d, got %d", 5, params.Concurrent)
		}

		if !params.TestAuth {
			t.Errorf("TestAuth不匹配: expected %t, got %t", true, params.TestAuth)
		}

		// 测试默认参数
		defaultParams := models.NewBatchTestParams()

		if defaultParams.FilePath != "" {
			t.Errorf("默认FilePath不匹配: expected %s, got %s", "", defaultParams.FilePath)
		}

		if defaultParams.Concurrent != 10 {
			t.Errorf("默认Concurrent不匹配: expected %d, got %d", 10, defaultParams.Concurrent)
		}

		if !defaultParams.TestAuth {
			t.Errorf("默认TestAuth不匹配: expected %t, got %t", true, defaultParams.TestAuth)
		}
	})

	// 测试随机生成的批量测试结果
	t.Run("随机生成的批量测试结果", func(t *testing.T) {
		// 生成随机批量测试结果
		result := helpers.CreateTestBatchTestResult()

		// 验证字段不为空
		if result.SuccessCount <= 0 {
			t.Errorf("SuccessCount应该大于0，但得到了 %d", result.SuccessCount)
		}

		if result.FailCount < 0 {
			t.Errorf("FailCount不应该小于0，但得到了 %d", result.FailCount)
		}

		if len(result.Results) == 0 {
			t.Errorf("Results不应该为空")
		}

		// 验证结果字段不为空
		for i, proxy := range result.Results {
			if proxy.IP == "" {
				t.Errorf("Results[%d].IP不应该为空", i)
			}

			if proxy.Port == "" {
				t.Errorf("Results[%d].Port不应该为空", i)
			}

			if proxy.Status == "" {
				t.Errorf("Results[%d].Status不应该为空", i)
			}
		}
	})

	// 测试消息类型
	t.Run("消息类型", func(t *testing.T) {
		// 创建进度消息
		progressMsg := common.ProgressMsg{
			Percent: 0.5,
		}

		// 验证字段
		if progressMsg.Percent != 0.5 {
			t.Errorf("Percent不匹配: expected %f, got %f", 0.5, progressMsg.Percent)
		}

		// 创建批量测试结果消息
		resultMsg := common.BatchTestResultMsg{
			Result: "测试完成: 成功 2 个, 失败 1 个",
		}

		// 验证字段
		if resultMsg.Result != "测试完成: 成功 2 个, 失败 1 个" {
			t.Errorf("Result不匹配: expected %s, got %s", "测试完成: 成功 2 个, 失败 1 个", resultMsg.Result)
		}

		// 创建批量测试错误消息
		errorMsg := common.BatchTestErrorMsg{
			Error: "测试失败: 文件不存在",
		}

		// 验证字段
		if errorMsg.Error != "测试失败: 文件不存在" {
			t.Errorf("Error不匹配: expected %s, got %s", "测试失败: 文件不存在", errorMsg.Error)
		}
	})
}
