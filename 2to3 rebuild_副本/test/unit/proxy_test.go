package main

import (
	"database/sql"
	"fmt"
	"net"
	"testing"
	"time"
)

// 使用全局数据库连接
var testDB *sql.DB

// openTestDB 打开测试用内存数据库
func openTestDB() (*sql.DB, error) {
	db, err := sql.Open("sqlite", ":memory:")
	if err != nil {
		return nil, fmt.Errorf("创建内存数据库失败: %v", err)
	}
	return db, nil
}

func TestProxyTester(t *testing.T) {
	// 创建测试配置
	cfg := &Config{}
	cfg.Proxy.TimeoutSeconds = 5
	cfg.Proxy.TestTargets = []string{"ipinfo.io:80"}
	cfg.Proxy.Credentials = []Credential{
		{Username: "test1", Password: "pass1"},
		{Username: "test2", Password: "pass2"},
	}

	// 初始化数据库
	if testDB == nil {
		var err error
		testDB, err = sql.Open("sqlite", ":memory:")
		if err != nil {
			t.Fatalf("创建内存数据库失败: %v", err)
		}

		// 创建数据库表
		_, err = testDB.Exec(`
        CREATE TABLE IF NOT EXISTS proxies (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            ip VARCHAR(50) NOT NULL,
            port VARCHAR(10) NOT NULL,
            username VARCHAR(50),
            password VARCHAR(50),
            auth_type VARCHAR(20),
            auth_required BOOLEAN DEFAULT false,
            status VARCHAR(20),
            last_check TIMESTAMP,
            latency INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(ip, port)
        );`)
		if err != nil {
			t.Fatalf("创建表失败: %v", err)
		}
	}

	// 创建代理测试器
	tester := NewProxyTester(cfg, testDB)

	// 测试用例：无效的代理地址
	t.Run("TestInvalidProxy", func(t *testing.T) {
		_, err := tester.TestNoAuth("127.0.0.1", "1234")
		if err == nil {
			t.Error("期望错误但得到nil")
		}
	})

	// 测试用例：保存代理信息
	t.Run("TestSaveProxy", func(t *testing.T) {
		proxy := &Proxy{
			IP:        "***********",
			Port:      "8080",
			AuthType:  "noauth",
			Status:    "可用",
			Latency:   100,
			LastCheck: time.Now(),
			CreatedAt: time.Now(),
		}

		err := SaveProxyToDB(testDB, proxy)
		if err != nil {
			t.Errorf("保存代理失败: %v", err)
		}

		// 验证保存的数据
		saved, err := GetProxyFromDB(testDB, proxy.IP, proxy.Port)
		if err != nil {
			t.Errorf("获取代理失败: %v", err)
		}
		if saved == nil {
			t.Error("未找到保存的代理")
			return
		}
		if saved.IP != proxy.IP || saved.Port != proxy.Port {
			t.Errorf("保存的数据不匹配，期望 %v，得到 %v", proxy, saved)
		}
	})

	// 测试用例：带认证的代理测试
	t.Run("TestAuthProxy", func(t *testing.T) {
		proxy, err := tester.TestWithAuth("127.0.0.1", "1234", "test", "pass")
		if err == nil {
			t.Error("期望错误但得到nil")
		}
		if proxy != nil {
			t.Error("期望nil代理但得到结果")
		}
	})

	// 测试用例：超时处理
	t.Run("TestTimeout", func(t *testing.T) {
		cfg.Proxy.TimeoutSeconds = 1
		_, err := tester.TestNoAuth("*******", "1234")
		if err == nil {
			t.Error("期望超时错误但得到nil")
		}
	})

	// 测试用例：重试次数验证
	t.Run("TestRetryCount", func(t *testing.T) {
		startTime := time.Now()
		_, err := tester.TestNoAuth("********", "1234") // 使用不可达的地址
		duration := time.Since(startTime)

		// 验证重试次数
		expectedDuration := DefaultRetryConfig.Interval * time.Duration(DefaultRetryConfig.MaxAttempts)
		if duration < expectedDuration {
			t.Errorf("重试次数不足，期望耗时至少 %v，实际耗时 %v", expectedDuration, duration)
		}

		// 验证错误信息
		if err == nil {
			t.Error("期望返回错误但得到nil")
		}
		if _, ok := err.(*net.OpError); !ok {
			t.Errorf("期望网络错误，但得到：%v", err)
		}
	})

	// 测试用例：非网络错误不重试
	t.Run("TestNoRetryOnNonNetError", func(t *testing.T) {
		startTime := time.Now()
		_, err := tester.TestWithAuth("127.0.0.1", "1234", "", "") // 传入无效认证信息
		duration := time.Since(startTime)

		// 验证没有重试（执行时间小于重试间隔）
		if duration >= DefaultRetryConfig.Interval {
			t.Errorf("不应该重试非网络错误，执行时间过长：%v", duration)
		}

		// 验证错误信息
		if err == nil {
			t.Error("期望返回错误但得到nil")
		}
	})

	// 测试用例：真实代理测试
	t.Run("TestRealProxy", func(t *testing.T) {
		// 使用真实可用的代理服务器
		proxyIP := "************"
		proxyPort := "18889"

		// 1. 测试正确认证
		t.Run("CorrectAuth", func(t *testing.T) {
			proxy, err := tester.TestWithAuth(proxyIP, proxyPort, "vip9", "123456")
			if err != nil {
				t.Errorf("连接可用代理失败: %v", err)
			}
			if proxy == nil {
				t.Error("期望返回代理信息但得到nil")
				return
			}
			if proxy.Status != "可用" {
				t.Errorf("代理状态不正确，期望'可用'，得到'%s'", proxy.Status)
			}
		})

		// 2. 测试错误认证（触发重试）
		t.Run("WrongAuth", func(t *testing.T) {
			startTime := time.Now()
			_, err := tester.TestWithAuth(proxyIP, proxyPort, "wronguser", "wrongpass")
			duration := time.Since(startTime)

			// 验证进行了重试
			expectedDuration := DefaultRetryConfig.Interval * time.Duration(DefaultRetryConfig.MaxAttempts)
			if duration < expectedDuration {
				t.Errorf("重试次数不足，期望耗时至少 %v，实际耗时 %v", expectedDuration, duration)
			}

			if err == nil {
				t.Error("使用错误认证应该返回错误")
			}
		})

		// 3. 测试无认证（触发重试）
		t.Run("NoAuth", func(t *testing.T) {
			startTime := time.Now()
			_, err := tester.TestNoAuth(proxyIP, proxyPort)
			duration := time.Since(startTime)

			// 验证进行了重试
			expectedDuration := DefaultRetryConfig.Interval * time.Duration(DefaultRetryConfig.MaxAttempts)
			if duration < expectedDuration {
				t.Errorf("重试次数不足，期望耗时至少 %v，实际耗时 %v", expectedDuration, duration)
			}

			if err == nil {
				t.Error("无认证访问应该返回错误")
			}
		})
	})
}
