package main

import (
	"database/sql"
	"testing"

	_ "modernc.org/sqlite" // SQLite驱动
)

func TestInitializeDatabase(t *testing.T) {
	// 创建内存数据库
	db, err := sql.Open("sqlite", ":memory:")
	if err != nil {
		t.Fatalf("创建内存数据库失败: %v", err)
	}
	defer db.Close()

	// 初始化数据库（使用测试配置文件）
	err = InitializeDatabase(db, "../2to3 18889/config.json")
	if err != nil {
		t.Fatalf("初始化数据库失败: %v", err)
	}

	// 验证认证信息是否正确导入
	creds, err := GetAllCredentialsFromDB(db)
	if err != nil {
		t.Fatalf("获取认证信息失败: %v", err)
	}

	// 检查是否导入了足够的认证信息
	if len(creds) == 0 {
		t.<PERSON><PERSON>("没有导入任何认证信息")
	}

	// 验证一些特定的认证信息是否存在
	expectedCreds := map[string]string{
		"vip9":   "123456",
		"admin":  "123456",
		"guest":  "123456",
		"socks5": "socks5",
	}

	for username, password := range expectedCreds {
		found := false
		for _, cred := range creds {
			if cred.Username == username && cred.Password == password {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("未找到期望的认证信息: %s:%s", username, password)
		}
	}
}
