// Package helpers 提供测试辅助函数
package helpers

import (
	"errors"
	"time"

	"github.com/2to3rebuild/internal/app/services"
	"github.com/2to3rebuild/internal/config"
	"github.com/2to3rebuild/internal/ui/models"
)

// MockServices 模拟服务层
type MockServices struct {
	Proxy    *MockProxyService
	Config   *MockConfigService
	Database *MockDatabaseService
	Services *services.Services
}

// NewMockServices 创建模拟服务层
func NewMockServices() *MockServices {
	mockProxy := NewMockProxyService()
	mockConfig := NewMockConfigService()
	mockDatabase := NewMockDatabaseService()

	// 创建服务层
	// 注意：这里我们不创建真实的Services对象，因为类型不匹配
	// 在测试中，我们将直接使用mock对象

	return &MockServices{
		Proxy:    mockProxy,
		Config:   mockConfig,
		Database: mockDatabase,
		Services: nil, // 在测试中不使用这个字段
	}
}

// MockProxyService 模拟代理服务
type MockProxyService struct {
	TestSingleProxyFunc  func(ip, port string, useAuth bool, username, password string) (*services.ProxyResult, error)
	TestBatchProxiesFunc func(proxies [][]string, concurrent int, testAuth bool) (*services.BatchTestResult, error)
	Tester               *MockProxyTester
}

// NewMockProxyService 创建模拟代理服务
func NewMockProxyService() *MockProxyService {
	return &MockProxyService{
		TestSingleProxyFunc: func(ip, port string, useAuth bool, username, password string) (*services.ProxyResult, error) {
			if ip == "error.test" {
				return nil, errors.New("测试错误")
			}

			result := &services.ProxyResult{
				IP:           ip,
				Port:         port,
				Status:       "可用",
				Latency:      100,
				LastCheck:    time.Now(),
				CreatedAt:    time.Now(),
				AuthRequired: useAuth,
			}

			if useAuth {
				result.Username = username
				result.Password = password
				result.AuthType = "auth"
			} else {
				result.AuthType = "noauth"
			}

			return result, nil
		},
		TestBatchProxiesFunc: func(proxies [][]string, concurrent int, testAuth bool) (*services.BatchTestResult, error) {
			if len(proxies) == 0 {
				return nil, errors.New("代理列表为空")
			}

			results := make([]*services.ProxyResult, 0, len(proxies))
			successCount := 0
			failCount := 0

			for _, proxy := range proxies {
				if len(proxy) < 2 {
					continue
				}

				ip, port := proxy[0], proxy[1]

				var username, password string
				useAuth := false

				if testAuth && len(proxy) >= 4 {
					username, password = proxy[2], proxy[3]
					useAuth = true
				}

				result := &services.ProxyResult{
					IP:           ip,
					Port:         port,
					Status:       "可用",
					Latency:      100,
					LastCheck:    time.Now(),
					CreatedAt:    time.Now(),
					AuthRequired: useAuth,
				}

				if useAuth {
					result.Username = username
					result.Password = password
					result.AuthType = "auth"
				} else {
					result.AuthType = "noauth"
				}

				// 模拟一些失败的情况
				if ip == "error.test" {
					result.Status = "不可用"
					failCount++
				} else {
					successCount++
				}

				results = append(results, result)
			}

			return &services.BatchTestResult{
				SuccessCount: successCount,
				FailCount:    failCount,
				Results:      results,
			}, nil
		},
		Tester: NewMockProxyTester(),
	}
}

// TestSingleProxy 测试单个代理
func (m *MockProxyService) TestSingleProxy(ip, port string, useAuth bool, username, password string) (*services.ProxyResult, error) {
	return m.TestSingleProxyFunc(ip, port, useAuth, username, password)
}

// TestBatchProxies 批量测试代理
func (m *MockProxyService) TestBatchProxies(proxies [][]string, concurrent int, testAuth bool) (*services.BatchTestResult, error) {
	return m.TestBatchProxiesFunc(proxies, concurrent, testAuth)
}

// MockProxyTester 模拟代理测试器
type MockProxyTester struct {
	SaveTestResultFunc func(result *services.ProxyResult) error
}

// NewMockProxyTester 创建模拟代理测试器
func NewMockProxyTester() *MockProxyTester {
	return &MockProxyTester{
		SaveTestResultFunc: func(result *services.ProxyResult) error {
			if result.IP == "error.test" {
				return errors.New("保存测试结果失败")
			}
			return nil
		},
	}
}

// SaveTestResult 保存测试结果
func (m *MockProxyTester) SaveTestResult(result *services.ProxyResult) error {
	return m.SaveTestResultFunc(result)
}

// MockConfigService 模拟配置服务
type MockConfigService struct {
	Config       *config.Config
	ValidateFunc func() error
	SaveFunc     func(path string) error
}

// NewMockConfigService 创建模拟配置服务
func NewMockConfigService() *MockConfigService {
	return &MockConfigService{
		Config: &config.Config{},
		ValidateFunc: func() error {
			return nil
		},
		SaveFunc: func(path string) error {
			if path == "error.json" {
				return errors.New("保存配置失败")
			}
			return nil
		},
	}
}

// Validate 验证配置
func (m *MockConfigService) Validate() error {
	return m.ValidateFunc()
}

// Save 保存配置
func (m *MockConfigService) Save(path string) error {
	return m.SaveFunc(path)
}

// MockDatabaseService 模拟数据库服务
type MockDatabaseService struct {
	ExecuteSQLFunc func(sqlCmd string) (interface{}, error)
}

// NewMockDatabaseService 创建模拟数据库服务
func NewMockDatabaseService() *MockDatabaseService {
	return &MockDatabaseService{
		ExecuteSQLFunc: func(sqlCmd string) (interface{}, error) {
			if sqlCmd == "ERROR" {
				return nil, errors.New("SQL执行错误")
			}

			if sqlCmd == "SELECT * FROM proxies" {
				return []map[string]interface{}{
					{
						"ip":            "***********",
						"port":          "8080",
						"status":        "可用",
						"latency":       100,
						"auth_type":     "noauth",
						"auth_required": false,
					},
					{
						"ip":            "***********",
						"port":          "8080",
						"status":        "可用",
						"latency":       150,
						"auth_type":     "auth",
						"auth_required": true,
						"username":      "testuser",
						"password":      "testpass",
					},
				}, nil
			}

			return "SQL执行成功", nil
		},
	}
}

// ExecuteSQL 执行SQL命令
func (m *MockDatabaseService) ExecuteSQL(sqlCmd string) (interface{}, error) {
	return m.ExecuteSQLFunc(sqlCmd)
}

// CreateMockConfigValidationResult 创建模拟配置验证结果
func CreateMockConfigValidationResult(valid bool) *models.ConfigValidationResult {
	if valid {
		return &models.ConfigValidationResult{
			Valid:   true,
			Errors:  map[string]string{},
			Summary: "配置验证成功",
		}
	}

	return &models.ConfigValidationResult{
		Valid: false,
		Errors: map[string]string{
			"TimeoutSeconds": "超时时间必须大于0",
			"TestTargets":    "至少需要一个测试目标",
		},
		Summary: "配置验证失败，请修复以下错误",
	}
}
