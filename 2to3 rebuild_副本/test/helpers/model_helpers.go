// Package helpers 提供测试辅助函数
package helpers

import (
	"testing"
	"time"

	"github.com/2to3rebuild/internal/ui/models"
)

// CreateFormattedProxyModel 创建格式化的代理模型
func CreateFormattedProxyModel() *models.FormattedProxyModel {
	return &models.FormattedProxyModel{
		IP:           "***********",
		Port:         "8080",
		Status:       "可用",
		Latency:      100,
		Username:     "testuser",
		Password:     "testpass",
		AuthType:     "auth",
		AuthRequired: true,
		Original:     "*********** 8080",
		WithAuth:     "socks5://testuser:testpass@***********:8080",
		NoAuth:       "socks5://***********:8080",
		WithInfo:     "socks5://testuser:testpass@***********:8080 latency:100ms",
	}
}

// AssertProxyModelEqual 断言两个代理模型相等
func AssertProxyModelEqual(t *testing.T, expected, actual *models.ProxyModel) {
	t.Helper()
	
	if expected.IP != actual.IP {
		t.Errorf("IP不匹配: expected %s, got %s", expected.IP, actual.IP)
	}
	
	if expected.Port != actual.Port {
		t.Errorf("Port不匹配: expected %s, got %s", expected.Port, actual.Port)
	}
	
	if expected.Status != actual.Status {
		t.Errorf("Status不匹配: expected %s, got %s", expected.Status, actual.Status)
	}
	
	if expected.Latency != actual.Latency {
		t.Errorf("Latency不匹配: expected %d, got %d", expected.Latency, actual.Latency)
	}
	
	if expected.Username != actual.Username {
		t.Errorf("Username不匹配: expected %s, got %s", expected.Username, actual.Username)
	}
	
	if expected.Password != actual.Password {
		t.Errorf("Password不匹配: expected %s, got %s", expected.Password, actual.Password)
	}
	
	if expected.AuthType != actual.AuthType {
		t.Errorf("AuthType不匹配: expected %s, got %s", expected.AuthType, actual.AuthType)
	}
	
	if expected.AuthRequired != actual.AuthRequired {
		t.Errorf("AuthRequired不匹配: expected %t, got %t", expected.AuthRequired, actual.AuthRequired)
	}
	
	if expected.Error != actual.Error {
		t.Errorf("Error不匹配: expected %s, got %s", expected.Error, actual.Error)
	}
}

// AssertConfigModelEqual 断言两个配置模型相等
func AssertConfigModelEqual(t *testing.T, expected, actual *models.ConfigModel) {
	t.Helper()
	
	if expected.TimeoutSeconds != actual.TimeoutSeconds {
		t.Errorf("TimeoutSeconds不匹配: expected %d, got %d", expected.TimeoutSeconds, actual.TimeoutSeconds)
	}
	
	if expected.DatabasePath != actual.DatabasePath {
		t.Errorf("DatabasePath不匹配: expected %s, got %s", expected.DatabasePath, actual.DatabasePath)
	}
	
	if len(expected.TestTargets) != len(actual.TestTargets) {
		t.Errorf("TestTargets长度不匹配: expected %d, got %d", len(expected.TestTargets), len(actual.TestTargets))
		return
	}
	
	for i, target := range expected.TestTargets {
		if target != actual.TestTargets[i] {
			t.Errorf("TestTargets[%d]不匹配: expected %s, got %s", i, target, actual.TestTargets[i])
		}
	}
	
	if len(expected.Credentials) != len(actual.Credentials) {
		t.Errorf("Credentials长度不匹配: expected %d, got %d", len(expected.Credentials), len(actual.Credentials))
		return
	}
	
	for i, cred := range expected.Credentials {
		if cred.Username != actual.Credentials[i].Username {
			t.Errorf("Credentials[%d].Username不匹配: expected %s, got %s", i, cred.Username, actual.Credentials[i].Username)
		}
		
		if cred.Password != actual.Credentials[i].Password {
			t.Errorf("Credentials[%d].Password不匹配: expected %s, got %s", i, cred.Password, actual.Credentials[i].Password)
		}
	}
}

// CreateTestBatchTestResult 创建测试用的批量测试结果
func CreateTestBatchTestResult() *models.BatchTestResult {
	return &models.BatchTestResult{
		SuccessCount: 2,
		FailCount:    1,
		Results: []*models.ProxyModel{
			{
				IP:           "***********",
				Port:         "8080",
				Status:       "可用",
				Latency:      100,
				TestedAt:     time.Now(),
				Username:     "testuser",
				Password:     "testpass",
				AuthType:     "auth",
				AuthRequired: true,
				Error:        "",
				RetryCount:   0,
				CreatedAt:    time.Now(),
			},
			{
				IP:           "***********",
				Port:         "8080",
				Status:       "可用",
				Latency:      150,
				TestedAt:     time.Now(),
				Username:     "",
				Password:     "",
				AuthType:     "noauth",
				AuthRequired: false,
				Error:        "",
				RetryCount:   0,
				CreatedAt:    time.Now(),
			},
			{
				IP:           "***********",
				Port:         "8080",
				Status:       "不可用",
				Latency:      0,
				TestedAt:     time.Now(),
				Username:     "",
				Password:     "",
				AuthType:     "noauth",
				AuthRequired: false,
				Error:        "连接超时",
				RetryCount:   3,
				CreatedAt:    time.Now(),
			},
		},
	}
}
