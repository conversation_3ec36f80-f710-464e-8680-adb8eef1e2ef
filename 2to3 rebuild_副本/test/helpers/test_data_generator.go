// Package helpers 提供测试辅助函数
package helpers

import (
	"fmt"
	"math/rand"
	"time"

	"github.com/2to3rebuild/internal/app/models"
	"github.com/2to3rebuild/internal/app/services"
	"github.com/2to3rebuild/internal/config"
	"github.com/2to3rebuild/internal/proxy"
	"github.com/2to3rebuild/internal/ui/menu"
	uimodels "github.com/2to3rebuild/internal/ui/models"
)

// 初始化随机数生成器
func init() {
	// Go 1.20+ 不再需要手动设置随机种子
	// rand.Seed 已被弃用
}

// GenerateRandomIP 生成随机IP地址
func GenerateRandomIP() string {
	return fmt.Sprintf("%d.%d.%d.%d", rand.Intn(256), rand.Intn(256), rand.Intn(256), rand.Intn(256))
}

// GenerateRandomPort 生成随机端口
func GenerateRandomPort() string {
	return fmt.Sprintf("%d", 1000+rand.Intn(9000))
}

// GenerateRandomString 生成随机字符串
func GenerateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}

// GenerateRandomProxyModel 生成随机代理模型
func GenerateRandomProxyModel() *uimodels.ProxyModel {
	authRequired := rand.Intn(2) == 1

	model := &uimodels.ProxyModel{
		IP:           GenerateRandomIP(),
		Port:         GenerateRandomPort(),
		Status:       "可用",
		Latency:      int64(rand.Intn(500)),
		TestedAt:     time.Now(),
		AuthRequired: authRequired,
		Error:        "",
		RetryCount:   rand.Intn(3),
		CreatedAt:    time.Now(),
	}

	if authRequired {
		model.Username = GenerateRandomString(8)
		model.Password = GenerateRandomString(8)
		model.AuthType = "auth"
	} else {
		model.AuthType = "noauth"
	}

	return model
}

// GenerateRandomConfigModel 生成随机配置模型
func GenerateRandomConfigModel() *uimodels.ConfigModel {
	credCount := 1 + rand.Intn(3)
	creds := make([]uimodels.CredentialModel, credCount)

	for i := 0; i < credCount; i++ {
		creds[i] = uimodels.CredentialModel{
			Username: GenerateRandomString(8),
			Password: GenerateRandomString(8),
		}
	}

	targetCount := 1 + rand.Intn(3)
	targets := make([]string, targetCount)

	for i := 0; i < targetCount; i++ {
		targets[i] = fmt.Sprintf("%s:%s", GenerateRandomString(8), GenerateRandomPort())
	}

	return &uimodels.ConfigModel{
		TimeoutSeconds: 1 + rand.Intn(10),
		TestTargets:    targets,
		DatabasePath:   fmt.Sprintf("%s.db", GenerateRandomString(8)),
		Credentials:    creds,
	}
}

// GenerateRandomProxyResult 生成随机代理测试结果
func GenerateRandomProxyResult() *proxy.TestResult {
	authRequired := rand.Intn(2) == 1

	result := &proxy.TestResult{
		IP:           GenerateRandomIP(),
		Port:         GenerateRandomPort(),
		Status:       "可用",
		Error:        "",
		Latency:      int64(rand.Intn(500)),
		TestedAt:     time.Now(),
		RetryCount:   rand.Intn(3),
		AuthRequired: authRequired,
		AuthSuccess:  authRequired,
	}

	if authRequired {
		result.Username = GenerateRandomString(8)
		result.Password = GenerateRandomString(8)
	}

	return result
}

// GenerateRandomMenuProxy 生成随机菜单代理
func GenerateRandomMenuProxy() *menu.Proxy {
	authRequired := rand.Intn(2) == 1

	proxy := &menu.Proxy{
		IP:           GenerateRandomIP(),
		Port:         GenerateRandomPort(),
		Status:       "可用",
		Latency:      int64(rand.Intn(500)),
		LastCheck:    time.Now(),
		AuthRequired: authRequired,
	}

	if authRequired {
		proxy.Username = GenerateRandomString(8)
		proxy.Password = GenerateRandomString(8)
		proxy.AuthType = "auth"
	} else {
		proxy.AuthType = "noauth"
	}

	return proxy
}

// GenerateRandomAppModelsTestResult 生成随机应用模型测试结果
func GenerateRandomAppModelsTestResult() *models.TestResult {
	authRequired := rand.Intn(2) == 1

	result := &models.TestResult{
		IP:           GenerateRandomIP(),
		Port:         GenerateRandomPort(),
		Status:       "可用",
		Error:        "",
		Latency:      int64(rand.Intn(500)),
		TestedAt:     time.Now(),
		RetryCount:   rand.Intn(3),
		AuthRequired: authRequired,
		AuthSuccess:  authRequired,
	}

	if authRequired {
		result.Username = GenerateRandomString(8)
		result.Password = GenerateRandomString(8)
	}

	return result
}

// GenerateRandomServiceProxyResult 生成随机服务代理结果
func GenerateRandomServiceProxyResult() *services.ProxyResult {
	authRequired := rand.Intn(2) == 1

	result := &services.ProxyResult{
		IP:           GenerateRandomIP(),
		Port:         GenerateRandomPort(),
		Status:       "可用",
		LastCheck:    time.Now(),
		Latency:      int64(rand.Intn(500)),
		CreatedAt:    time.Now(),
		AuthRequired: authRequired,
	}

	if authRequired {
		result.Username = GenerateRandomString(8)
		result.Password = GenerateRandomString(8)
		result.AuthType = "auth"
	} else {
		result.AuthType = "noauth"
	}

	return result
}

// GenerateRandomConfig 生成随机配置
func GenerateRandomConfig() *config.Config {
	cfg := config.DefaultConfig()

	// 设置随机数据库路径
	cfg.Database.Path = fmt.Sprintf("%s.db", GenerateRandomString(8))

	// 设置随机超时时间
	cfg.Proxy.TimeoutSeconds = 1 + rand.Intn(10)

	// 设置随机测试目标
	targetCount := 1 + rand.Intn(3)
	targets := make([]string, targetCount)

	for i := 0; i < targetCount; i++ {
		targets[i] = fmt.Sprintf("%s:%s", GenerateRandomString(8), GenerateRandomPort())
	}
	cfg.Proxy.TestTargets = targets

	return cfg
}
