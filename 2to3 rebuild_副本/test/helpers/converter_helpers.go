// Package helpers 提供测试辅助函数
package helpers

import (
	"reflect"
	"strconv"
	"testing"
	"time"

	"github.com/2to3rebuild/internal/app/models"
	"github.com/2to3rebuild/internal/app/services"
	"github.com/2to3rebuild/internal/config"
	"github.com/2to3rebuild/internal/proxy"
	"github.com/2to3rebuild/internal/ui/menu"
	uimodels "github.com/2to3rebuild/internal/ui/models"
)

// AssertEqual 断言两个值相等
func AssertEqual(t *testing.T, expected, actual interface{}, message string) {
	t.Helper()
	if !reflect.DeepEqual(expected, actual) {
		t.<PERSON><PERSON>rf("%s: expected %v, got %v", message, expected, actual)
	}
}

// AssertNotEqual 断言两个值不相等
func AssertNotEqual(t *testing.T, expected, actual interface{}, message string) {
	t.Helper()
	if reflect.DeepEqual(expected, actual) {
		t.<PERSON><PERSON><PERSON>("%s: expected %v to be different from %v", message, expected, actual)
	}
}

// AssertNil 断言值为nil
func AssertNil(t *testing.T, actual interface{}, message string) {
	t.Helper()
	if actual != nil && !reflect.ValueOf(actual).IsNil() {
		t.Errorf("%s: expected nil, got %v", message, actual)
	}
}

// AssertNotNil 断言值不为nil
func AssertNotNil(t *testing.T, actual interface{}, message string) {
	t.Helper()
	if actual == nil || reflect.ValueOf(actual).IsNil() {
		t.Errorf("%s: expected not nil, got nil", message)
	}
}

// CreateTestProxyModel 创建测试用的代理模型
func CreateTestProxyModel() *uimodels.ProxyModel {
	return &uimodels.ProxyModel{
		IP:           "***********",
		Port:         "8080",
		Status:       "可用",
		Latency:      100,
		TestedAt:     time.Now(),
		Username:     "testuser",
		Password:     "testpass",
		AuthType:     "auth",
		AuthRequired: true,
		Error:        "",
		RetryCount:   0,
		CreatedAt:    time.Now(),
	}
}

// CreateTestConfigModel 创建测试用的配置模型
func CreateTestConfigModel() *uimodels.ConfigModel {
	return &uimodels.ConfigModel{
		TimeoutSeconds: 5,
		TestTargets:    []string{"ipinfo.io:80", "google.com:80"},
		DatabasePath:   "test.db",
		Credentials: []uimodels.CredentialModel{
			{Username: "user1", Password: "pass1"},
			{Username: "user2", Password: "pass2"},
		},
	}
}

// CreateTestProxyResult 创建测试用的代理测试结果
func CreateTestProxyResult() *proxy.TestResult {
	return &proxy.TestResult{
		IP:           "***********",
		Port:         "8080",
		Username:     "testuser",
		Password:     "testpass",
		Status:       "可用",
		Error:        "",
		Latency:      100,
		TestedAt:     time.Now(),
		RetryCount:   0,
		AuthRequired: true,
		AuthSuccess:  true,
	}
}

// CreateTestMenuProxy 创建测试用的菜单代理
func CreateTestMenuProxy() *menu.Proxy {
	return &menu.Proxy{
		IP:           "***********",
		Port:         "8080",
		Username:     "testuser",
		Password:     "testpass",
		Status:       "可用",
		Latency:      100,
		LastCheck:    time.Now(),
		AuthType:     "auth",
		AuthRequired: true,
	}
}

// CreateTestAppModelsTestResult 创建测试用的应用模型测试结果
func CreateTestAppModelsTestResult() *models.TestResult {
	return &models.TestResult{
		IP:           "***********",
		Port:         "8080",
		Username:     "testuser",
		Password:     "testpass",
		Status:       "可用",
		Error:        "",
		Latency:      100,
		TestedAt:     time.Now(),
		RetryCount:   0,
		AuthRequired: true,
		AuthSuccess:  true,
	}
}

// CreateTestServiceProxyResult 创建测试用的服务代理结果
func CreateTestServiceProxyResult() *services.ProxyResult {
	return &services.ProxyResult{
		IP:           "***********",
		Port:         "8080",
		Username:     "testuser",
		Password:     "testpass",
		AuthType:     "auth",
		AuthRequired: true,
		Status:       "可用",
		LastCheck:    time.Now(),
		Latency:      100,
		CreatedAt:    time.Now(),
	}
}

// CreateTestConfig 创建测试用的配置
func CreateTestConfig() *config.Config {
	cfg := config.DefaultConfig()
	cfg.Database.Path = "test.db"
	cfg.Proxy.TimeoutSeconds = 5
	cfg.Proxy.TestTargets = []string{"ipinfo.io:80", "google.com:80"}
	return cfg
}

// FormatInt 将整数格式化为字符串
func FormatInt(i int) string {
	return strconv.Itoa(i)
}
