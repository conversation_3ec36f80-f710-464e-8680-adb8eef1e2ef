// Package metrics 提供性能监控和指标收集
package metrics

import (
	"sync"
	"time"
)

// MetricType 指标类型
type MetricType string

const (
	// MetricTypeCounter 计数器类型
	MetricTypeCounter MetricType = "COUNTER"
	// MetricTypeGauge 仪表类型
	MetricTypeGauge MetricType = "GAUGE"
	// MetricTypeHistogram 直方图类型
	MetricTypeHistogram MetricType = "HISTOGRAM"
)

// Metric 指标接口
type Metric interface {
	Name() string
	Type() MetricType
	Value() interface{}
}

// Counter 计数器
type Counter struct {
	name  string
	value int64
	mu    sync.Mutex
}

// NewCounter 创建新的计数器
func NewCounter(name string) *Counter {
	return &Counter{
		name:  name,
		value: 0,
	}
}

// Name 获取计数器名称
func (c *Counter) Name() string {
	return c.name
}

// Type 获取计数器类型
func (c *Counter) Type() MetricType {
	return MetricTypeCounter
}

// Value 获取计数器值
func (c *Counter) Value() interface{} {
	c.mu.Lock()
	defer c.mu.Unlock()
	return c.value
}

// Inc 增加计数器值
func (c *Counter) Inc() {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.value++
}

// Add 增加计数器值
func (c *Counter) Add(delta int64) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.value += delta
}

// Gauge 仪表
type Gauge struct {
	name  string
	value float64
	mu    sync.Mutex
}

// NewGauge 创建新的仪表
func NewGauge(name string) *Gauge {
	return &Gauge{
		name:  name,
		value: 0,
	}
}

// Name 获取仪表名称
func (g *Gauge) Name() string {
	return g.name
}

// Type 获取仪表类型
func (g *Gauge) Type() MetricType {
	return MetricTypeGauge
}

// Value 获取仪表值
func (g *Gauge) Value() interface{} {
	g.mu.Lock()
	defer g.mu.Unlock()
	return g.value
}

// Set 设置仪表值
func (g *Gauge) Set(value float64) {
	g.mu.Lock()
	defer g.mu.Unlock()
	g.value = value
}

// Timer 计时器
type Timer struct {
	name      string
	startTime time.Time
	duration  time.Duration
}

// NewTimer 创建新的计时器
func NewTimer(name string) *Timer {
	return &Timer{
		name:      name,
		startTime: time.Now(),
	}
}

// Stop 停止计时器
func (t *Timer) Stop() time.Duration {
	t.duration = time.Since(t.startTime)
	return t.duration
}

// Duration 获取计时器持续时间
func (t *Timer) Duration() time.Duration {
	if t.duration == 0 {
		return time.Since(t.startTime)
	}
	return t.duration
}

// Registry 指标注册表
type Registry struct {
	metrics map[string]Metric
	mu      sync.RWMutex
}

// NewRegistry 创建新的指标注册表
func NewRegistry() *Registry {
	return &Registry{
		metrics: make(map[string]Metric),
	}
}

// Register 注册指标
func (r *Registry) Register(metric Metric) {
	r.mu.Lock()
	defer r.mu.Unlock()
	r.metrics[metric.Name()] = metric
}

// Get 获取指标
func (r *Registry) Get(name string) Metric {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.metrics[name]
}

// GetAll 获取所有指标
func (r *Registry) GetAll() map[string]Metric {
	r.mu.RLock()
	defer r.mu.RUnlock()
	result := make(map[string]Metric, len(r.metrics))
	for k, v := range r.metrics {
		result[k] = v
	}
	return result
}

// 全局指标注册表
var globalRegistry = NewRegistry()

// Register 注册指标
func Register(metric Metric) {
	globalRegistry.Register(metric)
}

// Get 获取指标
func Get(name string) Metric {
	return globalRegistry.Get(name)
}

// GetAll 获取所有指标
func GetAll() map[string]Metric {
	return globalRegistry.GetAll()
}
