// Package errors 提供自定义错误类型和错误处理函数
package errors

import (
	"fmt"
)

// ErrorType 错误类型枚举
type ErrorType string

const (
	// ErrorTypeDatabase 数据库错误
	ErrorTypeDatabase ErrorType = "DATABASE_ERROR"
	// ErrorTypeProxy 代理错误
	ErrorTypeProxy ErrorType = "PROXY_ERROR"
	// ErrorTypeConfig 配置错误
	ErrorTypeConfig ErrorType = "CONFIG_ERROR"
	// ErrorTypeNetwork 网络错误
	ErrorTypeNetwork ErrorType = "NETWORK_ERROR"
	// ErrorTypeAuth 认证错误
	ErrorTypeAuth ErrorType = "AUTH_ERROR"
)

// AppError 应用程序错误
type AppError struct {
	Type    ErrorType // 错误类型
	Message string    // 错误消息
	Err     error     // 原始错误
}

// Error 实现 error 接口
func (e *AppError) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("%s: %s (%v)", e.Type, e.Message, e.Err)
	}
	return fmt.Sprintf("%s: %s", e.Type, e.Message)
}

// Unwrap 返回原始错误
func (e *AppError) Unwrap() error {
	return e.Err
}

// NewDatabaseError 创建数据库错误
func NewDatabaseError(message string, err error) *AppError {
	return &AppError{
		Type:    ErrorTypeDatabase,
		Message: message,
		Err:     err,
	}
}

// NewProxyError 创建代理错误
func NewProxyError(message string, err error) *AppError {
	return &AppError{
		Type:    ErrorTypeProxy,
		Message: message,
		Err:     err,
	}
}

// NewConfigError 创建配置错误
func NewConfigError(message string, err error) *AppError {
	return &AppError{
		Type:    ErrorTypeConfig,
		Message: message,
		Err:     err,
	}
}

// NewNetworkError 创建网络错误
func NewNetworkError(message string, err error) *AppError {
	return &AppError{
		Type:    ErrorTypeNetwork,
		Message: message,
		Err:     err,
	}
}

// NewAuthError 创建认证错误
func NewAuthError(message string, err error) *AppError {
	return &AppError{
		Type:    ErrorTypeAuth,
		Message: message,
		Err:     err,
	}
}
