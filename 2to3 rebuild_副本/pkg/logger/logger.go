// Package logger 提供统一的日志处理接口
package logger

import (
	"fmt"
	"io"
	"log"
	"os"
	"time"
)

// LogLevel 日志级别
type LogLevel int

const (
	// DEBUG 调试级别
	DEBUG LogLevel = iota
	// INFO 信息级别
	INFO
	// WARN 警告级别
	WARN
	// ERROR 错误级别
	ERROR
	// FATAL 致命错误级别
	FATAL
)

// Logger 日志记录器接口
type Logger interface {
	Debug(format string, args ...interface{})
	Info(format string, args ...interface{})
	Warn(format string, args ...interface{})
	Error(format string, args ...interface{})
	Fatal(format string, args ...interface{})
	SetLevel(level LogLevel)
	SetOutput(w io.Writer)
}

// DefaultLogger 默认日志记录器
type DefaultLogger struct {
	level  LogLevel
	logger *log.Logger
}

// NewDefaultLogger 创建新的默认日志记录器
func NewDefaultLogger() *DefaultLogger {
	return &DefaultLogger{
		level:  INFO,
		logger: log.New(os.Stdout, "", log.LstdFlags),
	}
}

// SetLevel 设置日志级别
func (l *DefaultLogger) SetLevel(level LogLevel) {
	l.level = level
}

// SetOutput 设置输出目标
func (l *DefaultLogger) SetOutput(w io.Writer) {
	l.logger = log.New(w, "", log.LstdFlags)
}

// Debug 记录调试级别日志
func (l *DefaultLogger) Debug(format string, args ...interface{}) {
	if l.level <= DEBUG {
		l.log("DEBUG", format, args...)
	}
}

// Info 记录信息级别日志
func (l *DefaultLogger) Info(format string, args ...interface{}) {
	if l.level <= INFO {
		l.log("INFO", format, args...)
	}
}

// Warn 记录警告级别日志
func (l *DefaultLogger) Warn(format string, args ...interface{}) {
	if l.level <= WARN {
		l.log("WARN", format, args...)
	}
}

// Error 记录错误级别日志
func (l *DefaultLogger) Error(format string, args ...interface{}) {
	if l.level <= ERROR {
		l.log("ERROR", format, args...)
	}
}

// Fatal 记录致命错误级别日志
func (l *DefaultLogger) Fatal(format string, args ...interface{}) {
	if l.level <= FATAL {
		l.log("FATAL", format, args...)
		os.Exit(1)
	}
}

// log 记录日志
func (l *DefaultLogger) log(level, format string, args ...interface{}) {
	timestamp := time.Now().Format("2006-01-02 15:04:05")
	message := fmt.Sprintf(format, args...)
	l.logger.Printf("[%s] %s: %s", timestamp, level, message)
}

// 全局日志记录器
var globalLogger Logger = NewDefaultLogger()

// SetGlobalLogger 设置全局日志记录器
func SetGlobalLogger(logger Logger) {
	globalLogger = logger
}

// GetGlobalLogger 获取全局日志记录器
func GetGlobalLogger() Logger {
	return globalLogger
}

// 全局函数

// Debug 记录调试级别日志
func Debug(format string, args ...interface{}) {
	globalLogger.Debug(format, args...)
}

// Info 记录信息级别日志
func Info(format string, args ...interface{}) {
	globalLogger.Info(format, args...)
}

// Warn 记录警告级别日志
func Warn(format string, args ...interface{}) {
	globalLogger.Warn(format, args...)
}

// Error 记录错误级别日志
func Error(format string, args ...interface{}) {
	globalLogger.Error(format, args...)
}

// Fatal 记录致命错误级别日志
func Fatal(format string, args ...interface{}) {
	globalLogger.Fatal(format, args...)
}

// SetLevel 设置日志级别
func SetLevel(level LogLevel) {
	globalLogger.SetLevel(level)
}

// SetOutput 设置输出目标
func SetOutput(w io.Writer) {
	globalLogger.SetOutput(w)
}
