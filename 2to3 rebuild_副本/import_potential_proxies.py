#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
可能可用代理导入工具

这个脚本用于将可能可用的代理从文本文件直接导入到数据库中，
不需要API服务器。

用法:
    python import_potential_proxies.py [选项]

选项:
    --file FILE         指定代理文件路径，默认为 ip_output-18889.txt
    --db DB             指定数据库文件路径，默认为 proxies.db
    --source SOURCE     指定代理来源，默认为 file_import
    --limit LIMIT       限制导入数量，默认为全部导入
    --help              显示帮助信息
"""

import argparse
import datetime
import os
import re
import sqlite3
import sys


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="可能可用代理导入工具")
    parser.add_argument("--file", default="ip_output-18889.txt", help="代理文件路径")
    parser.add_argument("--db", default="proxies.db", help="数据库文件路径")
    parser.add_argument("--source", default="file_import", help="代理来源")
    parser.add_argument("--limit", type=int, help="限制导入数量")
    return parser.parse_args()


def connect_db(db_path):
    """连接到SQLite数据库"""
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        return conn
    except sqlite3.Error as e:
        print(f"数据库连接错误: {e}")
        sys.exit(1)


def ensure_tables_exist(conn):
    """确保必要的表存在"""
    cursor = conn.cursor()
    
    # 检查potential_proxies表是否存在
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='potential_proxies'")
    if not cursor.fetchone():
        print("创建potential_proxies表...")
        cursor.execute('''
        CREATE TABLE potential_proxies (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            ip VARCHAR(50) NOT NULL,
            port VARCHAR(10) NOT NULL,
            source VARCHAR(100),
            location VARCHAR(100),
            discovered_at TIMESTAMP,
            tested BOOLEAN DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(ip, port)
        )
        ''')
    
    conn.commit()


def parse_proxy_file(file_path, limit=None):
    """解析代理文件"""
    if not os.path.exists(file_path):
        print(f"错误: 文件 {file_path} 不存在")
        sys.exit(1)
    
    proxies = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for i, line in enumerate(f):
                if limit and i >= limit:
                    break
                
                parts = line.strip().split(' ', 2)
                if len(parts) >= 2:
                    ip = parts[0]
                    port = parts[1]
                    location = parts[2] if len(parts) > 2 else ""
                    
                    # 验证IP格式
                    if re.match(r'^(\d{1,3}\.){3}\d{1,3}$', ip) and port.isdigit():
                        proxies.append({
                            'ip': ip,
                            'port': port,
                            'location': location
                        })
    except Exception as e:
        print(f"解析文件错误: {e}")
        sys.exit(1)
    
    return proxies


def import_potential_proxies(conn, proxies, source):
    """导入可能可用的代理到数据库"""
    cursor = conn.cursor()
    now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    success_count = 0
    error_count = 0
    
    for proxy in proxies:
        try:
            cursor.execute('''
            INSERT OR IGNORE INTO potential_proxies 
            (ip, port, source, location, discovered_at, tested) 
            VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                proxy['ip'], 
                proxy['port'], 
                source, 
                proxy['location'], 
                now, 
                0
            ))
            
            if cursor.rowcount > 0:
                success_count += 1
            else:
                print(f"代理已存在: {proxy['ip']}:{proxy['port']}")
        except sqlite3.Error as e:
            print(f"导入代理 {proxy['ip']}:{proxy['port']} 失败: {e}")
            error_count += 1
    
    conn.commit()
    return success_count, error_count


def main():
    """主函数"""
    args = parse_args()
    
    # 连接数据库
    conn = connect_db(args.db)
    ensure_tables_exist(conn)
    
    # 解析代理文件
    proxies = parse_proxy_file(args.file, args.limit)
    print(f"从文件 {args.file} 中解析出 {len(proxies)} 个代理")
    
    # 导入可能可用的代理
    success_count, error_count = import_potential_proxies(conn, proxies, args.source)
    print(f"成功导入 {success_count} 个代理，失败 {error_count} 个")
    
    conn.close()
    print("操作完成")


if __name__ == "__main__":
    main()
