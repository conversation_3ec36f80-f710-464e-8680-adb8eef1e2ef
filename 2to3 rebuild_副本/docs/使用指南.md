# SOCKS5代理测试工具使用指南

**版本**: v1.1.0
**最后更新**: 当前
**文档状态**: 稳定

## 1. 安装与配置

### 1.1 从源码安装

```bash
# 克隆仓库
git clone https://github.com/yourusername/socks5-proxy-tester.git
cd socks5-proxy-tester

# 编译
go build -o socks5tester.exe
```

### 1.2 配置文件

配置文件使用JSON格式，默认为`config.json`：

```json
{
  "database": {
    "path": "proxies.db"
  },
  "proxy": {
    "timeout_seconds": 5,
    "test_targets": [
      "ipinfo.io:80",
      "google.com:80"
    ],
    "credentials": [
      {
        "username": "user1",
        "password": "pass1"
      }
    ]
  }
}
```

## 2. 使用方法

### 2.1 命令行参数

```
socks5tester [options]

选项:
  -config string   配置文件路径 (默认 "config.json")
  -db string       SQLite数据库文件路径 (默认 "proxies.db")
  -ip string       要测试的代理IP
  -port string     要测试的代理端口
  -i               启用交互式菜单
  -api int         启动API服务器的端口，0表示不启动
```

### 2.2 交互式菜单

启动交互式菜单：

```bash
socks5tester -i
```

交互式菜单提供以下功能：

1. **测试单个代理** - 输入代理信息，显示测试结果
2. **批量测试代理** - 从文件加载代理列表，并发测试
3. **测试数据库代理** - 测试数据库中的代理，更新状态
4. **查看代理列表** - 显示数据库中的代理列表
5. **导出代理** - 按条件筛选并导出代理
6. **数据库管理** - 执行SQL命令，查询和修改数据库
7. **配置设置** - 设置超时时间、测试目标等

### 2.3 命令行模式

```bash
# 测试单个代理
socks5tester -ip *********** -port 1080

# 使用自定义配置和数据库
socks5tester -config my-config.json -db my-proxies.db -i

# 启动API服务器
socks5tester -api 8080

# 同时启动API服务器和交互式菜单
socks5tester -api 8080 -i
```

## 3. 代理列表文件格式

代理列表文件使用简单的文本格式，每行一个代理：

```
IP PORT [USERNAME PASSWORD]

示例：
*********** 1080
******** 8080 user1 pass1
******** 8080 admin password123
```

## 4. 数据库管理

### 4.1 使用数据库管理功能

在交互式菜单中选择"数据库管理"选项，进入SQL命令执行界面。

### 4.2 特殊命令

| 命令 | 描述 |
|------|------|
| `tables` | 显示所有表 |
| `schema 表名` | 显示指定表的结构 |
| `exit` 或 `quit` | 退出数据库管理界面 |

### 4.3 常用SQL命令

```sql
-- 查询所有代理
SELECT * FROM proxies

-- 查询可用的代理
SELECT * FROM proxies WHERE status = '可用'

-- 按延迟排序
SELECT * FROM proxies ORDER BY latency ASC

-- 更新代理状态
UPDATE proxies SET status = '不可用' WHERE ip = '***********'

-- 删除代理
DELETE FROM proxies WHERE ip = '***********' AND port = '8080'
```

## 5. 常见问题

### 5.1 代理测试失败
- 检查代理服务器是否在线
- 检查网络连接
- 增加超时时间
- 检查认证信息是否正确

### 5.2 数据库错误
- 检查数据库文件权限
- 确保SQLite驱动正确安装
- 尝试重新初始化数据库

### 5.3 配置加载失败
- 检查配置文件格式是否正确
- 确保配置文件路径正确
- 使用默认配置文件路径

## 6. API接口

### 6.1 启动API服务器

```bash
# 启动API服务器，监听8080端口
socks5tester -api 8080
```

### 6.2 API端点

#### 获取代理列表

```
GET /api/proxies?status=可用&limit=10
```

参数:
- `status`: 代理状态（可用、不可用、所有）
- `limit`: 返回结果数量限制

响应:
```json
{
  "success": true,
  "count": 2,
  "proxies": [
    {
      "ip": "***********",
      "port": "1080",
      "status": "可用",
      "latency": 100,
      "auth_required": false
    },
    {
      "ip": "***********",
      "port": "8080",
      "username": "user",
      "password": "pass",
      "status": "可用",
      "latency": 150,
      "auth_required": true
    }
  ]
}
```

#### 添加代理

```
POST /api/proxies
```

请求体:
```json
{
  "proxies": [
    {"ip": "***********", "port": "1080"},
    {"ip": "***********", "port": "8080", "source": "manual"}
  ]
}
```

响应:
```json
{
  "success": true,
  "count": 2,
  "message": "成功添加2个代理"
}
```

#### 测试代理

```
POST /api/proxies/test
```

请求体:
```json
{
  "proxies": [
    {"ip": "***********", "port": "1080"},
    {"ip": "***********", "port": "8080", "username": "user", "password": "pass"}
  ],
  "concurrent": 5
}
```

响应:
```json
{
  "success": true,
  "results": [
    {
      "ip": "***********",
      "port": "1080",
      "status": "可用",
      "latency": 100
    },
    {
      "ip": "***********",
      "port": "8080",
      "status": "不可用",
      "error": "连接超时"
    }
  ]
}
```

#### 导出代理

```
GET /api/proxies/export?status=可用&format=url&retest=true
```

参数:
- `status`: 代理状态（可用、不可用、所有）
- `format`: 导出格式（basic、url）
- `retest`: 是否在导出前重新测试（true、false）

响应:
```json
{
  "success": true,
  "count": 2,
  "format": "url",
  "proxies": [
    "socks5://***********:1080",
    "socks5://user:pass@***********:8080"
  ]
}
```

## 7. 高级用法

### 7.1 批量测试优化

对于大量代理的批量测试，可以调整并发数和超时设置：

```bash
# 使用自定义并发数和超时
socks5tester -i
# 在配置设置中调整:
# - 并发线程数: 建议设置为CPU核心数的2-4倍
# - 超时时间: 对于不稳定网络可增加到10秒
```

### 7.2 数据导出功能

使用交互式菜单中的"导出代理"功能，可以按条件筛选并导出代理：

1. 选择代理状态（所有、可用、不可用）
2. 选择是否在导出前重新测试代理
3. 选择导出格式（基本格式、URL格式）
4. 指定输出文件路径

### 7.3 数据库代理测试

使用交互式菜单中的"测试数据库代理"功能，可以测试数据库中的代理：

1. 选择要测试的代理（所有、可用、不可用）
2. 设置并发测试数量
3. 选择是否更新代理状态

### 7.4 自动化测试

可以结合系统定时任务实现自动化测试：

```bash
# Windows计划任务示例 - 测试单个代理
socks5tester -ip *********** -port 1080 > test_result.log

# Windows计划任务示例 - 启动API服务器
socks5tester -api 8080
```
