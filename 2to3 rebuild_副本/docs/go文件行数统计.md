# Go文件行数统计

下表列出了项目中所有Go文件的行数，按照行数从多到少排序。

| 文件路径 | 行数 |
|---------|------|
| internal/ui/menu/handlers.go | 285 |
| internal/ui/menu/database.go | 265 |
| internal/proxy/tester.go | 245 |
| test/unit/sqlite_test.go | 231 |
| test/unit/proxy_test.go | 217 |
| internal/ui/menu/input.go | 207 |
| cmd/flags.go | 196 |
| pkg/metrics/metrics.go | 196 |
| internal/ui/menu.go | 187 |
| internal/ui/menu/batch_test.go | 187 |
| test/unit/concurrent_test.go | 182 |
| cmd/main.go | 174 |
| pkg/logger/logger.go | 154 |
| cmd/import_credentials/main.go | 137 |
| internal/app/adapters/proxy.go | 133 |
| internal/config/config.go | 119 |
| internal/database/sqlite/proxy_ops.go | 117 |
| internal/ui/menu/proxy_test.go | 112 |
| internal/database/sqlite/db.go | 106 |
| internal/ui/menu/core.go | 102 |
| internal/app/core/app.go | 101 |
| internal/ui/menu/utils.go | 96 |
| internal/proxy/retry.go | 95 |
| internal/proxy/auth.go | 90 |
| internal/ui/menu/display.go | 89 |
| pkg/errors/errors.go | 87 |
| internal/ui/menu/config.go | 86 |
| cmd/test_single_proxy.go | 83 |
| cmd/test_all_proxies.go | 82 |
| internal/database/interface.go | 67 |
| internal/database/sqlite/credential_ops.go | 64 |
| internal/proxy/models.go | 62 |
| internal/ui/menu/proxy_list.go | 60 |
| internal/app/adapters/config.go | 58 |
| internal/app/types/types.go | 58 |
| internal/app/core/lifecycle.go | 55 |
| test/unit/init_test.go | 55 |
| update_db.go | 50 |
| internal/database/migrations/update_db.go | 50 |
| config.go | 49 |
| internal/ui/types/types.go | 46 |
| main.go | 42 |
| internal/proxy/types.go | 40 |
| internal/database/sqlite/transaction.go | 34 |
| internal/ui/menu/menu.go | 20 |
| internal/app/app.go | 14 |

## 文件分组统计

### 按目录分组

| 目录 | 文件数 | 总行数 | 平均行数 |
|------|-------|-------|---------|
| internal/ui/menu/ | 11 | 1509 | 137.2 |
| test/unit/ | 4 | 685 | 171.2 |
| cmd/ | 4 | 535 | 133.8 |
| internal/proxy/ | 5 | 532 | 106.4 |
| internal/database/sqlite/ | 4 | 321 | 80.2 |
| pkg/metrics/ | 1 | 196 | 196.0 |
| internal/app/adapters/ | 2 | 191 | 95.5 |
| internal/ui/ | 1 | 187 | 187.0 |
| internal/app/core/ | 2 | 156 | 78.0 |
| pkg/logger/ | 1 | 154 | 154.0 |
| 根目录 | 3 | 141 | 47.0 |
| cmd/import_credentials/ | 1 | 137 | 137.0 |
| internal/config/ | 1 | 119 | 119.0 |
| pkg/errors/ | 1 | 87 | 87.0 |
| internal/database/ | 1 | 67 | 67.0 |
| internal/app/types/ | 1 | 58 | 58.0 |
| internal/database/migrations/ | 1 | 50 | 50.0 |
| internal/ui/types/ | 1 | 46 | 46.0 |
| internal/app/ | 1 | 14 | 14.0 |

### 按功能分组

| 功能模块 | 文件数 | 总行数 | 平均行数 |
|---------|-------|-------|---------|
| 用户界面 (internal/ui/) | 13 | 1742 | 134.0 |
| 测试代码 (test/unit/) | 4 | 685 | 171.2 |
| 命令行工具 (cmd/) | 5 | 672 | 134.4 |
| 代理测试 (internal/proxy/) | 5 | 532 | 106.4 |
| 数据库操作 (internal/database/) | 6 | 438 | 73.0 |
| 公共包 (pkg/) | 3 | 437 | 145.7 |
| 应用程序核心 (internal/app/) | 6 | 419 | 69.8 |
| 核心功能 (根目录) | 3 | 141 | 47.0 |
| 配置管理 (internal/config/) | 1 | 119 | 119.0 |

## 分析与建议

1. **大文件拆分**：
   - `internal/ui/menu/handlers.go` (285行) - 可以考虑将不同的功能拆分为单独的文件
   - `internal/ui/menu/database.go` (265行) - 可以考虑将不同的功能拆分为单独的文件
   - `internal/proxy/tester.go` (245行) - 可以考虑将不同的功能拆分为单独的文件

2. **代码重构机会**：
   - 用户界面代码占比较大 (1742行)，可以考虑进一步模块化
   - 测试代码行数较多 (685行)，可以考虑更多的测试辅助函数减少重复代码

3. **文件组织优化**：
   - 根目录下有3个Go文件，可以考虑移动到相应的内部包中
   - 确保每个包都有明确的职责和边界
   - 考虑使用更多的接口来降低包之间的耦合度

4. **测试覆盖**：
   - 确保所有核心功能都有对应的测试文件
