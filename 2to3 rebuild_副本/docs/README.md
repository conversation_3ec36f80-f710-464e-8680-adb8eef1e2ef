# SOCKS5代理测试工具

**版本**: v1.0.0
**最后更新**: 2025-05-15
**文档状态**: 稳定

## 项目概述

SOCKS5代理测试工具是一个用Go语言开发的命令行工具，用于测试SOCKS5代理的可用性和性能。该工具支持无认证和带认证的代理测试，并提供交互式菜单和批量测试功能。测试结果存储在SQLite数据库中，便于后续查询和分析。

## 文档导航

- **[项目文档.md](项目文档.md)** - 详细的项目架构、技术细节和项目结构分析
- **[使用指南.md](使用指南.md)** - 具体的使用方法和常见问题解答
- **[开发指南.md](开发指南.md)** - 开发环境设置、包结构指南、编码规范和数据库管理
- **[progress_tracking.md](progress_tracking.md)** - 项目开发进度、任务分解和工作日志
- **[sqlite_commands.md](sqlite_commands.md)** - 数据库管理SQL命令参考
- **[go文件行数统计.md](go文件行数统计.md)** - 代码行数统计和分析
- **[bug_fix_report.md](bug_fix_report.md)** - Bug修复报告和解决方案

## 主要功能

1. **代理测试** - 无认证/带认证测试、超时重试、并发控制
2. **命令行工具** - 参数解析、配置管理、格式化输出
3. **数据管理** - SQLite存储、测试结果记录、数据导入导出
4. **用户界面** - 交互式菜单、数据库管理、配置设置
5. **代理导出** - 按条件筛选、多种导出格式、导出前重新测试
6. **数据库代理测试** - 批量测试数据库中的代理、并发控制、状态更新
7. **API接口** - 添加代理、获取代理列表、测试代理、导出代理

## 快速开始

### 安装

```bash
# 克隆仓库
git clone https://github.com/yourusername/socks5-proxy-tester.git
cd socks5-proxy-tester

# 编译
go build -o socks5tester.exe
```

### 使用方法

```bash
# 启动交互式菜单
socks5tester -i

# 测试单个代理
socks5tester -ip *********** -port 1080

# 使用自定义配置和数据库
socks5tester -config my-config.json -db my-proxies.db -i

# 启动API服务器
socks5tester -api 8080

# 同时启动API服务器和交互式菜单
socks5tester -api 8080 -i
```

### API接口

```
# 获取代理列表
GET /api/proxies?status=可用&limit=10

# 添加代理
POST /api/proxies
{
  "proxies": [
    {"ip": "***********", "port": "1080"},
    {"ip": "***********", "port": "8080"}
  ]
}

# 测试代理
POST /api/proxies/test
{
  "proxies": [
    {"ip": "***********", "port": "1080"},
    {"ip": "***********", "port": "8080", "username": "user", "password": "pass"}
  ],
  "concurrent": 5
}

# 导出代理
GET /api/proxies/export?status=可用&format=url&retest=true
```

### 标准配置文件

```json
{
  "database": {
    "path": "proxies.db"
  },
  "proxy": {
    "timeout_seconds": 5,
    "test_targets": [
      "ipinfo.io:80",
      "google.com:80"
    ],
    "credentials": [
      {
        "username": "user1",
        "password": "pass1"
      }
    ]
  }
}
```

## 技术栈

- 语言：Go 1.19+
- 数据库：SQLite 3
- 主要依赖：golang.org/x/net/proxy, database/sql, modernc.org/sqlite, github.com/AlecAivazis/survey/v2

## 文档更新指南

1. 所有文档应使用Markdown格式
2. 图表可以使用PlantUML或Mermaid语法
3. 代码示例应包含注释
4. 文档更新应与代码更改同步

## 最近更新

- **当前**: 实现新功能框架（代理导出、数据库代理测试、API接口），修复编译错误
- **2025-05-15**: 整合文档，减少文档数量，更新项目结构分析
- **2025-05-12**: 修复编译错误，解决重复的main函数声明问题
- **2025-05-04**: 优化项目结构，添加pkg、docs、tools目录
- **2025-05-03**: 完成目录结构优化，修复编译错误
- **2025-05-02**: 添加UI数据库管理功能，创建SQL命令参考文档
