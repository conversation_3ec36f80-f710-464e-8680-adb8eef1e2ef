# SOCKS5代理测试工具项目文档

**版本**: v1.1.0
**最后更新**: 当前
**文档状态**: 稳定

## 1. 项目概述

SOCKS5代理测试工具是一个用Go语言开发的命令行工具，用于测试SOCKS5代理的可用性和性能。该工具支持无认证和带认证的代理测试，并提供交互式菜单和批量测试功能。测试结果存储在SQLite数据库中，便于后续查询和分析。

## 2. 主要功能

- **单个代理测试** - 支持无认证/带认证测试、自动重试、超时控制
- **批量代理测试** - 从文件加载代理列表、并发测试、结果统计
- **数据库代理测试** - 测试数据库中的代理、并发控制、状态更新
- **代理导出功能** - 按条件筛选、多种导出格式、导出前重新测试
- **数据管理** - SQLite存储测试结果、管理认证信息、查询历史结果
- **用户界面** - 交互式菜单、命令行参数、配置管理、数据库管理
- **API接口** - 添加代理、获取代理列表、测试代理、导出代理

## 3. 项目结构

```
.
├── cmd/                    # 命令行工具
├── internal/               # 内部包
│   ├── api/                # API服务
│   ├── app/                # 应用程序核心
│   │   ├── adapters/       # 适配器
│   │   ├── core/           # 核心逻辑
│   │   └── types/          # 类型定义
│   ├── config/             # 配置相关
│   ├── database/           # 数据库抽象和实现
│   │   ├── migrations/     # 数据库迁移
│   │   └── sqlite/         # SQLite实现
│   ├── proxy/              # 代理测试核心逻辑
│   └── ui/                 # 交互式用户界面
│       ├── menu/           # 菜单实现
│       └── types/          # 类型定义
├── pkg/                    # 公共包
│   ├── errors/             # 错误处理
│   ├── logger/             # 日志处理
│   └── metrics/            # 性能指标
├── docs/                   # 文档
├── test/                   # 测试相关
│   ├── fixtures/           # 测试数据
│   └── unit/               # 单元测试
├── tools/                  # 工具
│   ├── scripts/            # 脚本
│   └── build/              # 构建工具
├── config.json             # 配置文件
├── main.go                 # 程序入口
└── proxies.db              # SQLite数据库
```

## 4. 架构设计

### 4.1 核心组件

1. **应用程序核心 (internal/app)**
   - 负责协调各个组件
   - 管理应用程序生命周期
   - 提供适配器连接不同组件

2. **代理测试模块 (internal/proxy)**
   - 实现SOCKS5代理测试逻辑
   - 支持无认证和带认证测试
   - 提供重试和超时机制

3. **数据库模块 (internal/database)**
   - 提供数据存储接口
   - 实现SQLite存储
   - 管理数据库连接和事务
   - 存储可能可用的代理

4. **用户界面模块 (internal/ui)**
   - 提供交互式菜单
   - 处理用户输入
   - 展示测试结果
   - 支持代理导出和数据库代理测试

5. **API服务模块 (internal/api)**
   - 提供RESTful API接口
   - 支持添加、获取、测试、导出代理
   - 与应用程序核心集成

### 4.2 数据流

1. **测试流程**
   - 用户输入 -> UI模块 -> 应用程序核心 -> 代理测试模块 -> 数据库模块
   - 测试结果 -> 数据库模块 -> 应用程序核心 -> UI模块 -> 用户输出

2. **配置流程**
   - 配置文件 -> 配置模块 -> 应用程序核心 -> 各功能模块
   - 用户配置 -> UI模块 -> 配置模块 -> 配置文件

3. **导出流程**
   - 用户输入 -> UI模块 -> 应用程序核心 -> 数据库模块 -> 文件系统
   - 导出结果 -> 文件系统 -> UI模块 -> 用户输出

4. **API流程**
   - HTTP请求 -> API模块 -> 应用程序核心 -> 各功能模块
   - 处理结果 -> 应用程序核心 -> API模块 -> HTTP响应

## 5. 数据库设计

### 5.1 表结构

#### proxies 表

| 列名 | 类型 | 描述 |
|------|------|------|
| id | INTEGER | 主键 |
| ip | VARCHAR(50) | 代理IP地址 |
| port | VARCHAR(10) | 代理端口 |
| username | VARCHAR(50) | 认证用户名 |
| password | VARCHAR(50) | 认证密码 |
| auth_type | VARCHAR(20) | 认证类型(noauth, auth) |
| auth_required | BOOLEAN | 是否需要认证 |
| status | VARCHAR(20) | 代理状态 |
| last_check | TIMESTAMP | 最后检查时间 |
| latency | INTEGER | 延迟(毫秒) |
| created_at | TIMESTAMP | 创建时间 |

#### credentials 表

| 列名 | 类型 | 描述 |
|------|------|------|
| id | INTEGER | 主键 |
| username | VARCHAR(50) | 用户名 |
| password | VARCHAR(50) | 密码 |
| created_at | TIMESTAMP | 创建时间 |

#### potential_proxies 表

| 列名 | 类型 | 描述 |
|------|------|------|
| id | INTEGER | 主键 |
| ip | VARCHAR(50) | 代理IP地址 |
| port | VARCHAR(10) | 代理端口 |
| source | VARCHAR(100) | 代理来源 |
| location | VARCHAR(100) | 地理位置 |
| discovered_at | TIMESTAMP | 发现时间 |
| tested | BOOLEAN | 是否已测试 |
| created_at | TIMESTAMP | 创建时间 |

## 6. 已知限制和未来计划

### 已知限制
- 目前只支持SOCKS5代理
- 数据库使用SQLite，不适合大规模并发访问
- 认证信息以明文存储，存在安全风险

### 未来计划
- 完善代理导出功能的业务逻辑
- 完善数据库代理测试功能的业务逻辑
- 完善API接口的业务逻辑
- 实现文件锁和同步机制
- 添加更多的代理类型支持
- 改进用户界面，添加进度条和彩色输出
- 增强数据分析功能，添加统计报表
- 实现Web界面，提供更友好的操作体验
- 添加同步机制，支持多设备数据同步

## 7. 项目文件分析

### 7.1 当前项目结构分析

本节提供了SOCKS5代理测试工具项目的详细文件分析，包括每个Go文件的用途以及已完成的项目结构优化。

#### 7.1.1 根目录文件

| 文件名 | 用途 | 状态 |
|-------|------|------|
| **main.go** | 主程序入口点，解析命令行参数并启动应用程序 | 已保留，作为程序的主入口点 |
| **config.go** | 定义配置结构和加载配置的函数 | 已移动到 `internal/config` 目录并合并 |
| **update_db.go** | 数据库更新相关功能 | 已删除，功能已存在于 `internal/database/migrations` 目录 |

#### 7.1.2 cmd 目录

| 文件名 | 用途 | 状态 |
|-------|------|------|
| **cmd/main.go** | 命令行工具的入口点 | 已移动到 `cmd/socks5tester/main.go` |
| **cmd/flags.go** | 命令行参数解析 | 已移动到 `cmd/socks5tester/flags.go` |
| **cmd/test_all_proxies.go** | 批量测试代理的命令行工具 | 已移动到 `cmd/socks5tester/test_all_proxies.go` |
| **cmd/test_single_proxy.go** | 测试单个代理的命令行工具 | 已移动到 `cmd/socks5tester/test_single_proxy.go` |
| **cmd/import_credentials/main.go** | 导入认证信息的工具 | 已保留，作为独立工具 |

#### 7.1.3 internal/app 包

| 文件名 | 用途 | 状态 |
|-------|------|------|
| **internal/app/app.go** | 应用程序包的入口点，导出 core 包中的 App 类型 | 已保留，作为应用程序的主要接口 |
| **internal/app/adapters/config.go** | 配置适配器实现 | 已保留，用于组件之间的连接 |
| **internal/app/adapters/proxy.go** | 代理测试器适配器实现 | 已保留，用于组件之间的连接 |
| **internal/app/core/app.go** | 应用程序核心实现 | 已保留，作为应用程序的核心 |
| **internal/app/core/lifecycle.go** | 应用程序生命周期管理 | 已保留，用于应用程序生命周期管理 |
| **internal/app/types/types.go** | 应用程序类型定义 | 已保留，用于类型定义 |

#### 7.1.4 internal/ui 包

| 文件名 | 用途 | 状态 |
|-------|------|------|
| **internal/ui/menu.go** | UI 菜单的包装器 | 已保留，作为 UI 的主要接口 |
| **internal/ui/menu/batch_test.go** | 批量测试菜单功能 | 已保留，作为菜单功能的具体实现 |
| **internal/ui/menu/config.go** | 配置菜单功能 | 已保留，作为菜单功能的具体实现 |
| **internal/ui/menu/core.go** | 菜单核心功能 | 已保留，作为菜单功能的具体实现 |
| **internal/ui/menu/database.go** | 数据库管理菜单功能 | 已保留，作为菜单功能的具体实现 |
| **internal/ui/menu/display.go** | 显示相关功能 | 已保留，作为菜单功能的具体实现 |
| **internal/ui/menu/handlers.go** | 菜单事件处理 | 已保留，作为菜单功能的具体实现 |
| **internal/ui/menu/input.go** | 用户输入处理 | 已保留，作为菜单功能的具体实现 |
| **internal/ui/menu/proxy_list.go** | 代理列表显示功能 | 已保留，作为菜单功能的具体实现 |
| **internal/ui/menu/proxy_test.go** | 代理测试菜单功能 | 已保留，作为菜单功能的具体实现 |
| **internal/ui/menu/utils.go** | 菜单工具函数 | 已保留，作为菜单功能的具体实现 |
| **internal/ui/types/types.go** | UI 类型定义 | 已保留，用于类型定义 |

### 7.2 已完成的优化

#### 7.2.1 已移动的文件

1. ✅ 已将 `config.go` 移动到 `internal/config` 目录，并与现有的 `config.go` 合并
   - 添加了 Credential 类型和相关方法
   - 更新了 DefaultConfig 函数，添加了 Credentials 字段的默认值
   - 更新了 Validate 函数，添加了对 Credentials 字段的验证
   - 添加了一些辅助方法来获取和设置配置

2. ✅ 已删除 `update_db.go`，因为 `internal/database/migrations/update_db.go` 已经包含相同功能

3. ✅ 已将 `cmd/*.go` 文件移动到 `cmd/socks5tester/` 目录下
   - 将 `cmd/main.go` 复制到 `cmd/socks5tester/main.go`
   - 将 `cmd/flags.go` 复制到 `cmd/socks5tester/flags.go`
   - 将 `cmd/test_all_proxies.go` 复制到 `cmd/socks5tester/test_all_proxies.go`，并更新了命令路径
   - 将 `cmd/test_single_proxy.go` 复制到 `cmd/socks5tester/test_single_proxy.go`，并更新了数据库路径

4. ✅ 已将 `test/unit/ui/models/batch_test_model.go` 重命名为 `batch_test_model_test.go`，使其符合 Go 的测试命名约定

## 8. 最近更新

- **当前**: 实现新功能框架（代理导出、数据库代理测试、API接口），修复编译错误
- **2025-05-15**: 整合文档，减少文档数量，更新项目结构分析
- **2025-05-12**: 修复编译错误，解决重复的main函数声明问题
- **2025-05-04**: 优化项目结构，添加pkg、docs、tools目录
- **2025-05-03**: 完成目录结构优化，修复编译错误
- **2025-05-02**: 添加UI数据库管理功能，创建SQL命令参考文档
