# SQLite 命令参考

## 基本命令

### 特殊命令

这些是我们在数据库管理界面中实现的特殊命令：

| 命令 | 描述 |
|------|------|
| `tables` | 显示所有表 |
| `schema 表名` | 显示指定表的结构 |
| `exit` 或 `quit` | 退出数据库管理界面 |

### 表操作命令

#### 查看表结构

```sql
-- 查看表结构
PRAGMA table_info(proxies);
PRAGMA table_info(credentials);
```

#### 查看数据库信息

```sql
-- 查看数据库列表
PRAGMA database_list;

-- 查看数据库统计信息
PRAGMA stats;
```

## 查询命令

### 基本查询

```sql
-- 查询所有代理
SELECT * FROM proxies;

-- 查询前5条记录
SELECT * FROM proxies LIMIT 5;

-- 只查询特定列
SELECT ip, port, status FROM proxies;

-- 查询所有认证信息
SELECT * FROM credentials;
```

### 条件查询

```sql
-- 查询可用的代理
SELECT * FROM proxies WHERE status = '可用';

-- 查询特定IP的代理
SELECT * FROM proxies WHERE ip = '************';

-- 查询延迟小于100ms的代理
SELECT * FROM proxies WHERE latency < 100;

-- 查询无认证的代理
SELECT * FROM proxies WHERE auth_type = 'noauth';

-- 查询带认证的代理
SELECT * FROM proxies WHERE auth_type = 'auth';
```

### 排序查询

```sql
-- 按延迟升序排序
SELECT * FROM proxies ORDER BY latency ASC;

-- 按最后检查时间降序排序
SELECT * FROM proxies ORDER BY last_check DESC;

-- 先按状态排序，再按延迟排序
SELECT * FROM proxies ORDER BY status, latency ASC;
```

### 统计查询

```sql
-- 统计代理总数
SELECT COUNT(*) FROM proxies;

-- 统计可用代理数量
SELECT COUNT(*) FROM proxies WHERE status = '可用';

-- 按认证类型统计代理数量
SELECT auth_type, COUNT(*) as count FROM proxies GROUP BY auth_type;

-- 计算平均延迟
SELECT AVG(latency) FROM proxies WHERE status = '可用';
```

### 复杂查询

**注意**: 复杂查询需要在一行内完成，不支持多行输入。

```sql
-- 联合查询示例 (必须在一行内完成)
SELECT p.ip, p.port, p.status, p.latency, c.username, c.password FROM proxies p JOIN credentials c ON p.username = c.username AND p.password = c.password WHERE p.status = '可用';

-- 子查询示例
SELECT * FROM proxies WHERE latency < (SELECT AVG(latency) FROM proxies);
```

## 数据修改命令

### 插入数据

```sql
-- 插入认证信息
INSERT INTO credentials (username, password) VALUES ('testuser', 'testpass');

-- 插入代理信息
INSERT INTO proxies (ip, port, auth_type, status) VALUES ('***********', '8080', 'noauth', '可用');
```

### 更新数据

```sql
-- 更新代理状态
UPDATE proxies SET status = '不可用' WHERE ip = '***********' AND port = '8080';

-- 更新代理延迟
UPDATE proxies SET latency = 100 WHERE ip = '***********' AND port = '8080';

-- 更新认证信息
UPDATE credentials SET password = 'newpass' WHERE username = 'testuser';
```

### 删除数据

```sql
-- 删除特定代理
DELETE FROM proxies WHERE ip = '***********' AND port = '8080';

-- 删除不可用的代理
DELETE FROM proxies WHERE status = '不可用';

-- 删除认证信息
DELETE FROM credentials WHERE username = 'testuser';
```

## 注意事项

1. **所有SQL命令必须在一行内完成**，当前实现不支持多行SQL命令。
2. 联合查询和复杂查询必须写在一行内，不能分多行输入。
3. 使用 `exit` 或 `quit` 命令可以退出数据库管理界面。
4. 使用 `tables` 命令可以查看所有表。
5. 使用 `schema 表名` 命令可以查看指定表的结构。

## 表结构参考

### proxies 表

| 列名 | 类型 | 描述 |
|------|------|------|
| id | INTEGER | 主键 |
| ip | VARCHAR(50) | 代理IP地址 |
| port | VARCHAR(10) | 代理端口 |
| username | VARCHAR(50) | 认证用户名 |
| password | VARCHAR(50) | 认证密码 |
| auth_type | VARCHAR(20) | 认证类型(noauth, auth) |
| auth_required | BOOLEAN | 是否需要认证 |
| status | VARCHAR(20) | 代理状态 |
| last_check | TIMESTAMP | 最后检查时间 |
| latency | INTEGER | 延迟(毫秒) |
| created_at | TIMESTAMP | 创建时间 |

### credentials 表

| 列名 | 类型 | 描述 |
|------|------|------|
| id | INTEGER | 主键 |
| username | VARCHAR(50) | 用户名 |
| password | VARCHAR(50) | 密码 |
| created_at | TIMESTAMP | 创建时间 |
