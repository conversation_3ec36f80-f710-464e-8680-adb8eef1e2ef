# SOCKS5代理测试工具需求文档

## 1. 项目定位

### 1.1 工具定位
- **专业级命令行工具**：面向开发者和代理服务管理者的专业测试工具
- **轻量级设计**：保持工具的简洁性和高效性，避免过度设计
- **注重自动化**：支持批量操作和自动化测试场景
- **完整工作流**：支持从导入可能可用代理、测试代理到导出可用代理的完整流程

### 1.2 应用场景
- **开发环境**：用于代理服务开发和测试阶段
- **运维环境**：进行代理服务可用性监控
- **集成环境**：支持与其他代理相关项目协作

### 1.3 技术定位
- **技术栈**：基于Go语言开发，采用SQLite数据库
- **架构特点**：模块化设计，标准库为主，保持独立性
- **扩展性**：预留API接口，支持后续功能扩展

### 1.4 发展方向
- **深化测试能力**：性能评分、稳定性测试、匿名性测试
- **增强分析能力**：数据统计、可视化报表
- **提升协作能力**：项目间集成、数据同步

## 2. 已实现功能需求

### 2.1 代理测试功能
- [x] 测试单个代理（无认证方式）
- [x] 测试单个代理（带认证方式）
- [x] 批量测试代理（从文件加载）
- [x] 自动重试机制
- [x] 超时控制
- [x] 测试结果保存到数据库
- [ ] 测试未测试的potential_proxies

### 2.2 数据库管理功能
- [x] 保存代理测试结果
- [x] 保存认证信息
- [x] 查询代理列表
- [x] 执行SQL命令管理数据库

### 2.3 用户界面功能
- [x] 命令行参数支持
- [x] 交互式菜单
- [x] 配置管理
- [x] 测试结果展示

### 2.4 项目结构优化
- [x] 分层架构设计
- [x] 适配器模式连接组件
- [x] 模块化代码组织
- [x] 标准目录结构

## 3. 新增功能需求

### 3.1 代理导出功能
- [ ] **需求描述**：从数据库中导出特定条件的代理
- [ ] **功能细节**：
  - 按状态索引导出代理（如只导出"可用"状态的代理）
  - 支持多条件组合筛选（如状态+延迟+认证类型）
  - 导出前可选择重新测试代理可用性
  - 支持多种导出格式：
    - 基本格式：`IP 端口 [账号 密码]`
    - URL格式：`socks5://[账号:密码@]IP:端口`
    - 其他自定义格式（预留扩展点）

### 3.2 项目间集成需求
- [ ] **需求描述**：与代理获取项目集成，但保持项目独立性
- [ ] **功能细节**：
  - 两个项目操作同一个数据库
  - 或通过API接口进行通信
  - 代理获取项目将可能可用的代理写入数据库
  - 本项目从数据库读取并测试这些代理
- [ ] **技术考虑**：
  - 数据库表结构兼容性
  - 并发访问控制
  - API设计（如果采用API方式）
- [ ] **数据库表结构**：
  - 新增`potential_proxies`表，存储可能可用的代理（不含账密、连接延迟等信息）

### 3.3 同步机制实现
- [ ] **需求描述**：实现基础文件锁和超时机制
- [ ] **功能细节**：
  - 基础文件锁：
    - 锁机制设计
    - 死锁预防
    - 异常处理（进程崩溃、网络中断等）
  - 超时机制：
    - 超时配置
    - 超时检测
    - 自动释放资源

## 4. UI入口规划

### 4.1 现有UI入口
1. **主菜单**
   - 测试单个代理
   - 批量测试代理
   - 查看代理列表
   - 数据库管理
   - 配置设置
   - 退出

2. **命令行参数**
   - `-config`：配置文件路径
   - `-db`：数据库文件路径
   - `-ip`：要测试的代理IP
   - `-port`：要测试的代理端口
   - `-i`：启用交互式菜单

### 4.2 新增UI入口规划 - API接口

采用API接口方式实现新功能，便于项目间集成：

1. **REST API**（新增）
   - `POST /api/proxies`：添加代理
   - `GET /api/proxies`：获取代理列表
   - `POST /api/proxies/test`：测试代理
   - `GET /api/proxies/export`：导出代理

2. **API接口详细设计**

   a. **添加代理**
   ```
   POST /api/proxies
   请求体：
   {
     "proxies": [
       {"ip": "***********", "port": "1080"},
       {"ip": "***********", "port": "8080"}
     ]
   }
   响应：
   {
     "success": true,
     "count": 2,
     "message": "成功添加2个代理"
   }
   ```

   b. **获取代理列表**
   ```
   GET /api/proxies?status=可用&limit=10
   响应：
   {
     "success": true,
     "count": 10,
     "proxies": [
       {
         "ip": "***********",
         "port": "1080",
         "status": "可用",
         "latency": 100,
         "auth_required": false
       },
       ...
     ]
   }
   ```

   c. **测试代理**
   ```
   POST /api/proxies/test
   请求体：
   {
     "proxies": [
       {"ip": "***********", "port": "1080"},
       {"ip": "***********", "port": "8080", "username": "user", "password": "pass"}
     ],
     "concurrent": 5
   }
   响应：
   {
     "success": true,
     "results": [
       {
         "ip": "***********",
         "port": "1080",
         "status": "可用",
         "latency": 100
       },
       ...
     ]
   }
   ```

   d. **导出代理**
   ```
   GET /api/proxies/export?status=可用&format=url&retest=true
   响应：
   {
     "success": true,
     "count": 5,
     "format": "url",
     "proxies": [
       "socks5://***********:1080",
       "socks5://user:pass@***********:8080",
       ...
     ]
   }
   ```

## 5. 数据库表结构

### 5.1 现有表结构

#### proxies 表
| 列名 | 类型 | 描述 |
|------|------|------|
| id | INTEGER | 主键 |
| ip | VARCHAR(50) | 代理IP地址 |
| port | VARCHAR(10) | 代理端口 |
| username | VARCHAR(50) | 认证用户名 |
| password | VARCHAR(50) | 认证密码 |
| auth_type | VARCHAR(20) | 认证类型(noauth, auth) |
| auth_required | BOOLEAN | 是否需要认证 |
| status | VARCHAR(20) | 代理状态 |
| last_check | TIMESTAMP | 最后检查时间 |
| latency | INTEGER | 延迟(毫秒) |
| created_at | TIMESTAMP | 创建时间 |

#### credentials 表
| 列名 | 类型 | 描述 |
|------|------|------|
| id | INTEGER | 主键 |
| username | VARCHAR(50) | 用户名 |
| password | VARCHAR(50) | 密码 |
| created_at | TIMESTAMP | 创建时间 |

### 5.2 新增表结构

#### potential_proxies 表
| 列名 | 类型 | 描述 |
|------|------|------|
| id | INTEGER | 主键 |
| ip | VARCHAR(50) | 代理IP地址 |
| port | VARCHAR(10) | 代理端口 |
| source | VARCHAR(100) | 代理来源 |
| discovered_at | TIMESTAMP | 发现时间 |
| tested | BOOLEAN | 是否已测试 |
| created_at | TIMESTAMP | 创建时间 |

## 6. 技术实现考虑

### 6.1 代理导出功能
- 使用SQL查询从数据库筛选代理
- 支持多种导出格式的转换器
- 文件写入操作

### 6.2 项目间集成
- 数据库表结构设计
- 并发控制机制
- API接口设计与实现

### 6.3 同步机制
- 文件锁实现
- 超时机制设计
- 异常恢复策略

## 7. 优先级排序

1. **高优先级**
   - 实现代理导出功能
   - 实现API接口

2. **中优先级**
   - 新增potential_proxies表
   - 实现基础文件锁和超时机制

3. **低优先级**
   - 高级筛选和导出格式
   - API接口扩展功能

## 8. 完整工作流程

### 8.1 导入可能可用代理
1. **通过API导入**
   ```bash
   # 启动API服务器
   socks5tester -api 8080

   # 通过API导入代理
   python import_proxies_via_api.py --file ip_output-18889.txt --api-port 8080
   ```

2. **直接导入数据库**
   ```bash
   # 直接导入到数据库
   python import_proxies.py --file ip_output-18889.txt --db proxies.db
   ```

### 8.2 测试未测试的代理
1. **通过命令行测试**（计划实现）
   ```bash
   # 测试未测试的代理
   socks5tester --test-potential --concurrent 5 --batch-size 10

   # 重新测试所有代理
   socks5tester --test-potential --retest-all --concurrent 5
   ```

2. **通过UI菜单测试**（计划实现）
   - 启动交互式菜单：`socks5tester -i`
   - 选择"测试可能可用代理"选项
   - 配置测试参数（并发数、批处理大小等）
   - 开始测试

3. **通过Python脚本测试**（临时解决方案）
   ```bash
   # 启动API服务器
   socks5tester -api 8080

   # 测试未测试的代理
   python test_potential_proxies.py --db proxies.db --batch-size 10 --concurrent 5
   ```

### 8.3 导出可用代理
1. **通过UI菜单导出**
   - 启动交互式菜单：`socks5tester -i`
   - 选择"导出代理"选项
   - 配置导出参数（状态、格式、是否重新测试等）
   - 导出到文件

2. **通过API导出**
   ```bash
   # 启动API服务器
   socks5tester -api 8080

   # 通过浏览器或curl导出
   curl "http://localhost:8080/api/proxies/export?status=可用&format=url"
   ```

## 9. 后续功能规划

1. **核心功能增强**
   - 代理性能评分系统
   - 代理稳定性测试
   - 代理匿名性测试

2. **使用体验优化**
   - 批量导入认证信息
   - 数据可视化报表
   - 代理地理位置显示

3. **运维能力提升**
   - 自动化测试调度
   - 性能监控告警
   - 数据备份恢复
