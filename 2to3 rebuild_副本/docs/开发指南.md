# SOCKS5代理测试工具开发指南

**版本**: v1.1.0
**最后更新**: 当前
**文档状态**: 稳定

## 1. 开发环境设置

### 1.1 前提条件

- Go 1.19+
- SQLite 3
- Git

### 1.2 安装步骤

1. 克隆仓库
   ```bash
   git clone https://github.com/yourusername/socks5-proxy-tester.git
   cd socks5-proxy-tester
   ```

2. 安装依赖
   ```bash
   go mod download
   ```

3. 编译项目
   ```bash
   go build -o socks5tester.exe
   ```

## 2. 项目结构

```
.
├── cmd/                    # 命令行工具
├── internal/               # 内部包
│   ├── api/                # API服务
│   ├── app/                # 应用程序核心
│   │   ├── adapters/       # 适配器
│   │   ├── core/           # 核心逻辑
│   │   └── types/          # 类型定义
│   ├── config/             # 配置相关
│   ├── database/           # 数据库抽象和实现
│   │   ├── migrations/     # 数据库迁移
│   │   └── sqlite/         # SQLite实现
│   ├── proxy/              # 代理测试核心逻辑
│   └── ui/                 # 交互式用户界面
│       ├── menu/           # 菜单实现
│       └── types/          # 类型定义
├── pkg/                    # 公共包
│   ├── errors/             # 错误处理
│   ├── logger/             # 日志处理
│   └── metrics/            # 性能指标
├── docs/                   # 文档
├── test/                   # 测试相关
│   ├── fixtures/           # 测试数据
│   └── unit/               # 单元测试
├── tools/                  # 工具
│   ├── scripts/            # 脚本
│   └── build/              # 构建工具
├── config.json             # 配置文件
├── main.go                 # 程序入口
└── proxies.db              # SQLite数据库
```

## 3. 核心接口

### 3.1 代理测试接口

```go
// ProxyTester 代理测试接口
type ProxyTester interface {
    // TestNoAuth 测试无认证代理
    TestNoAuth(ip, port string) (*Proxy, error)

    // TestWithAuth 测试带认证代理
    TestWithAuth(ip, port, username, password string) (*Proxy, error)

    // TestAllAuth 测试所有认证方式
    TestAllAuth(ip, port string) (*Proxy, error)

    // SaveTestResult 保存测试结果
    SaveTestResult(proxy *Proxy) error

    // GetProxies 获取代理列表
    GetProxies(filters map[string]interface{}) ([]*Proxy, error)
}
```

### 3.2 数据库接口

```go
// Storage 数据库存储接口
type Storage interface {
    // Connect 连接数据库
    Connect() error

    // Close 关闭数据库连接
    Close() error

    // SaveProxy 保存代理信息
    SaveProxy(proxy *Proxy) error

    // GetProxy 获取代理信息
    GetProxy(ip, port string) (*Proxy, error)

    // GetAllProxies 获取所有代理信息
    GetAllProxies() ([]*Proxy, error)

    // SaveCredential 保存认证信息
    SaveCredential(username, password string) error

    // GetAllCredentials 获取所有认证信息
    GetAllCredentials() ([]*Credential, error)

    // SavePotentialProxy 保存可能可用的代理
    SavePotentialProxy(proxy *PotentialProxy) error

    // GetPotentialProxies 获取可能可用的代理
    GetPotentialProxies(tested bool, limit int) ([]*PotentialProxy, error)

    // MarkPotentialProxyTested 标记可能可用的代理为已测试
    MarkPotentialProxyTested(ip, port string, tested bool) error

    // ImportPotentialProxies 批量导入可能可用的代理
    ImportPotentialProxies(proxies []*PotentialProxy) (int, error)
}
```

## 4. 包结构指南

### 4.1 包结构总览

项目采用多层架构设计，主要包括以下目录：

```
.
├── cmd/                    # 命令行工具
├── internal/               # 内部包
│   ├── app/                # 应用程序核心
│   │   ├── adapters/       # 适配器
│   │   ├── core/           # 核心逻辑
│   │   └── models/         # 应用层数据模型
│   ├── config/             # 配置相关
│   ├── converter/          # 数据转换器
│   ├── database/           # 数据库抽象和实现
│   ├── proxy/              # 代理测试核心逻辑
│   └── ui/                 # 交互式用户界面
│       ├── menu/           # 菜单实现
│       ├── models/         # UI数据模型
│       └── types/          # 类型定义
├── pkg/                    # 公共包
├── test/                   # 测试相关
│   ├── helpers/            # 测试辅助函数
│   ├── fixtures/           # 测试数据
│   └── unit/               # 单元测试
└── docs/                   # 文档
```

### 4.2 关键包说明

#### 4.2.1 UI相关包

**internal/ui/menu**
- **职责**: 实现交互式菜单功能
- **重要文件**:
  - `core.go` - 菜单核心结构和接口
  - `display.go` - 显示相关函数
  - `input.go` - 用户输入处理
  - `proxy_test.go` - 代理测试菜单功能
  - `batch_test.go` - 批量测试菜单功能
- **导入路径**: `github.com/2to3rebuild/internal/ui/menu`

**internal/ui/models**
- **职责**: 定义UI数据模型
- **重要文件**:
  - `proxy_model.go` - 代理数据模型
  - `config_model.go` - 配置数据模型
  - `batch_test_model.go` - 批量测试数据模型
- **导入路径**: `github.com/2to3rebuild/internal/ui/models`

**internal/ui/types**
- **职责**: 定义UI类型和接口
- **重要文件**: `types.go` - 定义了UI接口如 `ProxyTester`, `Config` 等
- **导入路径**: `github.com/2to3rebuild/internal/ui/types`

#### 4.2.2 应用核心包

**internal/app/models**
- **职责**: 定义应用层数据模型
- **导入路径**: `github.com/2to3rebuild/internal/app/models`

**internal/app/adapters**
- **职责**: 提供应用层适配器
- **导入路径**: `github.com/2to3rebuild/internal/app/adapters`

#### 4.2.3 测试相关包

**test/helpers**
- **职责**: 提供测试辅助函数
- **重要文件**:
  - `model_helpers.go` - 模型测试辅助函数
  - `test_data_generator.go` - 测试数据生成器
- **导入路径**: `github.com/2to3rebuild/test/helpers`

### 4.3 常见错误及避免方法

#### 4.3.1 包引用错误

**问题: 引用了未定义的类型**
```go
// 错误示例
import "github.com/2to3rebuild/internal/ui/bubbletea/common"
// ...
msg := common.ProgressMsg{} // 编译错误: common.ProgressMsg未定义
```

**解决方法**:
1. **检查类型定义位置**:
   ```go
   // 正确示例
   import "github.com/2to3rebuild/internal/ui/common" // 注意是ui/common而非ui/bubbletea/common
   // ...
   msg := common.ProgressMsg{} // 正确
   ```

2. **使用别名避免混淆**:
   ```go
   import (
       uicommon "github.com/2to3rebuild/internal/ui/common"
       bubblecommon "github.com/2to3rebuild/internal/ui/bubbletea/common"
   )
   // ...
   msg := uicommon.ProgressMsg{} // 明确指定使用ui/common包
   ```

#### 4.3.2 包结构理解不清

**问题: 混淆了不同包中的同名类型**
```go
// 错误示例: 混淆了UI模型和应用模型
import "github.com/2to3rebuild/internal/app/models" // 应用模型
// ...
proxy := &models.ProxyModel{} // 但实际需要的是UI模型
```

**解决方法**:
1. **使用明确的导入别名**:
   ```go
   import (
       appmodels "github.com/2to3rebuild/internal/app/models"
       uimodels "github.com/2to3rebuild/internal/ui/models"
   )
   // ...
   proxy := &uimodels.ProxyModel{} // 明确指定使用UI模型
   ```

2. **查看类型定义**:
   - 使用IDE的"转到定义"功能
   - 或使用`grep`等工具搜索类型定义

## 5. 编码规范

1. **命名规范**
   - 使用驼峰命名法
   - 公共函数和变量首字母大写
   - 私有函数和变量首字母小写
   - 常量全部大写，用下划线分隔

2. **注释规范**
   - 所有公共函数和类型必须有注释
   - 注释应以函数/类型名开头
   - 使用完整的句子
   - 复杂逻辑应有详细注释

3. **错误处理**
   - 不要忽略错误
   - 使用pkg/errors包中的函数创建错误
   - 错误信息应该清晰明了

4. **代码组织**
   - 相关功能应放在同一个包中
   - 包应该有明确的职责
   - 避免循环依赖

5. **最佳实践**
   - 添加新功能前，阅读相关文档和检查现有实现
   - 编写测试时，使用正确的测试包名和测试辅助函数
   - 代码审查前，检查导入路径、包名、类型使用等

## 6. 数据库管理

### 6.1 数据库表结构

#### proxies 表

| 列名 | 类型 | 描述 |
|------|------|------|
| id | INTEGER | 主键 |
| ip | VARCHAR(50) | 代理IP地址 |
| port | VARCHAR(10) | 代理端口 |
| username | VARCHAR(50) | 认证用户名 |
| password | VARCHAR(50) | 认证密码 |
| auth_type | VARCHAR(20) | 认证类型(noauth, auth) |
| auth_required | BOOLEAN | 是否需要认证 |
| status | VARCHAR(20) | 代理状态 |
| last_check | TIMESTAMP | 最后检查时间 |
| latency | INTEGER | 延迟(毫秒) |
| created_at | TIMESTAMP | 创建时间 |

#### credentials 表

| 列名 | 类型 | 描述 |
|------|------|------|
| id | INTEGER | 主键 |
| username | VARCHAR(50) | 用户名 |
| password | VARCHAR(50) | 密码 |
| created_at | TIMESTAMP | 创建时间 |

#### potential_proxies 表

| 列名 | 类型 | 描述 |
|------|------|------|
| id | INTEGER | 主键 |
| ip | VARCHAR(50) | 代理IP地址 |
| port | VARCHAR(10) | 代理端口 |
| source | VARCHAR(100) | 代理来源 |
| location | VARCHAR(100) | 地理位置 |
| discovered_at | TIMESTAMP | 发现时间 |
| tested | BOOLEAN | 是否已测试 |
| created_at | TIMESTAMP | 创建时间 |

### 6.2 常用SQL命令

#### 基本查询

```sql
-- 查询所有代理
SELECT * FROM proxies;

-- 查询前5条记录
SELECT * FROM proxies LIMIT 5;

-- 只查询特定列
SELECT ip, port, status FROM proxies;

-- 查询所有认证信息
SELECT * FROM credentials;
```

#### 条件查询

```sql
-- 查询可用的代理
SELECT * FROM proxies WHERE status = '可用';

-- 查询特定IP的代理
SELECT * FROM proxies WHERE ip = '************';

-- 查询延迟小于100ms的代理
SELECT * FROM proxies WHERE latency < 100;

-- 查询无认证的代理
SELECT * FROM proxies WHERE auth_type = 'noauth';

-- 查询带认证的代理
SELECT * FROM proxies WHERE auth_type = 'auth';
```

#### 数据修改

```sql
-- 插入认证信息
INSERT INTO credentials (username, password) VALUES ('testuser', 'testpass');

-- 插入代理信息
INSERT INTO proxies (ip, port, auth_type, status) VALUES ('***********', '8080', 'noauth', '可用');

-- 更新代理状态
UPDATE proxies SET status = '不可用' WHERE ip = '***********' AND port = '8080';

-- 删除代理
DELETE FROM proxies WHERE ip = '***********' AND port = '8080';

-- 插入可能可用的代理
INSERT INTO potential_proxies (ip, port, source, location, discovered_at, tested)
VALUES ('***********', '8080', 'manual', '中国', datetime('now'), 0);

-- 标记代理为已测试
UPDATE potential_proxies SET tested = 1 WHERE ip = '***********' AND port = '8080';

-- 查询未测试的代理
SELECT * FROM potential_proxies WHERE tested = 0 LIMIT 10;
```

## 7. 测试指南

### 7.1 单元测试

运行所有测试：

```bash
go test ./...
```

运行特定包的测试：

```bash
go test ./internal/proxy
```

运行特定测试：

```bash
go test ./internal/proxy -run TestProxyNoAuth
```

### 7.2 测试数据

测试数据位于 `test/fixtures/` 目录下：

- `test/fixtures/proxies/proxies.txt` - 无认证代理列表
- `test/fixtures/proxies/auth_proxies.txt` - 带认证代理列表
- `test/fixtures/auth/credentials.txt` - 认证信息列表

### 7.3 测试覆盖率

生成测试覆盖率报告：

```bash
go test ./... -cover
```

生成HTML格式的覆盖率报告：

```bash
go test ./... -coverprofile=coverage.out
go tool cover -html=coverage.out
```

## 8. 贡献指南

1. Fork仓库
2. 创建功能分支
3. 提交更改
4. 运行测试
5. 提交Pull Request

## 9. 版本控制

使用语义化版本控制：

- 主版本号：不兼容的API更改
- 次版本号：向后兼容的功能性新增
- 修订号：向后兼容的问题修正
