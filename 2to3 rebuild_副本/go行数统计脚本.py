import os
import re
from collections import defaultdict

def count_lines(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return len(f.readlines())
    except Exception as e:
        print(f"Error counting lines in {file_path}: {e}")
        return 0

def find_go_files(root_dir='.'):
    go_files = []
    for root, dirs, files in os.walk(root_dir):
        # Skip .git directory
        if '.git' in dirs:
            dirs.remove('.git')

        for file in files:
            if file.endswith('.go') and not file.endswith('.go.bak'):
                file_path = os.path.join(root, file)
                # Convert to relative path
                rel_path = os.path.relpath(file_path, root_dir)
                # Replace backslashes with forward slashes for consistency
                rel_path = rel_path.replace('\\', '/')
                go_files.append(rel_path)

    return go_files

def group_by_directory(file_stats):
    dir_stats = defaultdict(lambda: {'count': 0, 'lines': 0})

    for file_path, lines in file_stats:
        # Get directory
        directory = os.path.dirname(file_path)
        if not directory:
            directory = '根目录'
        else:
            directory = directory + '/'

        dir_stats[directory]['count'] += 1
        dir_stats[directory]['lines'] += lines

    # Calculate average
    for dir_name in dir_stats:
        dir_stats[dir_name]['avg'] = round(dir_stats[dir_name]['lines'] / dir_stats[dir_name]['count'], 1)

    # Sort by total lines
    sorted_dirs = sorted(dir_stats.items(), key=lambda x: x[1]['lines'], reverse=True)
    return sorted_dirs

def group_by_function(file_stats):
    func_mapping = {
        r'^(main\.go|config\.go|update_db\.go)$': '核心功能 (根目录)',
        r'^cmd/': '命令行工具 (cmd/)',
        r'^internal/config/': '配置管理 (internal/config/)',
        r'^internal/database/': '数据库操作 (internal/database/)',
        r'^internal/proxy/': '代理测试 (internal/proxy/)',
        r'^internal/ui/': '用户界面 (internal/ui/)',
        r'^internal/app/': '应用程序核心 (internal/app/)',
        r'^test/unit/': '测试代码 (test/unit/)',
        r'^pkg/': '公共包 (pkg/)'
    }

    func_stats = defaultdict(lambda: {'count': 0, 'lines': 0})

    for file_path, lines in file_stats:
        matched = False
        for pattern, func_name in func_mapping.items():
            if re.match(pattern, file_path):
                func_stats[func_name]['count'] += 1
                func_stats[func_name]['lines'] += lines
                matched = True
                break

        if not matched and not os.path.dirname(file_path):
            # Root directory files not matched by other patterns
            func_stats['核心功能 (根目录)']['count'] += 1
            func_stats['核心功能 (根目录)']['lines'] += lines

    # Calculate average
    for func_name in func_stats:
        func_stats[func_name]['avg'] = round(func_stats[func_name]['lines'] / func_stats[func_name]['count'], 1)

    # Sort by total lines
    sorted_funcs = sorted(func_stats.items(), key=lambda x: x[1]['lines'], reverse=True)
    return sorted_funcs

def generate_markdown(file_stats, dir_stats, func_stats):
    md_content = "# Go文件行数统计\n\n"
    md_content += "下表列出了项目中所有Go文件的行数，按照行数从多到少排序。\n\n"

    # File table
    md_content += "| 文件路径 | 行数 |\n"
    md_content += "|---------|------|\n"
    for file_path, lines in file_stats:
        md_content += f"| {file_path} | {lines} |\n"

    md_content += "\n## 文件分组统计\n\n"

    # Directory stats
    md_content += "### 按目录分组\n\n"
    md_content += "| 目录 | 文件数 | 总行数 | 平均行数 |\n"
    md_content += "|------|-------|-------|---------|\n"
    for dir_name, stats in dir_stats:
        md_content += f"| {dir_name} | {stats['count']} | {stats['lines']} | {stats['avg']} |\n"

    # Function stats
    md_content += "\n### 按功能分组\n\n"
    md_content += "| 功能模块 | 文件数 | 总行数 | 平均行数 |\n"
    md_content += "|---------|-------|-------|---------|\n"
    for func_name, stats in func_stats:
        md_content += f"| {func_name} | {stats['count']} | {stats['lines']} | {stats['avg']} |\n"

    # Analysis and suggestions
    md_content += "\n## 分析与建议\n\n"

    # Find large files (>200 lines)
    large_files = [f for f, l in file_stats if l > 200]
    if large_files:
        md_content += "1. **大文件拆分**：\n"
        for file in large_files[:3]:  # Top 3 largest files
            line_count = next(l for f, l in file_stats if f == file)
            md_content += f"   - `{file}` ({line_count}行) - 可以考虑将不同的功能拆分为单独的文件\n"
        md_content += "\n"

    # Code refactoring opportunities
    md_content += "2. **代码重构机会**：\n"
    ui_stats = next((stats for name, stats in func_stats if '用户界面' in name), None)
    if ui_stats and ui_stats['lines'] > 500:
        md_content += f"   - 用户界面代码占比较大 ({ui_stats['lines']}行)，可以考虑进一步模块化\n"

    test_stats = next((stats for name, stats in func_stats if '测试代码' in name), None)
    if test_stats and test_stats['lines'] > 300:
        md_content += f"   - 测试代码行数较多 ({test_stats['lines']}行)，可以考虑更多的测试辅助函数减少重复代码\n"
    md_content += "\n"

    # File organization
    md_content += "3. **文件组织优化**：\n"
    root_files = [f for f, l in file_stats if not '/' in f]
    if root_files:
        md_content += f"   - 根目录下有{len(root_files)}个Go文件，可以考虑移动到相应的内部包中\n"
    md_content += "   - 确保每个包都有明确的职责和边界\n"
    md_content += "   - 考虑使用更多的接口来降低包之间的耦合度\n\n"

    # Test coverage
    md_content += "4. **测试覆盖**：\n"
    md_content += "   - 确保所有核心功能都有对应的测试文件\n"

    # Check if UI and cmd packages have tests
    ui_test_files = [f for f, l in file_stats if 'internal/ui' in f and 'test' in f]
    if not ui_test_files:
        md_content += "   - 考虑为 `internal/ui` 包添加更多测试\n"

    cmd_test_files = [f for f, l in file_stats if 'cmd' in f and 'test' in f]
    if not cmd_test_files:
        md_content += "   - 考虑为 `cmd` 包添加测试\n"

    return md_content

def main():
    try:
        # Find all Go files
        go_files = find_go_files()

        # Count lines in each file
        file_stats = [(file_path, count_lines(file_path)) for file_path in go_files]

        # Sort by line count (descending)
        file_stats.sort(key=lambda x: x[1], reverse=True)

        # Group by directory
        dir_stats = group_by_directory(file_stats)

        # Group by function
        func_stats = group_by_function(file_stats)

        # Generate markdown
        md_content = generate_markdown(file_stats, dir_stats, func_stats)

        # Write to file
        with open('readme/go文件行数统计.md', 'w', encoding='utf-8') as f:
            f.write(md_content)

        print("统计完成，结果已写入 readme/go文件行数统计.md")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
