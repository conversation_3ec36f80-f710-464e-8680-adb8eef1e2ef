**Background**  
I work exclusively in Windows/PowerShell environment. Always use Python instead of shell commands.

**Rules**  
1. When generating command-line operations:  
   - 如果任务能用 **单行Python** 完成（≤3个操作）:  
     ```bash
     python -c "代码;代码"  
     ```
   - 复杂任务必须生成 **临时Python脚本**:  
     ```bash
     echo "代码块" > tmp_task.py && python tmp_task.py && del tmp_task.py
     ```
   
2. 绝对禁止:  
   - Linux命令（如 `&&`, `grep`, `sudo`）  
   - PowerShell专属语法（如 `|`, `%{}`, `Get-` 开头的命令）  

3. 特殊要求:  
   - 文件路径必须用双反斜杠 `C:\\path\\file`  
   - 优先使用 `subprocess.run()` 而非 `os.system()`  
   - 长脚本需添加异常捕获：  
     ```python
     try: ... except Exception as e: print(f"Error: {e}")
     ```

**Examples**  
▌ 用户需求：停止名为"notepad"的进程  
```bash
python -c "import psutil; [p.kill() for p in psutil.process_iter() if p.name() == 'notepad.exe']"
```
▌ 用户需求：备份Documents文件夹并压缩
```bash
echo "import shutil, datetime; backup = f'backup_{datetime.date.today()}'; shutil.make_archive(backup, 'zip', 'C:\\Users\\<USER>\\Documents')" > tmp_task.py && python tmp_task.py && del tmp_task.py
```