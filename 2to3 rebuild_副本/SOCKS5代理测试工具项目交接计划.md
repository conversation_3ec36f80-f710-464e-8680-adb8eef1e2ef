# SOCKS5代理测试工具项目交接计划

## 项目架构

项目基于Go 1.20.4构建，采用Clean Architecture架构模式，实现了UI、业务逻辑和数据存储的分离。

**核心依赖库**：
- github.com/AlecAivazis/survey/v2 v2.3.6（交互式菜单）
- github.com/mattn/go-sqlite3 v1.14.16（SQLite数据库）
- golang.org/x/net v0.10.0（网络操作）
- github.com/stretchr/testify v1.8.2（测试框架）

**依赖关系图**：
```
App(核心应用) ──┬── ProxyTester(代理测试) ── SOCKS5Client(协议实现)
                ├── DatabaseManager(数据管理) ── SQLiteDB(数据库实现)
                ├── APIServer(API服务) ── HTTPHandlers(请求处理)
                └── MenuUI(菜单界面) ── MenuItems(菜单项)
```

架构选择理由：通过接口隔离各层，实现高内聚低耦合，便于测试和扩展。

## 核心模块

**internal/proxy/**：代理测试核心逻辑
- tester.go：实现代理测试流程，包含TestProxy、TestWithAuth等方法
- socks5.go：SOCKS5协议实现，处理握手、认证和连接建立
- models.go：定义TestResult等数据结构

**internal/database/**：数据存储层
- interfaces.go：定义DB、ProxyDB等接口
- sqlite/db.go：SQLite数据库实现
- sqlite/proxy_ops.go：代理相关数据库操作
- sqlite/potential_proxy_ops.go：可能可用代理操作

**internal/api/**：API服务
- server.go：HTTP服务器实现，包含启动和路由注册
- handlers.go：API请求处理函数

**internal/ui/**：用户界面
- menu/menu.go：菜单系统入口
- menu/export.go：导出功能实现
- menu/test_database.go：测试数据库代理功能

**关键接口**：ProxyTester、DB、ProxyDB、PotentialProxyDB、APIServer

**重点维护文件**：
- internal/proxy/tester.go：代理测试核心逻辑，高频修改
- internal/api/handlers.go：API请求处理，需优化错误处理
- internal/database/sqlite/potential_proxy_ops.go：潜在代理操作，待完善功能

## 开发环境

**环境变量**：
- SOCKS5_TEST_TARGET=google.com:80（测试目标）
- SOCKS5_TEST_TIMEOUT=10（超时设置，秒）
- SOCKS5_DB_PATH=./proxies.db（数据库路径）
- SOCKS5_API_PORT=8080（API服务端口）
- SOCKS5_LOG_LEVEL=info（日志级别）

**编译命令**：
```bash
go build -o socks5tester.exe
```

**运行命令**：
```bash
# 交互式菜单
./socks5tester -i

# API服务器
./socks5tester -api 8080

# 测试单个代理
./socks5tester -ip 127.0.0.1 -port 1080
```

**测试命令**：
```bash
go test ./... -v
```

**依赖服务配置**：
- SQLite数据库：确保proxies.db文件有读写权限
- 测试目标服务器：需要稳定的互联网连接访问google.com:80

## 注意事项

- API服务器在处理大量并发请求时可能出现内存泄漏（internal/api/server.go:270）
- 数据库操作未使用事务，可能导致数据不一致
- 测试代理时必须确保网络连接正常，否则会产生误判
- 部署时需确保SQLite数据库文件有正确的读写权限
- 高并发测试时需控制goroutine数量，避免资源耗尽


