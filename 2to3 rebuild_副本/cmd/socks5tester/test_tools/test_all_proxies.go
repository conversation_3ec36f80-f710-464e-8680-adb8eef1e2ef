package main

import (
	"bufio"
	"fmt"
	"os"
	"os/exec"
	"strings"
)

func TestAllProxies() {
	// 读取代理列表
	proxies, err := readProxiesFromFile("test/proxies.txt")
	if err != nil {
		fmt.Printf("读取代理列表失败: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("从文件加载了 %d 个代理\n", len(proxies))

	// 测试每个代理
	for _, proxy := range proxies {
		if len(proxy) < 2 {
			continue
		}

		ip, port := proxy[0], proxy[1]
		fmt.Printf("\n开始测试代理: %s:%s\n", ip, port)

		// 构建命令
		cmd := fmt.Sprintf("go run cmd/socks5tester/main.go -ip %s -port %s", ip, port)

		// 执行命令
		fmt.Printf("执行命令: %s\n", cmd)
		if err := executeCommand(cmd); err != nil {
			fmt.Printf("执行命令失败: %v\n", err)
		}
	}

	fmt.Println("\n所有代理测试完成")
}

// readProxiesFromFile 从文件读取代理列表
func readProxiesFromFile(filename string) ([][]string, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var proxies [][]string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.Fields(line)
		if len(parts) >= 2 {
			proxies = append(proxies, []string{parts[0], parts[1]})
		}
	}

	return proxies, scanner.Err()
}

// executeCommand 执行命令
func executeCommand(cmd string) error {
	// 创建命令
	command := exec.Command("cmd", "/C", cmd)

	// 设置输出
	command.Stdout = os.Stdout
	command.Stderr = os.Stderr

	// 执行命令
	return command.Run()
}
