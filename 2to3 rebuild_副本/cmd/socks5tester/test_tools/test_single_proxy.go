package main

import (
	"database/sql"
	"fmt"
	"os"
	"time"

	_ "modernc.org/sqlite"
)

// Credential 认证信息结构体
type Credential struct {
	Username string
	Password string
}

// Proxy 代理信息结构体
type Proxy struct {
	IP           string
	Port         string
	Username     string
	Password     string
	AuthType     string
	AuthRequired bool
	Status       string
	Latency      int64
	LastCheck    time.Time
}

func TestSingleProxy() {
	// 连接数据库
	db, err := sql.Open("sqlite", "proxies.db")
	if err != nil {
		fmt.Printf("打开数据库失败: %v\n", err)
		os.Exit(1)
	}
	defer db.Close()

	// 获取认证信息
	credentials, err := getAllCredentials(db)
	if err != nil {
		fmt.Printf("获取认证信息失败: %v\n", err)
		os.Exit(1)
	}
	fmt.Printf("从数据库加载了 %d 条认证信息\n", len(credentials))

	// 测试代理
	ip := "*************"
	port := "18889"
	fmt.Printf("开始测试代理: %s:%s\n", ip, port)

	// 测试所有认证信息
	for _, cred := range credentials {
		fmt.Printf("尝试认证 - 用户名: %s, 密码: %s\n", cred.Username, cred.Password)

		// 这里应该调用实际的测试函数
		// 由于我们无法直接调用，这里只是模拟
		fmt.Printf("模拟测试结果: 认证失败\n")
	}

	fmt.Println("测试完成")
}

// getAllCredentials 获取所有认证信息
func getAllCredentials(db *sql.DB) ([]Credential, error) {
	rows, err := db.Query("SELECT username, password FROM credentials")
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var credentials []Credential
	for rows.Next() {
		var cred Credential
		if err := rows.Scan(&cred.Username, &cred.Password); err != nil {
			return nil, err
		}
		credentials = append(credentials, cred)
	}

	return credentials, rows.Err()
}
