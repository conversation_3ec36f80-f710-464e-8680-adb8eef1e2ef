package main

import (
	"bufio"
	"fmt"
	"os"
	"strings"

	"github.com/2to3rebuild/internal/config"
	"github.com/2to3rebuild/internal/database/sqlite"
	"github.com/2to3rebuild/internal/proxy"
)

func main() {
	// 解析命令行参数
	flags, err := ParseFlags()
	if err != nil {
		fmt.Printf("参数错误: %v\n", err)
		os.Exit(1)
	}

	// 加载配置文件
	var cfg *config.Config
	if _, err := os.Stat(flags.ConfigPath); err == nil {
		cfg, err = config.LoadConfig(flags.ConfigPath)
		if err != nil {
			fmt.Printf("加载配置文件失败: %v\n", err)
			os.Exit(1)
		}
	} else {
		// 使用默认配置
		cfg = config.DefaultConfig()
	}

	// 合并命令行参数和配置文件
	cfg = flags.MergeWithConfig(cfg)

	// 打印配置信息
	flags.PrintConfig(cfg)

	// 初始化数据库
	db := sqlite.NewSQLiteDB(cfg.Database.Path)
	if err := db.Connect(); err != nil {
		fmt.Printf("连接数据库失败: %v\n", err)
		os.Exit(1)
	}
	defer db.Close()

	// 创建测试器
	tester := proxy.NewDefaultTester(cfg, db)

	// 测试单个代理
	if flags.TestIP != "" && flags.TestPort != "" {
		if flags.Username != "" && flags.Password != "" {
			// 带认证测试
			testSingleProxyWithAuth(tester, flags.TestIP, flags.TestPort, flags.Username, flags.Password)
		} else {
			// 无认证测试
			testSingleProxy(tester, flags.TestIP, flags.TestPort)
		}
		return
	}

	// 批量测试代理
	if flags.InputFile != "" {
		testProxiesFromFile(tester, flags.InputFile, flags.ThreadLimit)
		return
	}

	// 如果没有指定操作，显示帮助信息
	fmt.Println("请指定要测试的代理IP和端口，或者提供包含代理列表的输入文件")
	fmt.Println("使用 -h 或 --help 查看帮助信息")
}

// 测试单个代理
func testSingleProxy(tester proxy.ProxyTester, ip, port string) {
	fmt.Printf("测试代理: %s:%s\n", ip, port)

	result, err := tester.TestProxy(ip, port)
	if err != nil {
		fmt.Printf("测试失败: %v\n", err)
		return
	}

	info := tester.FormatResult(result)
	fmt.Printf("\n测试结果:\n")
	fmt.Printf("代理: %s\n", info.Original)
	fmt.Printf("状态: %s\n", info.Status)
	fmt.Printf("延迟: %dms\n", info.Latency)
	fmt.Printf("类型: %s\n", info.Type)

	if info.Type == "auth" {
		fmt.Printf("认证URL: %s\n", info.WithAuth)
	} else {
		fmt.Printf("无认证URL: %s\n", info.NoAuth)
	}

	if err := tester.SaveResult(result); err != nil {
		fmt.Printf("保存结果失败: %v\n", err)
	}
}

// 测试带认证的代理
func testSingleProxyWithAuth(tester proxy.ProxyTester, ip, port, username, password string) {
	fmt.Printf("测试代理: %s:%s\n", ip, port)
	fmt.Printf("使用认证: %s/%s\n", username, password)

	result, err := tester.TestWithAuth(ip, port, username, password)
	if err != nil {
		fmt.Printf("测试失败: %v\n", err)
		return
	}

	info := tester.FormatResult(result)
	fmt.Printf("\n测试结果:\n")
	fmt.Printf("代理: %s\n", info.Original)
	fmt.Printf("状态: %s\n", info.Status)
	fmt.Printf("延迟: %dms\n", info.Latency)
	fmt.Printf("类型: %s\n", info.Type)
	fmt.Printf("认证URL: %s\n", info.WithAuth)

	if err := tester.SaveResult(result); err != nil {
		fmt.Printf("保存结果失败: %v\n", err)
	}
}

// 从文件批量测试代理
func testProxiesFromFile(tester proxy.ProxyTester, filePath string, threadLimit int) {
	fmt.Printf("从文件加载代理: %s\n", filePath)

	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		fmt.Printf("打开文件失败: %v\n", err)
		return
	}
	defer file.Close()

	// 读取代理列表
	var proxies [][]string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.Fields(line)
		if len(parts) >= 2 {
			proxies = append(proxies, parts) // 保存所有字段
		}
	}

	if err := scanner.Err(); err != nil {
		fmt.Printf("读取文件失败: %v\n", err)
		return
	}

	fmt.Printf("加载了 %d 个代理\n", len(proxies))

	// TODO: 实现并发测试逻辑
	fmt.Println("批量测试功能尚未完全实现，将按顺序测试代理")

	// 顺序测试代理
	for _, proxy := range proxies {
		if len(proxy) >= 4 {
			// 带认证信息的代理
			testSingleProxyWithAuth(tester, proxy[0], proxy[1], proxy[2], proxy[3])
		} else if len(proxy) >= 2 {
			// 无认证信息的代理
			testSingleProxy(tester, proxy[0], proxy[1])
		}
	}
}
