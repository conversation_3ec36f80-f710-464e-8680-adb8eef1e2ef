package main

import (
	"flag"
	"fmt"
	"net"
	"os"
	"strings"

	"github.com/2to3rebuild/internal/config"
)

// CommandFlags 定义命令行参数结构
type CommandFlags struct {
	// 基本配置
	ConfigPath string // 配置文件路径
	DBPath     string // 数据库文件路径
	Verbose    bool   // 是否显示详细日志

	// 代理测试参数
	TestIP      string // 要测试的代理IP
	TestPort    string // 要测试的代理端口
	Username    string // 代理用户名
	Password    string // 代理密码
	Timeout     int    // 超时设置（秒）
	ThreadLimit int    // 并发线程数限制
	RetryCount  int    // 重试次数

	// 文件参数
	InputFile    string // 输入文件路径
	OutputFormat string // 输出格式选项 (3,30,multi,noauth)

	// 输出文件
	OutputFiles struct {
		Original string // 原始格式输出
		Delay    string // 带延迟格式输出
		Multi    string // 多账号格式输出
		NoAuth   string // 无认证格式输出
	}
}

// ParseFlags 解析命令行参数
func ParseFlags() (*CommandFlags, error) {
	flags := &CommandFlags{}

	// 基本配置
	flag.StringVar(&flags.ConfigPath, "config", "config.json", "配置文件路径")
	flag.StringVar(&flags.DBPath, "db", "proxies.db", "数据库文件路径")
	flag.BoolVar(&flags.Verbose, "verbose", false, "显示详细日志")

	// 代理测试参数
	flag.StringVar(&flags.TestIP, "ip", "", "要测试的代理IP")
	flag.StringVar(&flags.TestPort, "port", "", "要测试的代理端口")
	flag.StringVar(&flags.Username, "username", "", "代理用户名")
	flag.StringVar(&flags.Password, "password", "", "代理密码")
	flag.IntVar(&flags.Timeout, "timeout", 5, "代理测试超时时间（秒）")
	flag.IntVar(&flags.ThreadLimit, "limit", 10, "最大并发测试线程数")
	flag.IntVar(&flags.RetryCount, "retry", 2, "测试失败重试次数")

	// 文件参数
	flag.StringVar(&flags.InputFile, "input", "", "输入文件路径，每行格式：IP PORT")
	flag.StringVar(&flags.OutputFormat, "format", "3,30", "输出格式（支持：3=原格式，30=带延迟，multi=多账号，noauth=无认证）")

	// 输出文件
	flag.StringVar(&flags.OutputFiles.Original, "output-original", "original.txt", "原始格式输出文件")
	flag.StringVar(&flags.OutputFiles.Delay, "output-delay", "delay.txt", "带延迟信息输出文件")
	flag.StringVar(&flags.OutputFiles.Multi, "output-multi", "duplicate.txt", "多账号代理输出文件")
	flag.StringVar(&flags.OutputFiles.NoAuth, "output-noauth", "noauth.txt", "无认证代理输出文件")

	// 自定义帮助信息
	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "SOCKS5代理测试工具 - 用法:\n")
		fmt.Fprintf(os.Stderr, "  %s [选项]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "选项:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\n示例:\n")
		fmt.Fprintf(os.Stderr, "  测试单个代理(无认证): %s -ip 127.0.0.1 -port 1080\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  测试单个代理(带认证): %s -ip 127.0.0.1 -port 1080 -username user -password pass\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  批量测试代理: %s -input proxies.txt -limit 20 -format 3,30,noauth\n", os.Args[0])
	}

	flag.Parse()

	// 验证参数
	if err := flags.Validate(); err != nil {
		return nil, err
	}

	return flags, nil
}

// Validate 验证命令行参数
func (f *CommandFlags) Validate() error {
	// 如果指定了测试IP，则必须指定端口
	if f.TestIP != "" && f.TestPort == "" {
		return fmt.Errorf("指定IP时必须同时指定端口")
	}

	// 如果指定了端口，则必须指定IP
	if f.TestPort != "" && f.TestIP == "" {
		return fmt.Errorf("指定端口时必须同时指定IP")
	}

	// 验证IP格式
	if f.TestIP != "" {
		if net.ParseIP(f.TestIP) == nil {
			return fmt.Errorf("无效的IP地址格式: %s", f.TestIP)
		}
	}

	// 验证端口格式
	if f.TestPort != "" {
		if _, err := net.LookupPort("tcp", f.TestPort); err != nil {
			return fmt.Errorf("无效的端口号: %s", f.TestPort)
		}
	}

	// 验证线程数限制
	if f.ThreadLimit <= 0 {
		return fmt.Errorf("线程数限制必须大于0")
	}

	// 验证超时设置
	if f.Timeout <= 0 {
		return fmt.Errorf("超时时间必须大于0")
	}

	// 验证重试次数
	if f.RetryCount < 0 {
		return fmt.Errorf("重试次数不能为负数")
	}

	// 验证输入文件
	if f.InputFile != "" && f.TestIP != "" {
		return fmt.Errorf("不能同时指定输入文件和测试IP")
	}

	return nil
}

// MergeWithConfig 将命令行参数与配置文件合并
func (f *CommandFlags) MergeWithConfig(cfg *config.Config) *config.Config {
	// 创建配置副本
	mergedCfg := *cfg

	// 命令行参数优先于配置文件
	if f.Timeout > 0 {
		mergedCfg.Proxy.TimeoutSeconds = f.Timeout
	}

	if f.RetryCount > 0 {
		mergedCfg.Proxy.RetryConfig.MaxAttempts = f.RetryCount
	}

	// 处理输出格式
	if f.OutputFormat != "" {
		formats := strings.Split(f.OutputFormat, ",")
		mergedCfg.Output.Formats = formats
	}

	// 处理输出文件
	if mergedCfg.Output.Files == nil {
		mergedCfg.Output.Files = make(map[string]string)
	}

	mergedCfg.Output.Files["3"] = f.OutputFiles.Original
	mergedCfg.Output.Files["30"] = f.OutputFiles.Delay
	mergedCfg.Output.Files["multi"] = f.OutputFiles.Multi
	mergedCfg.Output.Files["noauth"] = f.OutputFiles.NoAuth

	// 数据库路径
	if f.DBPath != "" {
		mergedCfg.Database.Path = f.DBPath
	}

	return &mergedCfg
}

// PrintConfig 打印当前配置信息
func (f *CommandFlags) PrintConfig(cfg *config.Config) {
	if !f.Verbose {
		return
	}

	fmt.Println("当前配置:")
	fmt.Printf("  数据库路径: %s\n", cfg.Database.Path)
	fmt.Printf("  代理超时: %d秒\n", cfg.Proxy.TimeoutSeconds)
	fmt.Printf("  重试次数: %d\n", cfg.Proxy.RetryConfig.MaxAttempts)
	fmt.Printf("  测试目标: %v\n", cfg.Proxy.TestTargets)
	fmt.Printf("  输出格式: %v\n", cfg.Output.Formats)
	fmt.Println("  输出文件:")
	for format, file := range cfg.Output.Files {
		fmt.Printf("    %s: %s\n", format, file)
	}
	fmt.Println()
}
