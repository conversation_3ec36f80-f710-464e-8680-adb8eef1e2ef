package main

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"os"
	"time"

	_ "modernc.org/sqlite"
)

// ConfigFile 配置文件结构
type ConfigFile struct {
	Credentials [][]string `json:"credentials"`
}

// CredentialInfo 认证信息结构
type CredentialInfo struct {
	ID        int64
	Username  string
	Password  string
	CreatedAt time.Time
}

func main() {
	// 读取配置文件
	configFile := "../../test/config-original.json"
	data, err := os.ReadFile(configFile)
	if err != nil {
		fmt.Printf("读取配置文件失败: %v\n", err)
		os.Exit(1)
	}

	// 解析配置文件
	var config ConfigFile
	if err := json.Unmarshal(data, &config); err != nil {
		fmt.Printf("解析配置文件失败: %v\n", err)
		os.Exit(1)
	}

	// 初始化数据库
	dbPath := "../../proxies.db"
	db, err := sql.Open("sqlite", dbPath)
	if err != nil {
		fmt.Printf("打开数据库失败: %v\n", err)
		os.Exit(1)
	}
	defer db.Close()

	// 创建credentials表
	createCredentialsTableSQL := `
CREATE TABLE IF NOT EXISTS credentials (
id INTEGER PRIMARY KEY AUTOINCREMENT,
username VARCHAR(50) NOT NULL,
password VARCHAR(50) NOT NULL,
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
UNIQUE(username, password)
);`

	_, err = db.Exec(createCredentialsTableSQL)
	if err != nil {
		fmt.Printf("创建认证信息表失败: %v\n", err)
		os.Exit(1)
	}

	// 导入认证信息
	count := 0
	for _, cred := range config.Credentials {
		if len(cred) >= 2 {
			username := cred[0]
			password := cred[1]
			
			// 保存到数据库
			stmt, err := db.Prepare(`
INSERT OR IGNORE INTO credentials (username, password)
VALUES (?, ?)
			`)
			if err != nil {
				fmt.Printf("准备SQL语句失败: %v\n", err)
				continue
			}
			
			_, err = stmt.Exec(username, password)
			stmt.Close()
			
			if err != nil {
				fmt.Printf("保存认证信息失败 [%s:%s]: %v\n", username, password, err)
				continue
			}
			count++
		}
	}

	fmt.Printf("成功导入 %d 条认证信息\n", count)

	// 查询认证信息
	rows, err := db.Query(`
SELECT id, username, password, created_at
FROM credentials
LIMIT 100
	`)
	if err != nil {
		fmt.Printf("查询认证信息失败: %v\n", err)
		os.Exit(1)
	}
	defer rows.Close()

	var credentials []CredentialInfo
	for rows.Next() {
		var cred CredentialInfo
		var createdAt sql.NullTime
		
		err := rows.Scan(&cred.ID, &cred.Username, &cred.Password, &createdAt)
		if err != nil {
			fmt.Printf("读取认证信息失败: %v\n", err)
			continue
		}
		
		if createdAt.Valid {
			cred.CreatedAt = createdAt.Time
		}
		
		credentials = append(credentials, cred)
	}

	fmt.Printf("数据库中共有 %d 条认证信息\n", len(credentials))
	if len(credentials) > 0 {
		fmt.Println("前5条认证信息:")
		for i, cred := range credentials {
			if i >= 5 {
				break
			}
			fmt.Printf("ID: %d, 用户名: %s, 密码: %s\n", cred.ID, cred.Username, cred.Password)
		}
	}
}
