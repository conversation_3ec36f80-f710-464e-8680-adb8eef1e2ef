package main

import (
	"flag"
	"fmt"
	"os"
)

func main() {
	// 定义命令行参数
	testAllProxiesFlag := flag.Bool("test-all", false, "测试所有代理")
	testSingleProxyFlag := flag.Bool("test-single", false, "测试单个代理")
	helpFlag := flag.Bool("help", false, "显示帮助信息")

	// 解析命令行参数
	flag.Parse()

	// 显示帮助信息
	if *helpFlag || (!*testAllProxiesFlag && !*testSingleProxyFlag) {
		fmt.Println("代理测试工具 - 用法:")
		fmt.Println("  -test-all    测试所有代理")
		fmt.Println("  -test-single 测试单个代理")
		fmt.Println("  -help        显示帮助信息")
		os.Exit(0)
	}

	// 测试所有代理
	if *testAllProxiesFlag {
		fmt.Println("开始测试所有代理...")
		TestAllProxies()
		return
	}

	// 测试单个代理
	if *testSingleProxyFlag {
		fmt.Println("开始测试单个代理...")
		TestSingleProxy()
		return
	}
}
