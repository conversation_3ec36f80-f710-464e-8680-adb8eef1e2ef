package main

import (
	"flag"
	"fmt"
	"net/http"
	"os"

	"github.com/2to3rebuild/internal/api"
	"github.com/2to3rebuild/internal/app"
	_ "modernc.org/sqlite"
)

var (
	configFile  = flag.String("config", "config.json", "配置文件路径")
	dbFile      = flag.String("db", "proxies.db", "SQLite数据库文件路径")
	testIP      = flag.String("ip", "", "要测试的代理IP")
	testPort    = flag.String("port", "", "要测试的代理端口")
	interactive = flag.Bool("i", false, "启用交互式菜单")
	apiPort     = flag.Int("api", 0, "启动API服务器的端口，0表示不启动")
)

func main() {
	flag.Parse()

	fmt.Println("SOCKS5代理测试工具启动")
	fmt.Printf("配置文件: %s\n", *configFile)
	fmt.Printf("数据库文件: %s\n", *dbFile)

	// 创建应用程序实例
	application, err := app.NewApp(*configFile, *dbFile)
	if err != nil {
		fmt.Printf("初始化应用程序失败: %v\n", err)
		os.Exit(1)
	}
	defer application.Close()

	fmt.Println("应用程序初始化成功")

	// 如果指定了API端口，启动API服务器
	if *apiPort > 0 {
		fmt.Printf("启动API服务器，端口: %d\n", *apiPort)
		apiServer := api.NewServer(application.DB, application.ProxyTester, *apiPort)

		// 如果只启动API服务器（没有其他参数），则阻塞主线程
		if !*interactive && *testIP == "" && *testPort == "" {
			fmt.Printf("API服务器已启动，按Ctrl+C停止...\n")
			if err := apiServer.Start(); err != nil && err != http.ErrServerClosed {
				fmt.Printf("API服务器启动失败: %v\n", err)
				os.Exit(1)
			}
			return // 不会执行到这里，因为Start()会阻塞
		} else {
			// 如果有其他参数，则在后台启动API服务器
			go func() {
				if err := apiServer.Start(); err != nil && err != http.ErrServerClosed {
					fmt.Printf("API服务器启动失败: %v\n", err)
				}
			}()
			defer apiServer.Stop()
		}
	}

	// 运行应用程序
	if err := application.Run(*interactive, *testIP, *testPort); err != nil {
		fmt.Printf("运行应用程序失败: %v\n", err)
		os.Exit(1)
	}
}
