package proxy

import (
	"time"
)

// Proxy 代理信息结构体
type Proxy struct {
	ID           int64
	IP           string
	Port         string
	Username     string
	Password     string
	AuthType     string // noauth, auth
	AuthRequired bool
	Status       string
	LastCheck    time.Time
	Latency      int64
	CreatedAt    time.Time
}

// Credential 认证信息结构体
type Credential struct {
	Username string
	Password string
}

// CredentialRecord 认证信息数据库记录
type CredentialRecord struct {
	ID        int64     `db:"id"`
	Username  string    `db:"username"`
	Password  string    `db:"password"`
	CreatedAt time.Time `db:"created_at"`
}

// Config 配置接口
type Config interface {
	GetTimeoutSeconds() int
	GetTestTargets() []string
}
