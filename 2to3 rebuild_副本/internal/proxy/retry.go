package proxy

import (
	"errors"
	"fmt"
	"net"
	"time"
)

// SimpleRetry 简单重试策略
type SimpleRetry struct {
	maxAttempts int
	interval    time.Duration
}

// NewSimpleRetry 创建简单重试策略
func NewSimpleRetry(maxAttempts int, interval time.Duration) *SimpleRetry {
	return &SimpleRetry{
		maxAttempts: maxAttempts,
		interval:    interval,
	}
}

// ShouldRetry 判断是否应该重试
func (sr *SimpleRetry) ShouldRetry(err error, attemptNum int) bool {
	if attemptNum >= sr.maxAttempts {
		return false
	}

	var netErr net.Error
	if errors.As(err, &netErr) {
		return netErr.Timeout() || netErr.Temporary()
	}

	// 其他可重试的错误类型
	var opErr *net.OpError
	if errors.As(err, &opErr) {
		return true
	}

	return false
}

// NextInterval 获取下一次重试间隔
func (sr *SimpleRetry) NextInterval(attemptNum int) time.Duration {
	return sr.interval
}

// WithRetry 带重试机制的函数执行
func WithRetry[T any](strategy RetryStrategy, operation func() (T, error)) (T, error) {
	var (
		result  T
		lastErr error
		attempt = 0
	)

	for {
		result, lastErr = operation()
		if lastErr == nil {
			return result, nil
		}

		if !strategy.ShouldRetry(lastErr, attempt) {
			var zero T
			return zero, fmt.Errorf("达到最大重试次数或不可重试的错误：%w", lastErr)
		}

		time.Sleep(strategy.NextInterval(attempt))
		attempt++
	}
}

// RetryableError 可重试的错误
type RetryableError struct {
	err error
}

func (e *RetryableError) Error() string {
	return fmt.Sprintf("可重试的错误: %v", e.err)
}

func (e *RetryableError) Unwrap() error {
	return e.err
}

// NewRetryableError 创建可重试的错误
func NewRetryableError(err error) error {
	return &RetryableError{err: err}
}

// IsRetryableError 判断是否是可重试的错误
func IsRetryableError(err error) bool {
	var retryErr *RetryableError
	return errors.As(err, &retryErr)
}
