package proxy

import (
	"database/sql"
	"fmt"
)

// InitCredentialsTable 初始化认证信息表
func InitCredentialsTable(db *sql.DB) error {
	createTableSQL := `
    CREATE TABLE IF NOT EXISTS credentials (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username VARCHAR(50) NOT NULL,
        password VARCHAR(50) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(username, password)
    );`

	_, err := db.Exec(createTableSQL)
	if err != nil {
		return fmt.Errorf("创建认证信息表失败: %v", err)
	}
	return nil
}

// SaveCredential 保存认证信息到数据库
func SaveCredential(db *sql.DB, cred Credential) error {
	stmt, err := db.Prepare(`
    INSERT OR REPLACE INTO credentials (
        username, password
    ) VALUES (?, ?)
    `)
	if err != nil {
		return err
	}
	defer stmt.Close()

	_, err = stmt.Exec(cred.Username, cred.Password)
	if err != nil {
		return fmt.Errorf("保存认证信息失败: %v", err)
	}
	return nil
}

// GetAllCredentials 从数据库获取所有认证信息
func GetAllCredentials(db *sql.DB) ([]Credential, error) {
	rows, err := db.Query(`
    SELECT username, password
    FROM credentials
    `)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var creds []Credential
	for rows.Next() {
		var c Credential
		err := rows.Scan(&c.Username, &c.Password)
		if err != nil {
			return nil, err
		}
		creds = append(creds, c)
	}
	return creds, rows.Err()
}

// TestAllAuthWithDB 测试所有认证信息（使用数据库）
func TestAllAuthWithDB(db *sql.DB, tester ProxyTester, ip, port string) (*TestResult, error) {
	// 从数据库获取认证信息
	credentials, err := GetAllCredentials(db)
	if err != nil {
		return nil, fmt.Errorf("获取认证信息失败: %v", err)
	}

	// 测试数据库中的所有认证账号密码
	for _, cred := range credentials {
		result, err := tester.TestWithAuth(ip, port, cred.Username, cred.Password)
		if err == nil && result != nil && result.Status == "可用" {
			return result, nil
		}
	}

	return &TestResult{
		IP:     ip,
		Port:   port,
		Status: "不可用",
		Error:  "所有认证方式均失败",
	}, nil
}
