package proxy

import (
	"context"
	"fmt"
	"net"
	"time"

	"github.com/2to3rebuild/internal/config"
	"github.com/2to3rebuild/internal/database"
	"golang.org/x/net/proxy"
)

// DefaultTester 默认代理测试器实现
type DefaultTester struct {
	config   *config.Config
	db       database.Storage
	retry    RetryStrategy
	testAddr string
}

// NewDefaultTester 创建默认代理测试器
func NewDefaultTester(cfg *config.Config, db database.Storage) *DefaultTester {
	return &DefaultTester{
		config:   cfg,
		db:       db,
		retry:    NewSimpleRetry(cfg.Proxy.RetryConfig.MaxAttempts, cfg.Proxy.RetryConfig.Interval),
		testAddr: cfg.Proxy.TestTargets[0],
	}
}

// TestProxy 测试代理（自动判断认证方式）
func (t *DefaultTester) TestProxy(ip, port string) (*TestResult, error) {
	// 首先尝试无认证方式
	fmt.Printf("[DEBUG] 尝试无认证方式测试代理 %s:%s\n", ip, port)
	result, err := t.TestNoAuth(ip, port)
	if err == nil && result.Status == "可用" {
		fmt.Printf("[DEBUG] 代理 %s:%s 无认证测试成功\n", ip, port)
		result.AuthRequired = false
		return result, nil
	} else {
		fmt.Printf("[DEBUG] 代理 %s:%s 无认证测试失败: %v\n", ip, port, err)
	}

	// 如果无认证测试失败，尝试从数据库获取认证信息进行测试
	fmt.Printf("[DEBUG] 尝试获取认证信息测试代理 %s:%s\n", ip, port)
	credentials, err := t.db.GetAllCredentials()
	if err != nil {
		fmt.Printf("[DEBUG] 获取认证信息失败: %v\n", err)
		return nil, fmt.Errorf("获取认证信息失败：%v", err)
	}

	fmt.Printf("[DEBUG] 获取到 %d 条认证信息\n", len(credentials))
	for i, cred := range credentials {
		//fmt.Printf("[DEBUG] 尝试第 %d 个认证信息 %s:**** 测试代理 %s:%s\n", i+1, cred.Username, ip, port)
		result, err = t.TestWithAuth(ip, port, cred.Username, cred.Password)
		if err == nil && result.Status == "可用" {
			fmt.Printf("[DEBUG] 代理 %s:%s 使用第 %d 个认证 %s:%s 测试成功\n", ip, port, i+1, cred.Username, cred.Password)
			result.AuthRequired = true
			result.AuthSuccess = true
			return result, nil
		} else {
			fmt.Printf("[DEBUG] 代理 %s:%s 使用第 %d 个认证 %s:**** 测试失败: %v\n", ip, port, i+1, cred.Username, cred.Password, err)
		}
	}

	fmt.Printf("[DEBUG] 代理 %s:%s 所有认证方式均失败\n", ip, port)
	return &TestResult{
		IP:     ip,
		Port:   port,
		Status: "不可用",
		Error:  "所有认证方式均失败",
	}, nil
}

// TestNoAuth 测试无认证代理
func (t *DefaultTester) TestNoAuth(ip, port string) (*TestResult, error) {
	result := &TestResult{
		IP:         ip,
		Port:       port,
		TestedAt:   time.Now(),
		Status:     "不可用",
		RetryCount: 0,
	}

	testFn := func() (*TestResult, error) {
		dialAddr := fmt.Sprintf("%s:%s", ip, port)
		dialer, err := proxy.SOCKS5("tcp", dialAddr, nil, proxy.Direct)
		if err != nil {
			return nil, NewRetryableError(err)
		}

		ctx, cancel := context.WithTimeout(context.Background(), time.Duration(t.config.Proxy.TimeoutSeconds)*time.Second)
		defer cancel()

		start := time.Now()

		conn, err := t.dialContext(ctx, dialer)
		if err != nil {
			return nil, NewRetryableError(err)
		}
		defer conn.Close()

		result.Latency = time.Since(start).Milliseconds()
		result.Status = "可用"
		result.RetryCount++
		return result, nil
	}

	// 使用重试机制执行测试
	finalResult, err := WithRetry(t.retry, testFn)
	if err != nil {
		result.Error = err.Error()
		return result, nil
	}

	return finalResult, nil
}

// TestWithAuth 测试带认证代理
func (t *DefaultTester) TestWithAuth(ip, port, username, password string) (*TestResult, error) {
	result := &TestResult{
		IP:         ip,
		Port:       port,
		Username:   username,
		Password:   password,
		TestedAt:   time.Now(),
		Status:     "不可用",
		RetryCount: 0,
	}

	testFn := func() (*TestResult, error) {
		dialAddr := fmt.Sprintf("%s:%s", ip, port)
		auth := &proxy.Auth{
			User:     username,
			Password: password,
		}

		dialer, err := proxy.SOCKS5("tcp", dialAddr, auth, proxy.Direct)
		if err != nil {
			return nil, NewRetryableError(err)
		}

		ctx, cancel := context.WithTimeout(context.Background(), time.Duration(t.config.Proxy.TimeoutSeconds)*time.Second)
		defer cancel()

		start := time.Now()

		conn, err := t.dialContext(ctx, dialer)
		if err != nil {
			return nil, NewRetryableError(err)
		}
		defer conn.Close()

		result.Latency = time.Since(start).Milliseconds()
		result.Status = "可用"
		result.RetryCount++
		return result, nil
	}

	// 使用重试机制执行测试
	finalResult, err := WithRetry(t.retry, testFn)
	if err != nil {
		result.Error = err.Error()
		return result, nil
	}

	return finalResult, nil
}

// dialContext 使用context执行连接
func (t *DefaultTester) dialContext(ctx context.Context, dialer proxy.Dialer) (net.Conn, error) {
	connChan := make(chan net.Conn, 1)
	errChan := make(chan error, 1)

	go func() {
		conn, err := dialer.Dial("tcp", t.testAddr)
		if err != nil {
			errChan <- err
			return
		}
		connChan <- conn
	}()

	select {
	case conn := <-connChan:
		return conn, nil
	case err := <-errChan:
		return nil, err
	case <-ctx.Done():
		return nil, ctx.Err()
	}
}

// FormatResult 格式化测试结果
func (t *DefaultTester) FormatResult(result *TestResult) *ProxyInfo {
	info := &ProxyInfo{
		IP:       result.IP,
		Port:     result.Port,
		Username: result.Username,
		Password: result.Password,
		Status:   result.Status,
		Latency:  result.Latency,
	}

	if result.AuthRequired {
		info.Type = "auth"
		info.WithAuth = fmt.Sprintf("socks5://%s:%s@%s:%s", result.Username, result.Password, result.IP, result.Port)
		info.WithInfo = fmt.Sprintf("%s latency:%dms", info.WithAuth, result.Latency)
	} else {
		info.Type = "noauth"
		info.NoAuth = fmt.Sprintf("socks5://%s:%s", result.IP, result.Port)
		info.WithInfo = fmt.Sprintf("%s latency:%dms", info.NoAuth, result.Latency)
	}

	info.Original = fmt.Sprintf("%s %s", result.IP, result.Port)
	return info
}

// SaveResult 保存测试结果
func (t *DefaultTester) SaveResult(result *TestResult) error {
	proxy := &database.Proxy{
		IP:           result.IP,
		Port:         result.Port,
		Username:     result.Username,
		Password:     result.Password,
		AuthRequired: result.AuthRequired,
		Status:       result.Status,
		LastCheck:    result.TestedAt,
		Latency:      result.Latency,
	}

	if result.AuthRequired {
		proxy.AuthType = "auth"
	} else {
		proxy.AuthType = "noauth"
	}

	return t.db.SaveProxy(proxy)
}

// DB 获取数据库实例
func (t *DefaultTester) DB() database.Storage {
	return t.db
}
