package proxy

import "time"

// ProxyInfo 代理信息
type ProxyInfo struct {
	IP       string
	Port     string
	Username string
	Password string
	Status   string
	Latency  int64
	Type     string // noauth, auth

	// 用于生成不同格式的输出
	Original string // 原始格式：IP PORT
	WithAuth string // 带认证格式：socks5://user:pass@ip:port
	NoAuth   string // 无认证格式：socks5://ip:port
	WithInfo string // 带延迟信息格式：socks5://user:pass@ip:port latency:100ms
}

// TestResult 测试结果
type TestResult struct {
	IP           string
	Port         string
	Username     string
	Password     string
	Status       string    // 成功/失败
	Error        string    // 错误信息
	Latency      int64     // 延迟（毫秒）
	TestedAt     time.Time // 测试时间
	RetryCount   int       // 重试次数
	AuthRequired bool      // 是否需要认证
	AuthSuccess  bool      // 认证是否成功
}

// ProxyTester 代理测试接口
type ProxyTester interface {
	// TestProxy 测试代理（自动判断认证方式）
	TestProxy(ip, port string) (*TestResult, error)

	// TestNoAuth 测试无认证代理
	TestNoAuth(ip, port string) (*TestResult, error)

	// TestWithAuth 测试带认证代理
	TestWithAuth(ip, port, username, password string) (*TestResult, error)

	// FormatResult 格式化测试结果
	FormatResult(result *TestResult) *ProxyInfo

	// SaveResult 保存测试结果
	SaveResult(result *TestResult) error
}

// RetryStrategy 重试策略
type RetryStrategy interface {
	// ShouldRetry 判断是否应该重试
	ShouldRetry(err error, attemptNum int) bool

	// NextInterval 获取下一次重试间隔
	NextInterval(attemptNum int) time.Duration
}
