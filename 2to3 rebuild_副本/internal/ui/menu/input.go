package menu

import (
	"fmt"
	"strconv"

	"github.com/AlecAivazis/survey/v2"
)

// GetProxyInfo 获取代理信息
func GetProxyInfo() (string, string, bool, error) {
	var questions = []*survey.Question{
		{
			Name: "ip",
			Prompt: &survey.Input{
				Message: "请输入代理IP:",
			},
			Validate: survey.Required,
		},
		{
			Name: "port",
			Prompt: &survey.Input{
				Message: "请输入代理端口:",
			},
			Validate: survey.Required,
		},
		{
			Name: "useAuth",
			Prompt: &survey.Confirm{
				Message: "是否使用认证?",
				Default: false,
			},
		},
	}

	answers := struct {
		IP      string `survey:"ip"`
		Port    string `survey:"port"`
		UseAuth bool   `survey:"useAuth"`
	}{}

	err := survey.Ask(questions, &answers)
	if err != nil {
		return "", "", false, fmt.Errorf("收集代理信息失败: %v", err)
	}

	return answers.IP, answers.Port, answers.UseAuth, nil
}

// GetAuthInfo 获取认证信息
func GetAuthInfo() (string, string, error) {
	authQuestions := []*survey.Question{
		{
			Name: "username",
			Prompt: &survey.Input{
				Message: "请输入用户名:",
			},
			Validate: survey.Required,
		},
		{
			Name: "password",
			Prompt: &survey.Password{
				Message: "请输入密码:",
			},
			Validate: survey.Required,
		},
	}

	authAnswers := struct {
		Username string `survey:"username"`
		Password string `survey:"password"`
	}{}

	err := survey.Ask(authQuestions, &authAnswers)
	if err != nil {
		return "", "", fmt.Errorf("收集认证信息失败: %v", err)
	}

	return authAnswers.Username, authAnswers.Password, nil
}

// GetBatchTestParams 获取批量测试参数
func GetBatchTestParams() (string, int, bool, error) {
	questions := []*survey.Question{
		{
			Name: "filePath",
			Prompt: &survey.Input{
				Message: "请输入代理列表文件路径:",
				Help:    "文件格式: 每行一个代理，格式为 IP PORT",
			},
			Validate: survey.Required,
		},
		{
			Name: "concurrent",
			Prompt: &survey.Input{
				Message: "请输入并发测试数量:",
				Default: "5",
			},
		},
		{
			Name: "testAuth",
			Prompt: &survey.Confirm{
				Message: "是否测试认证代理?",
				Default: true,
			},
		},
	}

	answers := struct {
		FilePath   string `survey:"filePath"`
		Concurrent string `survey:"concurrent"`
		TestAuth   bool   `survey:"testAuth"`
	}{
		Concurrent: "5", // 默认值
	}

	err := survey.Ask(questions, &answers)
	if err != nil {
		return "", 0, false, fmt.Errorf("收集批量测试参数失败: %v", err)
	}

	concurrent, err := strconv.Atoi(answers.Concurrent)
	if err != nil {
		concurrent = 5 // 如果转换失败，使用默认值
	}

	return answers.FilePath, concurrent, answers.TestAuth, nil
}

// GetTimeoutSetting 获取超时设置
func GetTimeoutSetting(currentTimeout int) (int, error) {
	var timeout string
	promptTimeout := &survey.Input{
		Message: "请输入超时时间(秒):",
		Default: fmt.Sprintf("%d", currentTimeout),
	}
	err := survey.AskOne(promptTimeout, &timeout)
	if err != nil {
		return 0, fmt.Errorf("设置超时失败: %v", err)
	}

	timeoutInt, err := strconv.Atoi(timeout)
	if err != nil {
		return 0, fmt.Errorf("无效的超时时间: %v", err)
	}

	return timeoutInt, nil
}

// GetTestTarget 获取测试目标
func GetTestTarget(currentTarget string) (string, error) {
	var target string
	promptTarget := &survey.Input{
		Message: "请输入测试目标(格式: host:port):",
		Default: currentTarget,
	}
	err := survey.AskOne(promptTarget, &target)
	if err != nil {
		return "", fmt.Errorf("设置测试目标失败: %v", err)
	}

	return target, nil
}

// GetSQLCommand 获取SQL命令
func GetSQLCommand() (string, error) {
	var sqlCmd string
	prompt := &survey.Input{
		Message: "SQL> ",
	}
	err := survey.AskOne(prompt, &sqlCmd)
	if err != nil {
		return "", fmt.Errorf("读取SQL命令失败: %v", err)
	}

	return sqlCmd, nil
}

// GetMenuChoice 获取菜单选择
func GetMenuChoice(options []string) (string, error) {
	var choice string
	prompt := &survey.Select{
		Message: "请选择操作:",
		Options: options,
	}
	err := survey.AskOne(prompt, &choice)
	if err != nil {
		return "", fmt.Errorf("菜单选择错误: %v", err)
	}

	return choice, nil
}

// ConfirmAction 确认操作
func ConfirmAction(message string) (bool, error) {
	var confirm bool
	prompt := &survey.Confirm{
		Message: message,
		Default: false,
	}
	err := survey.AskOne(prompt, &confirm)
	if err != nil {
		return false, fmt.Errorf("确认操作失败: %v", err)
	}

	return confirm, nil
}
