package menu

import (
	"fmt"
	"strings"
)

// DisplayProxyResult 显示代理测试结果
func DisplayProxyResult(proxy *Proxy) {
	fmt.Println("\n测试结果:")
	fmt.Printf("IP: %s\n", proxy.IP)
	fmt.Printf("端口: %s\n", proxy.Port)
	fmt.Printf("状态: %s\n", proxy.Status)

	if proxy.Status == "可用" {
		fmt.Printf("延迟: %dms\n", proxy.Latency)

		if proxy.AuthRequired {
			fmt.Printf("认证类型: auth\n")
			fmt.Printf("用户名: %s\n", proxy.Username)
			fmt.Printf("密码: %s\n", strings.Repeat("*", len(proxy.Password)))
		} else {
			fmt.Printf("认证类型: noauth\n")
		}
	}
}

// DisplayBatchTestSummary 显示批量测试摘要
func DisplayBatchTestSummary(successCount, failCount int) {
	fmt.Printf("\n测试完成: 成功 %d 个, 失败 %d 个\n", successCount, failCount)

	if successCount > 0 {
		fmt.Printf("成功率: %.2f%%\n", float64(successCount)*100/float64(successCount+failCount))
	}
}

// DisplayProxyList 显示代理列表
func DisplayProxyList(proxies []*Proxy) {
	if len(proxies) == 0 {
		fmt.Println("没有找到代理")
		return
	}

	fmt.Printf("\n找到 %d 个代理:\n", len(proxies))
	fmt.Printf("%-15s %-6s %-10s %-8s %-6s\n", "IP", "端口", "状态", "延迟(ms)", "认证类型")
	fmt.Println(strings.Repeat("-", 60))

	for _, proxy := range proxies {
		authType := "noauth"
		if proxy.AuthRequired {
			authType = "auth"
		}

		fmt.Printf("%-15s %-6s %-10s %-8d %-6s\n",
			proxy.IP,
			proxy.Port,
			proxy.Status,
			proxy.Latency,
			authType)
	}
}

// DisplayWelcomeMessage 显示欢迎信息
func DisplayWelcomeMessage() {
	fmt.Println("=================================")
	fmt.Println("  SOCKS5代理测试工具")
	fmt.Println("  版本: v1.0.0")
	fmt.Println("=================================")
	fmt.Println("本工具用于测试SOCKS5代理的可用性和性能")
	fmt.Println("支持无认证和带认证的代理测试")
	fmt.Println("测试结果将保存到SQLite数据库中")
	fmt.Println("=================================")
}

// DisplayHelpMessage 显示帮助信息
func DisplayHelpMessage() {
	fmt.Println("\n帮助信息:")
	fmt.Println("1. 测试单个代理 - 测试指定IP和端口的代理")
	fmt.Println("2. 批量测试代理 - 从文件加载代理列表进行测试")
	fmt.Println("3. 查看代理列表 - 查看数据库中的代理列表")
	fmt.Println("4. 数据库管理 - 执行SQL命令管理数据库")
	fmt.Println("5. 配置设置 - 修改超时时间和测试目标等配置")
	fmt.Println("6. 退出 - 退出程序")
}

// DisplayErrorMessage 显示错误信息
func DisplayErrorMessage(err error) {
	fmt.Printf("\n错误: %v\n", err)
}
