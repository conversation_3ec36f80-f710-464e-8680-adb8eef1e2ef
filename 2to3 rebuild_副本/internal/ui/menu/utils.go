package menu

import (
	"fmt"
	"strings"
)

// FormatProxyInfo 格式化代理信息
func FormatProxyInfo(proxy *Proxy) string {
	var builder strings.Builder
	
	builder.WriteString(fmt.Sprintf("IP: %s\n", proxy.IP))
	builder.WriteString(fmt.Sprintf("端口: %s\n", proxy.Port))
	builder.WriteString(fmt.Sprintf("状态: %s\n", proxy.Status))
	
	if proxy.Status == "可用" {
		builder.WriteString(fmt.Sprintf("延迟: %dms\n", proxy.Latency))
		
		if proxy.AuthRequired {
			builder.WriteString(fmt.Sprintf("认证类型: auth\n"))
			builder.WriteString(fmt.Sprintf("用户名: %s\n", proxy.Username))
			builder.WriteString(fmt.Sprintf("密码: %s\n", strings.Repeat("*", len(proxy.Password))))
		} else {
			builder.WriteString(fmt.Sprintf("认证类型: noauth\n"))
		}
	}
	
	return builder.String()
}

// FormatTableHeader 格式化表头
func FormatTableHeader(columns []string) string {
	return strings.Join(columns, " | ")
}

// FormatTableRow 格式化表行
func FormatTableRow(values []string) string {
	return strings.Join(values, " | ")
}

// FormatTableSeparator 格式化表分隔符
func FormatTableSeparator(width int) string {
	return strings.Repeat("-", width)
}

// TruncateString 截断字符串
func TruncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen-3] + "..."
}

// PadString 填充字符串
func PadString(s string, width int) string {
	if len(s) >= width {
		return s
	}
	return s + strings.Repeat(" ", width-len(s))
}

// FormatDuration 格式化时间
func FormatDuration(ms int64) string {
	if ms < 1000 {
		return fmt.Sprintf("%dms", ms)
	}
	return fmt.Sprintf("%.2fs", float64(ms)/1000.0)
}

// FormatTimestamp 格式化时间戳
func FormatTimestamp(timestamp interface{}) string {
	if timestamp == nil {
		return "N/A"
	}
	return fmt.Sprintf("%v", timestamp)
}

// FormatBool 格式化布尔值
func FormatBool(b bool) string {
	if b {
		return "是"
	}
	return "否"
}

// FormatStatus 格式化状态
func FormatStatus(status string) string {
	switch status {
	case "可用":
		return "✓ 可用"
	case "不可用":
		return "✗ 不可用"
	default:
		return status
	}
}
