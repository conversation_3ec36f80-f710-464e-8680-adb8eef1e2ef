// Package menu 提供交互式菜单功能
package menu

import (
	"fmt"
	"os"
	"strconv"
	"strings"
	"sync"

	"github.com/AlecAivazis/survey/v2"
)

// ExportProxies 导出代理
func (m *Menu) ExportProxies() error {
	fmt.Println("\n=== 导出代理 ===")

	// 1. 选择导出条件
	var answers struct {
		Status      string
		RetestProxy bool
		Format      string
		OutputPath  string
		Concurrent  string
	}

	questions := []*survey.Question{
		{
			Name: "status",
			Prompt: &survey.Select{
				Message: "选择代理状态:",
				Options: []string{"所有", "可用", "不可用"},
				Default: "可用",
			},
		},
		{
			Name: "retestProxy",
			Prompt: &survey.Confirm{
				Message: "导出前重新测试代理?",
				Default: false,
			},
		},
		{
			Name: "concurrent",
			Prompt: &survey.Input{
				Message: "并发测试数量:",
				Default: "5",
			},
			Validate: survey.Required,
		},
		{
			Name: "format",
			Prompt: &survey.Select{
				Message: "选择导出格式:",
				Options: []string{
					"基本格式 (IP PORT [USERNAME PASSWORD])",
					"URL格式 (socks5://[USERNAME:PASSWORD@]IP:PORT)",
					"带延迟格式 (IP:PORT 延迟:XXXms)",
				},
				Default: "基本格式 (IP PORT [USERNAME PASSWORD])",
			},
		},
		{
			Name: "outputPath",
			Prompt: &survey.Input{
				Message: "输出文件路径:",
				Default: "exported_proxies.txt",
			},
		},
	}

	if err := survey.Ask(questions, &answers); err != nil {
		return fmt.Errorf("获取导出参数失败: %v", err)
	}

	// 2. 构建查询条件
	filters := make(map[string]interface{})
	if answers.Status != "所有" {
		filters["status"] = answers.Status
	}

	// 3. 获取代理列表
	fmt.Println("正在查询符合条件的代理...")
	proxies, err := m.ProxyTester.GetProxies(filters)
	if err != nil {
		return fmt.Errorf("获取代理列表失败: %v", err)
	}

	if len(proxies) == 0 {
		fmt.Println("没有找到符合条件的代理")
		return nil
	}

	fmt.Printf("找到 %d 个符合条件的代理\n", len(proxies))

	// 4. 如果需要重新测试，则测试代理
	if answers.RetestProxy {
		fmt.Println("开始重新测试代理...")

		// 解析并发数
		concurrent, err := strconv.Atoi(answers.Concurrent)
		if err != nil || concurrent < 1 {
			concurrent = 5 // 默认值
		}

		// 创建通道和等待组
		resultChan := make(chan *Proxy, len(proxies))
		var wg sync.WaitGroup
		semaphore := make(chan struct{}, concurrent)

		// 启动测试协程
		for _, proxy := range proxies {
			wg.Add(1)
			go func(p *Proxy) {
				defer wg.Done()

				// 获取信号量
				semaphore <- struct{}{}
				defer func() { <-semaphore }()

				// 测试代理
				var result *Proxy
				var err error

				if p.AuthRequired {
					result, err = m.ProxyTester.TestWithAuth(p.IP, p.Port, p.Username, p.Password)
				} else {
					result, err = m.ProxyTester.TestNoAuth(p.IP, p.Port)
				}

				// 如果测试成功，更新代理信息
				if err == nil && result != nil && result.Status == "可用" {
					// 保存测试结果
					if err := m.ProxyTester.SaveTestResult(result); err != nil {
						fmt.Printf("保存代理 %s:%s 测试结果失败: %v\n", p.IP, p.Port, err)
					}
					resultChan <- result
				} else {
					// 测试失败，标记为不可用
					p.Status = "不可用"
					if err := m.ProxyTester.SaveTestResult(p); err != nil {
						fmt.Printf("保存代理 %s:%s 测试结果失败: %v\n", p.IP, p.Port, err)
					}
					resultChan <- p
				}
			}(proxy)
		}

		// 等待所有测试完成
		go func() {
			wg.Wait()
			close(resultChan)
		}()

		// 收集测试结果
		var testedProxies []*Proxy
		var successCount, failCount int

		for result := range resultChan {
			if result.Status == "可用" {
				successCount++
				testedProxies = append(testedProxies, result)
			} else {
				failCount++
			}
		}

		fmt.Printf("测试完成: 成功 %d 个, 失败 %d 个\n", successCount, failCount)

		// 使用测试后的代理列表
		proxies = testedProxies

		if len(proxies) == 0 {
			fmt.Println("测试后没有可用的代理")
			return nil
		}
	}

	// 5. 导出代理
	fmt.Printf("正在导出代理到 %s...\n", answers.OutputPath)

	// 创建输出文件
	file, err := os.Create(answers.OutputPath)
	if err != nil {
		return fmt.Errorf("创建输出文件失败: %v", err)
	}
	defer file.Close()

	// 根据选择的格式导出代理
	formatType := 0
	if strings.HasPrefix(answers.Format, "URL格式") {
		formatType = 1
	} else if strings.HasPrefix(answers.Format, "带延迟格式") {
		formatType = 2
	}

	for _, proxy := range proxies {
		var line string
		switch formatType {
		case 0:
			// 基本格式: IP PORT [USERNAME PASSWORD]
			line = proxy.IP + " " + proxy.Port
			if proxy.AuthRequired {
				line += " " + proxy.Username + " " + proxy.Password
			}
		case 1:
			// URL格式: socks5://[USERNAME:PASSWORD@]IP:PORT
			if proxy.AuthRequired {
				line = fmt.Sprintf("socks5://%s:%s@%s:%s", proxy.Username, proxy.Password, proxy.IP, proxy.Port)
			} else {
				line = fmt.Sprintf("socks5://%s:%s", proxy.IP, proxy.Port)
			}
		case 2:
			// 带延迟格式: IP:PORT 延迟:XXXms
			line = fmt.Sprintf("%s:%s 延迟:%dms", proxy.IP, proxy.Port, proxy.Latency)
		}

		// 写入文件
		if _, err := file.WriteString(line + "\n"); err != nil {
			return fmt.Errorf("写入文件失败: %v", err)
		}
	}

	fmt.Printf("成功导出 %d 个代理到 %s\n", len(proxies), answers.OutputPath)
	return nil
}
