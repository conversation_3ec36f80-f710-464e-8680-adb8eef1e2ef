package menu

import (
	"fmt"

	"github.com/AlecAivazis/survey/v2"
)

// Menu 交互式菜单结构
type Menu struct {
	ProxyTester ProxyTester
	Config      Config
}

// ProxyTester 代理测试接口
type ProxyTester interface {
	TestNoAuth(ip, port string) (*Proxy, error)
	TestWithAuth(ip, port, username, password string) (*Proxy, error)
	TestAllAuth(ip, port string) (*Proxy, error) // 测试所有认证信息
	SaveTestResult(proxy *Proxy) error
	GetProxies(filters map[string]interface{}) ([]*Proxy, error) // 获取代理列表
}

// Proxy 代理信息结构体
type Proxy struct {
	IP           string
	Port         string
	Username     string
	Password     string
	AuthType     string
	AuthRequired bool
	Status       string
	LastCheck    interface{}
	Latency      int64
	CreatedAt    interface{}
}

// Config 配置接口
type Config interface {
	GetTimeoutSeconds() int
	SetTimeoutSeconds(seconds int)
	GetTestTarget() string
	SetTestTarget(target string)
	Save(path string) error
}

// Run 运行交互式菜单
func (m *Menu) Run() error {
	fmt.Println("欢迎使用SOCKS5代理测试工具")

	for {
		var choice string
		prompt := &survey.Select{
			Message: "请选择操作:",
			Options: []string{
				"测试单个代理",
				"批量测试代理",
				"测试数据库代理",
				"查看代理列表",
				"导出代理",
				"数据库管理",
				"配置设置",
				"退出",
			},
		}
		err := survey.AskOne(prompt, &choice)
		if err != nil {
			return fmt.Errorf("菜单选择错误: %v", err)
		}

		switch choice {
		case "测试单个代理":
			if err := m.TestSingleProxy(); err != nil {
				fmt.Printf("测试代理出错: %v\n", err)
			}
		case "批量测试代理":
			if err := m.TestBatchProxies(); err != nil {
				fmt.Printf("批量测试代理出错: %v\n", err)
			}
		case "测试数据库代理":
			if err := m.TestDatabaseProxies(); err != nil {
				fmt.Printf("测试数据库代理出错: %v\n", err)
			}
		case "查看代理列表":
			if err := m.ViewProxyList(); err != nil {
				fmt.Printf("查看代理列表出错: %v\n", err)
			}
		case "导出代理":
			if err := m.ExportProxies(); err != nil {
				fmt.Printf("导出代理出错: %v\n", err)
			}
		case "数据库管理":
			if err := m.ManageDatabase(); err != nil {
				fmt.Printf("数据库管理出错: %v\n", err)
			}
		case "配置设置":
			if err := m.ConfigSettings(); err != nil {
				fmt.Printf("配置设置出错: %v\n", err)
			}
		case "退出":
			fmt.Println("退出系统")
			return nil
		}
	}
}

// NewMenu 创建新的菜单实例
func NewMenu(proxyTester ProxyTester, config Config) *Menu {
	return &Menu{
		ProxyTester: proxyTester,
		Config:      config,
	}
}
