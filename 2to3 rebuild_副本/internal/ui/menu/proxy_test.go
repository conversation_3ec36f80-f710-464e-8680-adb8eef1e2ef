package menu

import (
	"fmt"

	"github.com/AlecAivazis/survey/v2"
)

// TestSingleProxyDetailed 测试单个代理（详细版本）
func (m *Menu) TestSingleProxyDetailed() error {
	// 收集代理信息
	var questions = []*survey.Question{
		{
			Name: "ip",
			Prompt: &survey.Input{
				Message: "请输入代理IP:",
			},
			Validate: survey.Required,
		},
		{
			Name: "port",
			Prompt: &survey.Input{
				Message: "请输入代理端口:",
			},
			Validate: survey.Required,
		},
		{
			Name: "useAuth",
			Prompt: &survey.Confirm{
				Message: "是否使用认证?",
				Default: false,
			},
		},
	}

	answers := struct {
		IP      string `survey:"ip"`
		Port    string `survey:"port"`
		UseAuth bool   `survey:"useAuth"`
	}{}

	err := survey.Ask(questions, &answers)
	if err != nil {
		return fmt.Errorf("收集代理信息失败: %v", err)
	}

	// 如果需要认证，收集认证信息
	var username, password string
	if answers.UseAuth {
		authQuestions := []*survey.Question{
			{
				Name: "username",
				Prompt: &survey.Input{
					Message: "请输入用户名:",
				},
				Validate: survey.Required,
			},
			{
				Name: "password",
				Prompt: &survey.Password{
					Message: "请输入密码:",
				},
				Validate: survey.Required,
			},
		}

		authAnswers := struct {
			Username string `survey:"username"`
			Password string `survey:"password"`
		}{}

		err := survey.Ask(authQuestions, &authAnswers)
		if err != nil {
			return fmt.Errorf("收集认证信息失败: %v", err)
		}

		username = authAnswers.Username
		password = authAnswers.Password
	}

	fmt.Printf("正在测试代理 %s:%s...\n", answers.IP, answers.Port)

	var result *Proxy
	var testErr error

	if answers.UseAuth {
		result, testErr = m.ProxyTester.TestWithAuth(answers.IP, answers.Port, username, password)
	} else {
		result, testErr = m.ProxyTester.TestNoAuth(answers.IP, answers.Port)
	}

	if testErr != nil {
		return fmt.Errorf("测试失败: %v", testErr)
	}

	// 显示测试结果
	fmt.Println("\n测试结果:")
	fmt.Printf("IP: %s\n", result.IP)
	fmt.Printf("端口: %s\n", result.Port)
	fmt.Printf("状态: %s\n", result.Status)
	if result.Status == "可用" {
		fmt.Printf("延迟: %dms\n", result.Latency)
	}

	// 保存测试结果
	if err := m.ProxyTester.SaveTestResult(result); err != nil {
		return fmt.Errorf("保存测试结果失败: %v", err)
	}

	fmt.Println("测试结果已保存到数据库")
	return nil
}
