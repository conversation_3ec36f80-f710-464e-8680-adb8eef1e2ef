package menu

import (
	"database/sql"
	"fmt"
	"strings"

	"github.com/AlecAivazis/survey/v2"
	_ "modernc.org/sqlite"
)

// ManageDatabase 数据库管理
func (m *Menu) ManageDatabase() error {
	fmt.Println("\n=== SQLite数据库管理 ===")
	fmt.Println("您可以在此输入SQL命令来管理数据库")
	fmt.Println("输入 'exit' 或 'quit' 返回主菜单")
	fmt.Println("输入 'tables' 查看所有表")
	fmt.Println("输入 'schema [表名]' 查看表结构")
	fmt.Println("示例: SELECT * FROM proxies LIMIT 10")
	fmt.Println("==============================")

	// 获取数据库连接
	db, err := getDBConnection()
	if err != nil {
		return fmt.Errorf("连接数据库失败: %v", err)
	}
	defer db.Close()

	for {
		// 提示用户输入SQL命令
		var sqlCmd string
		prompt := &survey.Input{
			Message: "SQL> ",
		}
		err := survey.AskOne(prompt, &sqlCmd)
		if err != nil {
			return fmt.Errorf("读取SQL命令失败: %v", err)
		}

		// 处理特殊命令
		sqlCmd = strings.TrimSpace(sqlCmd)
		if sqlCmd == "" {
			continue
		}

		// 退出命令
		if sqlCmd == "exit" || sqlCmd == "quit" {
			return nil
		}

		// 显示所有表
		if sqlCmd == "tables" {
			if err := showTables(db); err != nil {
				fmt.Printf("错误: %v\n", err)
			}
			continue
		}

		// 显示表结构
		if strings.HasPrefix(sqlCmd, "schema ") {
			tableName := strings.TrimSpace(strings.TrimPrefix(sqlCmd, "schema "))
			if tableName == "" {
				fmt.Println("请指定表名")
				continue
			}
			if err := showTableSchema(db, tableName); err != nil {
				fmt.Printf("错误: %v\n", err)
			}
			continue
		}

		// 执行SQL命令
		if err := executeSQL(db, sqlCmd); err != nil {
			fmt.Printf("SQL错误: %v\n", err)
		}
	}
}

// getDBConnection 获取数据库连接
func getDBConnection() (*sql.DB, error) {
	// 使用默认的数据库文件路径
	dbPath := "proxies.db"
	db, err := sql.Open("sqlite", dbPath)
	if err != nil {
		return nil, fmt.Errorf("打开数据库失败: %v", err)
	}
	return db, nil
}

// showTables 显示所有表
func showTables(db *sql.DB) error {
	rows, err := db.Query("SELECT name FROM sqlite_master WHERE type='table'")
	if err != nil {
		return err
	}
	defer rows.Close()

	fmt.Println("\n=== 数据库表 ===")
	var tables []string
	for rows.Next() {
		var tableName string
		if err := rows.Scan(&tableName); err != nil {
			return err
		}
		tables = append(tables, tableName)
	}

	if len(tables) == 0 {
		fmt.Println("数据库中没有表")
	} else {
		for _, table := range tables {
			fmt.Println(table)
		}
	}
	fmt.Println("===============")
	return nil
}

// showTableSchema 显示表结构
func showTableSchema(db *sql.DB, tableName string) error {
	// 检查表是否存在
	var count int
	err := db.QueryRow("SELECT count(*) FROM sqlite_master WHERE type='table' AND name=?", tableName).Scan(&count)
	if err != nil {
		return err
	}
	if count == 0 {
		return fmt.Errorf("表 '%s' 不存在", tableName)
	}

	// 获取表结构
	rows, err := db.Query(fmt.Sprintf("PRAGMA table_info(%s)", tableName))
	if err != nil {
		return err
	}
	defer rows.Close()

	fmt.Printf("\n=== 表 '%s' 结构 ===\n", tableName)
	fmt.Printf("%-5s %-20s %-15s %-10s %-10s\n", "序号", "列名", "类型", "非空", "主键")
	fmt.Println(strings.Repeat("-", 70))

	for rows.Next() {
		var cid int
		var name, typeName string
		var notNull, pk int
		var dfltValue interface{}
		if err := rows.Scan(&cid, &name, &typeName, &notNull, &dfltValue, &pk); err != nil {
			return err
		}
		fmt.Printf("%-5d %-20s %-15s %-10v %-10v\n", cid, name, typeName, notNull == 1, pk == 1)
	}
	fmt.Println("=====================")
	return nil
}

// executeSQL 执行SQL命令
func executeSQL(db *sql.DB, sqlCmd string) error {
	// 判断是查询还是更新操作
	sqlCmd = strings.TrimSpace(sqlCmd)
	sqlCmdLower := strings.ToLower(sqlCmd)

	// 查询操作
	if strings.HasPrefix(sqlCmdLower, "select") || strings.HasPrefix(sqlCmdLower, "pragma") {
		return executeQuery(db, sqlCmd)
	}

	// 更新操作
	return executeUpdate(db, sqlCmd)
}

// executeQuery 执行查询操作
func executeQuery(db *sql.DB, sqlCmd string) error {
	rows, err := db.Query(sqlCmd)
	if err != nil {
		return err
	}
	defer rows.Close()

	// 获取列名
	columns, err := rows.Columns()
	if err != nil {
		return err
	}

	if len(columns) == 0 {
		fmt.Println("查询结果为空")
		return nil
	}

	// 准备接收查询结果
	values := make([]interface{}, len(columns))
	valuePtrs := make([]interface{}, len(columns))
	for i := range columns {
		valuePtrs[i] = &values[i]
	}

	// 打印表头
	fmt.Println()
	for i, col := range columns {
		if i > 0 {
			fmt.Print(" | ")
		}
		fmt.Print(col)
	}
	fmt.Println()
	fmt.Println(strings.Repeat("-", 80))

	// 打印数据行
	rowCount := 0
	for rows.Next() {
		rowCount++
		err := rows.Scan(valuePtrs...)
		if err != nil {
			return err
		}

		for i, val := range values {
			if i > 0 {
				fmt.Print(" | ")
			}
			printValue(val)
		}
		fmt.Println()
	}

	if rowCount == 0 {
		fmt.Println("查询结果为空")
	} else {
		fmt.Printf("\n共 %d 行记录\n", rowCount)
	}

	return nil
}

// executeUpdate 执行更新操作
func executeUpdate(db *sql.DB, sqlCmd string) error {
	result, err := db.Exec(sqlCmd)
	if err != nil {
		return err
	}

	// 获取受影响的行数
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	fmt.Printf("操作成功，影响了 %d 行记录\n", rowsAffected)
	return nil
}

// printValue 打印值
func printValue(val interface{}) {
	if val == nil {
		fmt.Print("NULL")
		return
	}

	switch v := val.(type) {
	case []byte:
		fmt.Print(string(v))
	default:
		fmt.Print(v)
	}
}
