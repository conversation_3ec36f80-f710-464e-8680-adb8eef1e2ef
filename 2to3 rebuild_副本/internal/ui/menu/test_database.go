// Package menu 提供交互式菜单功能
package menu

import (
	"fmt"
	"strconv"
	"sync"
	"time"

	"github.com/AlecAivazis/survey/v2"
)

// TestDatabaseProxies 测试数据库中的代理
func (m *Menu) TestDatabaseProxies() error {
	fmt.Println("\n=== 测试数据库代理 ===")
	
	// 1. 选择测试条件
	var answers struct {
		Status      string
		Concurrent  string
		UpdateStatus bool
	}
	
	questions := []*survey.Question{
		{
			Name: "status",
			Prompt: &survey.Select{
				Message: "选择要测试的代理:",
				Options: []string{"所有", "可用", "不可用"},
				Default: "所有",
			},
		},
		{
			Name: "concurrent",
			Prompt: &survey.Input{
				Message: "并发测试数量:",
				Default: "5",
			},
			Validate: survey.Required,
		},
		{
			Name: "updateStatus",
			Prompt: &survey.Confirm{
				Message: "更新代理状态?",
				Default: true,
			},
		},
	}
	
	if err := survey.Ask(questions, &answers); err != nil {
		return fmt.Errorf("获取测试参数失败: %v", err)
	}
	
	// 2. 构建查询条件
	filters := make(map[string]interface{})
	if answers.Status != "所有" {
		filters["status"] = answers.Status
	}
	
	// 3. 获取代理列表
	fmt.Println("正在查询符合条件的代理...")
	proxies, err := m.ProxyTester.GetProxies(filters)
	if err != nil {
		return fmt.Errorf("获取代理列表失败: %v", err)
	}
	
	if len(proxies) == 0 {
		fmt.Println("没有找到符合条件的代理")
		return nil
	}
	
	fmt.Printf("找到 %d 个符合条件的代理\n", len(proxies))
	
	// 4. 测试代理
	fmt.Println("开始测试代理...")
	
	// 解析并发数
	concurrent, err := strconv.Atoi(answers.Concurrent)
	if err != nil || concurrent < 1 {
		concurrent = 5 // 默认值
	}
	
	// 创建通道和等待组
	resultChan := make(chan *testResult, len(proxies))
	var wg sync.WaitGroup
	semaphore := make(chan struct{}, concurrent)
	
	// 启动测试协程
	for _, proxy := range proxies {
		wg.Add(1)
		go func(p *Proxy) {
			defer wg.Done()
			
			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()
			
			// 测试代理
			start := time.Now()
			var result *Proxy
			var err error
			
			if p.AuthRequired {
				result, err = m.ProxyTester.TestWithAuth(p.IP, p.Port, p.Username, p.Password)
			} else {
				result, err = m.ProxyTester.TestNoAuth(p.IP, p.Port)
			}
			
			elapsed := time.Since(start)
			
			// 发送结果
			resultChan <- &testResult{
				Proxy:    p,
				NewProxy: result,
				Error:    err,
				Duration: elapsed,
			}
		}(proxy)
	}
	
	// 启动结果处理协程
	go func() {
		wg.Wait()
		close(resultChan)
	}()
	
	// 处理结果
	var successCount, failCount int
	for result := range resultChan {
		if result.Error != nil || (result.NewProxy != nil && result.NewProxy.Status != "可用") {
			fmt.Printf("[-] %s:%s - 测试失败: %v\n", result.Proxy.IP, result.Proxy.Port, result.Error)
			failCount++
		} else {
			fmt.Printf("[+] %s:%s - 测试成功 (延迟: %dms)\n", result.Proxy.IP, result.Proxy.Port, result.NewProxy.Latency)
			successCount++
			
			// 如果需要更新状态，保存测试结果
			if answers.UpdateStatus && result.NewProxy != nil {
				if err := m.ProxyTester.SaveTestResult(result.NewProxy); err != nil {
					fmt.Printf("    保存测试结果失败: %v\n", err)
				}
			}
		}
	}
	
	// 5. 显示测试摘要
	fmt.Printf("\n测试完成: 成功 %d 个, 失败 %d 个\n", successCount, failCount)
	if successCount+failCount > 0 {
		fmt.Printf("成功率: %.2f%%\n", float64(successCount)*100/float64(successCount+failCount))
	}
	
	return nil
}

// testResult 测试结果
type testResult struct {
	Proxy    *Proxy
	NewProxy *Proxy
	Error    error
	Duration time.Duration
}
