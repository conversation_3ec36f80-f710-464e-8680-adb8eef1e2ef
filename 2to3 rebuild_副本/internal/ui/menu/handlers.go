package menu

import (
	"bufio"
	"fmt"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/AlecAivazis/survey/v2"
)

// TestSingleProxy 测试单个代理
func (m *Menu) TestSingleProxy() error {
	// 收集代理信息
	var questions = []*survey.Question{
		{
			Name: "ip",
			Prompt: &survey.Input{
				Message: "请输入代理IP:",
			},
			Validate: survey.Required,
		},
		{
			Name: "port",
			Prompt: &survey.Input{
				Message: "请输入代理端口:",
			},
			Validate: survey.Required,
		},
		{
			Name: "useAuth",
			Prompt: &survey.Confirm{
				Message: "是否使用认证?",
				Default: false,
			},
		},
	}

	answers := struct {
		IP      string `survey:"ip"`
		Port    string `survey:"port"`
		UseAuth bool   `survey:"useAuth"`
	}{}

	err := survey.Ask(questions, &answers)
	if err != nil {
		return fmt.Errorf("收集代理信息失败: %v", err)
	}

	// 如果需要认证，收集认证信息
	var username, password string
	if answers.UseAuth {
		authQuestions := []*survey.Question{
			{
				Name: "username",
				Prompt: &survey.Input{
					Message: "请输入用户名:",
				},
				Validate: survey.Required,
			},
			{
				Name: "password",
				Prompt: &survey.Password{
					Message: "请输入密码:",
				},
				Validate: survey.Required,
			},
		}

		authAnswers := struct {
			Username string `survey:"username"`
			Password string `survey:"password"`
		}{}

		err := survey.Ask(authQuestions, &authAnswers)
		if err != nil {
			return fmt.Errorf("收集认证信息失败: %v", err)
		}

		username = authAnswers.Username
		password = authAnswers.Password
	}

	fmt.Printf("正在测试代理 %s:%s...\n", answers.IP, answers.Port)

	var result *Proxy
	var testErr error

	if answers.UseAuth {
		result, testErr = m.ProxyTester.TestWithAuth(answers.IP, answers.Port, username, password)
	} else {
		result, testErr = m.ProxyTester.TestNoAuth(answers.IP, answers.Port)
	}

	if testErr != nil {
		return fmt.Errorf("测试失败: %v", testErr)
	}

	// 显示测试结果
	fmt.Println("\n测试结果:")
	fmt.Printf("IP: %s\n", result.IP)
	fmt.Printf("端口: %s\n", result.Port)
	fmt.Printf("状态: %s\n", result.Status)
	if result.Status == "可用" {
		fmt.Printf("延迟: %dms\n", result.Latency)
	}

	// 保存测试结果
	if err := m.ProxyTester.SaveTestResult(result); err != nil {
		return fmt.Errorf("保存测试结果失败: %v", err)
	}

	fmt.Println("测试结果已保存到数据库")
	return nil
}

// TestBatchProxies 批量测试代理
func (m *Menu) TestBatchProxies() error {
	// 收集批量测试参数
	questions := []*survey.Question{
		{
			Name: "filePath",
			Prompt: &survey.Input{
				Message: "请输入代理列表文件路径:",
				Help:    "文件格式: 每行一个代理，格式为 IP PORT",
			},
			Validate: survey.Required,
		},
		{
			Name: "concurrent",
			Prompt: &survey.Input{
				Message: "请输入并发测试数量:",
				Default: "5",
			},
		},
		{
			Name: "testAuth",
			Prompt: &survey.Confirm{
				Message: "是否测试认证代理?",
				Default: true,
			},
		},
	}

	answers := struct {
		FilePath   string `survey:"filePath"`
		Concurrent int    `survey:"concurrent"`
		TestAuth   bool   `survey:"testAuth"`
	}{
		Concurrent: 5, // 默认值
	}

	err := survey.Ask(questions, &answers)
	if err != nil {
		return fmt.Errorf("收集批量测试参数失败: %v", err)
	}

	// 打开文件
	file, err := os.Open(answers.FilePath)
	if err != nil {
		return fmt.Errorf("打开文件失败: %v", err)
	}
	defer file.Close()

	// 读取代理列表
	var proxies [][]string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.Fields(line)
		if len(parts) >= 2 {
			proxies = append(proxies, parts)
		}
	}

	if err := scanner.Err(); err != nil {
		return fmt.Errorf("读取文件失败: %v", err)
	}

	fmt.Printf("加载了 %d 个代理\n", len(proxies))
	fmt.Println("开始测试代理...")

	return runBatchTest(m, proxies, answers.Concurrent, answers.TestAuth)
}

// runBatchTest 运行批量测试
func runBatchTest(m *Menu, proxies [][]string, concurrent int, testAuth bool) error {
	// 创建结果通道
	resultChan := make(chan *Proxy, len(proxies))
	errorChan := make(chan error, len(proxies))

	// 创建等待组
	var wg sync.WaitGroup

	// 创建信号量控制并发
	sem := make(chan struct{}, concurrent)

	// 启动测试协程
	for _, proxy := range proxies {
		if len(proxy) < 2 {
			continue
		}

		ip := proxy[0]
		port := proxy[1]

		wg.Add(1)

		go func(ip, port string) {
			defer wg.Done()

			// 获取信号量
			sem <- struct{}{}
			defer func() { <-sem }() // 释放信号量

			// 先测试无认证
			result, err := m.ProxyTester.TestNoAuth(ip, port)
			if err == nil && result != nil && result.Status == "可用" {
				resultChan <- result
				return
			}

			// 如果需要测试认证代理，使用 TestAllAuth 方法
			if testAuth {
				result, err := m.ProxyTester.TestAllAuth(ip, port)
				if err == nil && result != nil && result.Status == "可用" {
					fmt.Printf("代理 %s:%s 使用认证 %s:%s 测试成功\n",
						ip, port, result.Username, result.Password)
					resultChan <- result
					return
				}
			}

			errorChan <- fmt.Errorf("代理 %s:%s 测试失败", ip, port)
		}(ip, port)
	}

	// 设置超时
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	// 等待所有测试完成或超时
	select {
	case <-done:
		fmt.Println("所有测试已完成")
	case <-time.After(time.Duration(concurrent*30) * time.Second):
		fmt.Println("测试超时，部分代理可能未完成测试")
	}

	// 关闭通道
	close(resultChan)
	close(errorChan)

	// 统计结果
	var successCount, failCount int

	// 处理成功结果
	for result := range resultChan {
		successCount++
		fmt.Printf("代理可用: %s:%s, 延迟: %dms\n", result.IP, result.Port, result.Latency)

		// 保存测试结果
		if err := m.ProxyTester.SaveTestResult(result); err != nil {
			fmt.Printf("保存代理 %s:%s 失败: %v\n", result.IP, result.Port, err)
		}
	}

	// 处理错误
	for err := range errorChan {
		failCount++
		fmt.Println(err)
	}

	fmt.Printf("\n测试完成: 成功 %d 个, 失败 %d 个\n", successCount, failCount)
	return nil
}
