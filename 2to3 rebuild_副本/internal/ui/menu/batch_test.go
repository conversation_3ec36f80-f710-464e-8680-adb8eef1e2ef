package menu

import (
	"bufio"
	"fmt"
	"os"
	"strings"

	"github.com/AlecAivazis/survey/v2"
)

// TestBatchProxiesDetailed 批量测试代理（详细版本）
func (m *Menu) TestBatchProxiesDetailed() error {
	// 收集批量测试参数
	questions := []*survey.Question{
		{
			Name: "filePath",
			Prompt: &survey.Input{
				Message: "请输入代理列表文件路径:",
				Help:    "文件格式: 每行一个代理，格式为 IP PORT",
			},
			Validate: survey.Required,
		},
		{
			Name: "concurrent",
			Prompt: &survey.Input{
				Message: "请输入并发测试数量:",
				Default: "5",
			},
		},
		{
			Name: "testAuth",
			Prompt: &survey.Confirm{
				Message: "是否测试认证代理?",
				Default: true,
			},
		},
	}

	answers := struct {
		FilePath   string `survey:"filePath"`
		Concurrent int    `survey:"concurrent"`
		TestAuth   bool   `survey:"testAuth"`
	}{
		Concurrent: 5, // 默认值
	}

	err := survey.Ask(questions, &answers)
	if err != nil {
		return fmt.Errorf("收集批量测试参数失败: %v", err)
	}

	// 打开文件
	file, err := os.Open(answers.FilePath)
	if err != nil {
		return fmt.Errorf("打开文件失败: %v", err)
	}
	defer file.Close()

	// 读取代理列表
	var proxies [][]string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.Fields(line)
		if len(parts) >= 2 {
			proxies = append(proxies, parts)
		}
	}

	if err := scanner.Err(); err != nil {
		return fmt.Errorf("读取文件失败: %v", err)
	}

	fmt.Printf("加载了 %d 个代理\n", len(proxies))
	fmt.Println("开始测试代理...")

	return runBatchTest(m, proxies, answers.Concurrent, answers.TestAuth)
}

// runBatchTestDetailed 运行批量测试（详细版本）
func runBatchTestDetailed(m *Menu, proxies [][]string, concurrent int, testAuth bool) error {
	// 调用handlers.go中的runBatchTest函数
	return runBatchTest(m, proxies, concurrent, testAuth)
}
