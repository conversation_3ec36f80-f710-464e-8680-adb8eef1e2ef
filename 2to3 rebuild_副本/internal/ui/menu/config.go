package menu

import (
	"fmt"

	"github.com/AlecAivazis/survey/v2"
)

// ConfigSettings 配置设置
func (m *Menu) ConfigSettings() error {
	for {
		var choice string
		prompt := &survey.Select{
			Message: "请选择配置项:",
			Options: []string{
				"超时设置",
				"测试目标设置",
				"保存配置",
				"返回主菜单",
			},
		}
		err := survey.AskOne(prompt, &choice)
		if err != nil {
			return fmt.Errorf("配置选择错误: %v", err)
		}

		switch choice {
		case "超时设置":
			if err := m.SetTimeoutConfig(); err != nil {
				return err
			}
		case "测试目标设置":
			if err := m.SetTargetConfig(); err != nil {
				return err
			}
		case "保存配置":
			if err := m.SaveConfig(); err != nil {
				return err
			}
		case "返回主菜单":
			return nil
		}
	}
}

// SetTimeoutConfig 设置超时时间
func (m *Menu) SetTimeoutConfig() error {
	var timeout int
	promptTimeout := &survey.Input{
		Message: "请输入超时时间(秒):",
		Default: fmt.Sprintf("%d", m.Config.GetTimeoutSeconds()),
	}
	err := survey.AskOne(promptTimeout, &timeout)
	if err != nil {
		return fmt.Errorf("设置超时失败: %v", err)
	}
	m.Config.SetTimeoutSeconds(timeout)
	fmt.Printf("超时时间已设置为 %d 秒\n", timeout)
	return nil
}

// SetTargetConfig 设置测试目标
func (m *Menu) SetTargetConfig() error {
	var target string
	promptTarget := &survey.Input{
		Message: "请输入测试目标(格式: host:port):",
		Default: m.Config.GetTestTarget(),
	}
	err := survey.AskOne(promptTarget, &target)
	if err != nil {
		return fmt.Errorf("设置测试目标失败: %v", err)
	}
	m.Config.SetTestTarget(target)
	fmt.Printf("测试目标已设置为 %s\n", target)
	return nil
}

// SaveConfig 保存配置
func (m *Menu) SaveConfig() error {
	// 保存配置到文件
	if err := m.Config.Save("config.json"); err != nil {
		return fmt.Errorf("保存配置失败: %v", err)
	}
	fmt.Println("配置已保存")
	return nil
}
