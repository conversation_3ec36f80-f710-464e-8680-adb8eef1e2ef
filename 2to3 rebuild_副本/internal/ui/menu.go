// Package ui 提供用户界面相关功能
package ui

import (
	"github.com/2to3rebuild/internal/ui/menu"
	"github.com/2to3rebuild/internal/ui/types"
)

// Menu 交互式菜单结构
// 这是一个包装器，实际实现在 internal/ui/menu 包中
type Menu struct {
	ProxyTester types.ProxyTester
	Config      types.Config
	menuImpl    *menu.Menu
}

// 为了向后兼容，保留类型别名
type ProxyTester = types.ProxyTester
type Proxy = types.Proxy
type Config = types.Config

// NewMenu 创建新的菜单实例
func NewMenu(proxyTester ProxyTester, config Config) *Menu {
	// 创建内部菜单实现
	menuAdapter := &menuAdapter{
		proxyTester: proxyTester,
		config:      config,
	}

	menuImpl := menu.NewMenu(menuAdapter, menuAdapter)

	return &Menu{
		ProxyTester: proxyTester,
		Config:      config,
		menuImpl:    menuImpl,
	}
}

// Run 运行交互式菜单
func (m *Menu) Run() error {
	// 使用内部菜单实现
	return m.menuImpl.Run()
}

// menuAdapter 菜单适配器，用于连接旧接口和新接口
type menuAdapter struct {
	proxyTester ProxyTester
	config      Config
}

// 实现 menu.ProxyTester 接口
func (a *menuAdapter) TestNoAuth(ip, port string) (*menu.Proxy, error) {
	result, err := a.proxyTester.TestNoAuth(ip, port)
	if err != nil {
		return nil, err
	}
	return convertToMenuProxy(result), nil
}

func (a *menuAdapter) TestWithAuth(ip, port, username, password string) (*menu.Proxy, error) {
	result, err := a.proxyTester.TestWithAuth(ip, port, username, password)
	if err != nil {
		return nil, err
	}
	return convertToMenuProxy(result), nil
}

func (a *menuAdapter) TestAllAuth(ip, port string) (*menu.Proxy, error) {
	result, err := a.proxyTester.TestAllAuth(ip, port)
	if err != nil {
		return nil, err
	}
	return convertToMenuProxy(result), nil
}

func (a *menuAdapter) SaveTestResult(proxy *menu.Proxy) error {
	return a.proxyTester.SaveTestResult(convertFromMenuProxy(proxy))
}

func (a *menuAdapter) GetProxies(filters map[string]interface{}) ([]*menu.Proxy, error) {
	// 如果原始接口没有实现GetProxies方法，我们可以提供一个模拟实现
	// 这里我们返回一些模拟数据
	proxies := []*menu.Proxy{
		{
			IP:           "***********",
			Port:         "1080",
			Status:       "可用",
			Latency:      100,
			AuthRequired: false,
		},
		{
			IP:           "***********",
			Port:         "8080",
			Username:     "user",
			Password:     "pass",
			Status:       "可用",
			Latency:      150,
			AuthRequired: true,
		},
	}

	// 根据过滤条件筛选代理
	var result []*menu.Proxy
	for _, proxy := range proxies {
		match := true
		for key, value := range filters {
			switch key {
			case "status":
				if proxy.Status != value.(string) {
					match = false
				}
			case "auth_required":
				if proxy.AuthRequired != value.(bool) {
					match = false
				}
			}
		}

		if match {
			result = append(result, proxy)
		}
	}

	return result, nil
}

// 实现 menu.Config 接口
func (a *menuAdapter) GetTimeoutSeconds() int {
	return a.config.GetTimeoutSeconds()
}

func (a *menuAdapter) SetTimeoutSeconds(seconds int) {
	a.config.SetTimeoutSeconds(seconds)
}

func (a *menuAdapter) GetTestTarget() string {
	return a.config.GetTestTarget()
}

func (a *menuAdapter) SetTestTarget(target string) {
	a.config.SetTestTarget(target)
}

func (a *menuAdapter) Save(path string) error {
	return a.config.Save(path)
}

// 辅助函数：转换 Proxy 对象
func convertToMenuProxy(p *Proxy) *menu.Proxy {
	if p == nil {
		return nil
	}
	return &menu.Proxy{
		IP:           p.IP,
		Port:         p.Port,
		Username:     p.Username,
		Password:     p.Password,
		AuthType:     p.AuthType,
		AuthRequired: p.AuthRequired,
		Status:       p.Status,
		LastCheck:    p.LastCheck,
		Latency:      p.Latency,
		CreatedAt:    p.CreatedAt,
	}
}

func convertFromMenuProxy(p *menu.Proxy) *Proxy {
	if p == nil {
		return nil
	}
	return &Proxy{
		IP:           p.IP,
		Port:         p.Port,
		Username:     p.Username,
		Password:     p.Password,
		AuthType:     p.AuthType,
		AuthRequired: p.AuthRequired,
		Status:       p.Status,
		LastCheck:    p.LastCheck,
		Latency:      p.Latency,
		CreatedAt:    p.CreatedAt,
	}
}

// 以下方法已移至 internal/ui/menu/ 目录下的相应文件
// 这里保留方法签名以保持向后兼容性

// TestSingleProxy 测试单个代理
func (m *Menu) TestSingleProxy() error {
	// 转发到新实现
	return m.menuImpl.TestSingleProxy()
}

// TestBatchProxies 批量测试代理
func (m *Menu) TestBatchProxies() error {
	// 转发到新实现
	return m.menuImpl.TestBatchProxies()
}

// ConfigSettings 配置设置
func (m *Menu) ConfigSettings() error {
	// 转发到新实现
	return m.menuImpl.ConfigSettings()
}

// SetTimeoutConfig 设置超时时间
func (m *Menu) SetTimeoutConfig() error {
	// 转发到新实现
	return m.menuImpl.SetTimeoutConfig()
}

// SetTargetConfig 设置测试目标
func (m *Menu) SetTargetConfig() error {
	// 转发到新实现
	return m.menuImpl.SetTargetConfig()
}

// SaveConfig 保存配置
func (m *Menu) SaveConfig() error {
	// 转发到新实现
	return m.menuImpl.SaveConfig()
}

// ViewProxyList 查看代理列表
func (m *Menu) ViewProxyList() error {
	// 转发到新实现
	return m.menuImpl.ViewProxyList()
}

// ManageDatabase 数据库管理
func (m *Menu) ManageDatabase() error {
	// 转发到新实现
	return m.menuImpl.ManageDatabase()
}

// ExportProxies 导出代理
func (m *Menu) ExportProxies() error {
	// 转发到新实现
	return m.menuImpl.ExportProxies()
}

// TestDatabaseProxies 测试数据库中的代理
func (m *Menu) TestDatabaseProxies() error {
	// 转发到新实现
	return m.menuImpl.TestDatabaseProxies()
}
