// Package types 提供UI模块使用的类型定义
package types

// ProxyTester 代理测试接口
type ProxyTester interface {
	TestNoAuth(ip, port string) (*Proxy, error)
	TestWithAuth(ip, port, username, password string) (*Proxy, error)
	TestAllAuth(ip, port string) (*Proxy, error) // 测试所有认证信息
	SaveTestResult(proxy *Proxy) error
}

// Proxy 代理信息结构体
type Proxy struct {
	IP           string
	Port         string
	Username     string
	Password     string
	AuthType     string
	AuthRequired bool
	Status       string
	LastCheck    interface{}
	Latency      int64
	CreatedAt    interface{}
}

// Config 配置接口
type Config interface {
	GetTimeoutSeconds() int
	SetTimeoutSeconds(seconds int)
	GetTestTarget() string
	SetTestTarget(target string)
	Save(path string) error
}

// Menu 菜单接口
type Menu interface {
	Run() error
	TestSingleProxy() error
	TestBatchProxies() error
	ViewProxyList() error
	ManageDatabase() error
	ConfigSettings() error
	SetTimeoutConfig() error
	SetTargetConfig() error
	SaveConfig() error
}
