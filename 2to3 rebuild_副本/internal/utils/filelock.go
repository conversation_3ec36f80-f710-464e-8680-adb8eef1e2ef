// Package utils 提供工具函数
package utils

import (
	"fmt"
	"os"
	"sync"
	"time"
)

// FileLock 文件锁结构体
type FileLock struct {
	filePath string
	file     *os.File
	locked   bool
	mutex    sync.Mutex
}

// NewFileLock 创建新的文件锁
func NewFileLock(filePath string) *FileLock {
	return &FileLock{
		filePath: filePath,
		locked:   false,
	}
}

// Lock 获取文件锁
func (fl *FileLock) Lock() error {
	fl.mutex.Lock()
	defer fl.mutex.Unlock()

	if fl.locked {
		return fmt.Errorf("文件锁已被获取")
	}

	// 创建锁文件
	file, err := os.OpenFile(fl.filePath, os.O_CREATE|os.O_EXCL|os.O_WRONLY, 0666)
	if err != nil {
		return fmt.Errorf("获取文件锁失败: %v", err)
	}

	// 写入进程ID和时间戳
	pid := os.Getpid()
	timestamp := time.Now().Format(time.RFC3339)
	if _, err := file.WriteString(fmt.Sprintf("PID: %d\nTime: %s\n", pid, timestamp)); err != nil {
		file.Close()
		os.Remove(fl.filePath)
		return fmt.Errorf("写入锁文件信息失败: %v", err)
	}

	fl.file = file
	fl.locked = true
	return nil
}

// LockWithTimeout 带超时的获取文件锁
func (fl *FileLock) LockWithTimeout(timeout time.Duration) error {
	deadline := time.Now().Add(timeout)
	var lastErr error

	for time.Now().Before(deadline) {
		err := fl.Lock()
		if err == nil {
			return nil
		}
		lastErr = err

		// 检查锁文件是否存在
		if _, err := os.Stat(fl.filePath); os.IsNotExist(err) {
			// 锁文件不存在，可能是被其他进程删除了，尝试重新获取锁
			continue
		}

		// 检查锁文件是否过期
		if fl.isLockExpired(timeout) {
			// 锁文件过期，尝试强制解锁
			if err := fl.ForceUnlock(); err == nil {
				continue
			}
		}

		// 等待一段时间再重试
		time.Sleep(100 * time.Millisecond)
	}

	if lastErr != nil {
		return fmt.Errorf("获取文件锁超时: %v", lastErr)
	}
	return fmt.Errorf("获取文件锁超时")
}

// Unlock 释放文件锁
func (fl *FileLock) Unlock() error {
	fl.mutex.Lock()
	defer fl.mutex.Unlock()

	if !fl.locked {
		return fmt.Errorf("文件锁未被获取")
	}

	// 关闭文件
	if fl.file != nil {
		fl.file.Close()
		fl.file = nil
	}

	// 删除锁文件
	if err := os.Remove(fl.filePath); err != nil {
		return fmt.Errorf("删除锁文件失败: %v", err)
	}

	fl.locked = false
	return nil
}

// ForceUnlock 强制释放文件锁
func (fl *FileLock) ForceUnlock() error {
	fl.mutex.Lock()
	defer fl.mutex.Unlock()

	// 关闭文件
	if fl.file != nil {
		fl.file.Close()
		fl.file = nil
	}

	// 删除锁文件
	if err := os.Remove(fl.filePath); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("强制删除锁文件失败: %v", err)
	}

	fl.locked = false
	return nil
}

// IsLocked 检查文件锁是否被获取
func (fl *FileLock) IsLocked() bool {
	fl.mutex.Lock()
	defer fl.mutex.Unlock()
	return fl.locked
}

// isLockExpired 检查锁文件是否过期
func (fl *FileLock) isLockExpired(maxAge time.Duration) bool {
	info, err := os.Stat(fl.filePath)
	if err != nil {
		return false
	}

	// 如果锁文件的修改时间超过了最大年龄，认为锁已过期
	return time.Since(info.ModTime()) > maxAge
}
