package migrations

import (
	"database/sql"
	"fmt"
)

// UpdateDatabaseSchema 检查并更新数据库表结构
func UpdateDatabaseSchema(db *sql.DB) error {
	// 检查 proxies 表是否存在
	var tableName string
	err := db.QueryRow("SELECT name FROM sqlite_master WHERE type='table' AND name='proxies'").Scan(&tableName)
	if err != nil && err != sql.ErrNoRows {
		return fmt.Errorf("检查表是否存在失败: %v", err)
	}

	if tableName == "proxies" {
		// 检查 auth_type 列是否存在
		rows, err := db.Query("PRAGMA table_info(proxies)")
		if err != nil {
			return fmt.Errorf("获取表结构失败: %v", err)
		}
		defer rows.Close()

		hasAuthTypeColumn := false
		for rows.Next() {
			var cid, notnull, pk int
			var name, dataType string
			var dfltValue interface{}
			if err := rows.Scan(&cid, &name, &dataType, &notnull, &dfltValue, &pk); err != nil {
				return fmt.Errorf("读取列信息失败: %v", err)
			}
			if name == "auth_type" {
				hasAuthTypeColumn = true
				break
			}
		}

		// 如果 auth_type 列不存在，添加它
		if !hasAuthTypeColumn {
			_, err = db.Exec("ALTER TABLE proxies ADD COLUMN auth_type VARCHAR(20)")
			if err != nil {
				return fmt.Errorf("添加 auth_type 列失败: %v", err)
			}
			fmt.Println("已添加 auth_type 列到 proxies 表")
		}
	}

	return nil
}
