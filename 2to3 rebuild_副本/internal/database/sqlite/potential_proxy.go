// Package sqlite 提供SQLite数据库实现
package sqlite

import (
	"database/sql"
	"fmt"

	"github.com/2to3rebuild/internal/database"
)

// SavePotentialProxy 保存可能可用的代理
func (db *SQLiteDB) SavePotentialProxy(proxy *database.PotentialProxy) error {
	query := `
	INSERT OR REPLACE INTO potential_proxies
	(ip, port, source, location, discovered_at, tested)
	VALUES (?, ?, ?, ?, ?, ?)
	`

	_, err := db.db.Exec(
		query,
		proxy.IP,
		proxy.Port,
		proxy.Source,
		proxy.Location,
		proxy.DiscoveredAt,
		proxy.Tested,
	)

	if err != nil {
		return fmt.Errorf("保存可能可用代理失败: %v", err)
	}

	return nil
}

// GetPotentialProxies 获取可能可用的代理
func (db *SQLiteDB) GetPotentialProxies(tested bool, limit int) ([]*database.PotentialProxy, error) {
	query := `
	SELECT id, ip, port, source, location, discovered_at, tested, created_at
	FROM potential_proxies
	WHERE tested = ?
	ORDER BY discovered_at DESC
	LIMIT ?
	`

	rows, err := db.db.Query(query, tested, limit)
	if err != nil {
		return nil, fmt.Errorf("获取可能可用代理失败: %v", err)
	}
	defer rows.Close()

	var proxies []*database.PotentialProxy
	for rows.Next() {
		proxy := &database.PotentialProxy{}
		var discoveredAt sql.NullTime
		var createdAt sql.NullTime

		err := rows.Scan(
			&proxy.ID,
			&proxy.IP,
			&proxy.Port,
			&proxy.Source,
			&proxy.Location,
			&discoveredAt,
			&proxy.Tested,
			&createdAt,
		)

		if err != nil {
			return nil, fmt.Errorf("扫描代理数据失败: %v", err)
		}

		if discoveredAt.Valid {
			proxy.DiscoveredAt = discoveredAt.Time
		}

		if createdAt.Valid {
			proxy.CreatedAt = createdAt.Time
		}

		proxies = append(proxies, proxy)
	}

	return proxies, nil
}

// MarkPotentialProxyTested 标记可能可用的代理为已测试
func (db *SQLiteDB) MarkPotentialProxyTested(ip, port string, tested bool) error {
	query := `
	UPDATE potential_proxies
	SET tested = ?
	WHERE ip = ? AND port = ?
	`

	_, err := db.db.Exec(query, tested, ip, port)
	if err != nil {
		return fmt.Errorf("标记代理测试状态失败: %v", err)
	}

	return nil
}

// ImportPotentialProxies 批量导入可能可用的代理
func (db *SQLiteDB) ImportPotentialProxies(proxies []*database.PotentialProxy) (int, error) {
	tx, err := db.db.Begin()
	if err != nil {
		return 0, fmt.Errorf("开始事务失败: %v", err)
	}

	query := `
	INSERT OR IGNORE INTO potential_proxies
	(ip, port, source, location, discovered_at, tested)
	VALUES (?, ?, ?, ?, ?, ?)
	`

	stmt, err := tx.Prepare(query)
	if err != nil {
		tx.Rollback()
		return 0, fmt.Errorf("准备语句失败: %v", err)
	}
	defer stmt.Close()

	count := 0
	for _, proxy := range proxies {
		_, err := stmt.Exec(
			proxy.IP,
			proxy.Port,
			proxy.Source,
			proxy.Location,
			proxy.DiscoveredAt,
			proxy.Tested,
		)

		if err != nil {
			tx.Rollback()
			return count, fmt.Errorf("执行插入失败: %v", err)
		}

		count++
	}

	if err := tx.Commit(); err != nil {
		return count, fmt.Errorf("提交事务失败: %v", err)
	}

	return count, nil
}
