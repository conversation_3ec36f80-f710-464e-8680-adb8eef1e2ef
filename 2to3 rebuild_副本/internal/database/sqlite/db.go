package sqlite

import (
	"database/sql"
	"fmt"
	"sync"
	"time"

	_ "modernc.org/sqlite"
)

// SQLiteDB SQLite数据库实现
type SQLiteDB struct {
	db     *sql.DB
	dbPath string
	mu     sync.Mutex
}

// NewSQLiteDB 创建新的SQLite数据库实例
func NewSQLiteDB(dbPath string) *SQLiteDB {
	return &SQLiteDB{
		dbPath: dbPath,
	}
}

// Connect 连接数据库
func (s *SQLiteDB) Connect() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.db != nil {
		return nil
	}

	db, err := sql.Open("sqlite", s.dbPath)
	if err != nil {
		return fmt.Errorf("连接数据库失败: %v", err)
	}

	// 设置连接池
	db.SetMaxOpenConns(10)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(time.Hour)

	// 创建表
	if err := s.createTables(db); err != nil {
		db.Close()
		return err
	}

	s.db = db
	return nil
}

// Close 关闭数据库连接
func (s *SQLiteDB) Close() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.db != nil {
		err := s.db.Close()
		s.db = nil
		return err
	}
	return nil
}

// createTables 创建必要的表
func (s *SQLiteDB) createTables(db *sql.DB) error {
	// 创建代理表
	createProxyTable := `
CREATE TABLE IF NOT EXISTS proxies (
id INTEGER PRIMARY KEY AUTOINCREMENT,
ip VARCHAR(50) NOT NULL,
port VARCHAR(10) NOT NULL,
username VARCHAR(50),
password VARCHAR(50),
auth_type VARCHAR(20),
auth_required BOOLEAN DEFAULT false,
status VARCHAR(20),
last_check TIMESTAMP,
latency INTEGER,
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
UNIQUE(ip, port)
);`

	if _, err := db.Exec(createProxyTable); err != nil {
		return fmt.Errorf("创建代理表失败: %v", err)
	}

	// 创建认证信息表
	createCredentialsTable := `
CREATE TABLE IF NOT EXISTS credentials (
id INTEGER PRIMARY KEY AUTOINCREMENT,
username VARCHAR(50) NOT NULL,
password VARCHAR(50) NOT NULL,
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
UNIQUE(username, password)
);`

	if _, err := db.Exec(createCredentialsTable); err != nil {
		return fmt.Errorf("创建认证信息表失败: %v", err)
	}

	// 创建可能可用代理表
	createPotentialProxiesTable := `
CREATE TABLE IF NOT EXISTS potential_proxies (
id INTEGER PRIMARY KEY AUTOINCREMENT,
ip VARCHAR(50) NOT NULL,
port VARCHAR(10) NOT NULL,
source VARCHAR(100),
location VARCHAR(100),
discovered_at TIMESTAMP,
tested BOOLEAN DEFAULT 0,
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
UNIQUE(ip, port)
);`

	if _, err := db.Exec(createPotentialProxiesTable); err != nil {
		return fmt.Errorf("创建可能可用代理表失败: %v", err)
	}

	return nil
}
