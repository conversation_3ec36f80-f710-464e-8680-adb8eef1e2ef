package sqlite

import (
	"github.com/2to3rebuild/internal/database"
)

// SaveCredential 保存认证信息
func (s *SQLiteDB) SaveCredential(cred *database.Credential) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	stmt, err := s.db.Prepare(`
INSERT OR REPLACE INTO credentials (
username, password, created_at
) VALUES (?, ?, COALESCE(?, CURRENT_TIMESTAMP))
`)
	if err != nil {
		return err
	}
	defer stmt.Close()

	_, err = stmt.Exec(cred.Username, cred.Password, cred.CreatedAt)
	return err
}

// GetAllCredentials 获取所有认证信息
func (s *SQLiteDB) GetAllCredentials() ([]*database.Credential, error) {
	s.mu.Lock()
	defer s.mu.Unlock()

	rows, err := s.db.Query(`
SELECT id, username, password, created_at
FROM credentials
`)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var creds []*database.Credential
	for rows.Next() {
		cred := &database.Credential{}
		err := rows.Scan(&cred.ID, &cred.Username, &cred.Password, &cred.CreatedAt)
		if err != nil {
			return nil, err
		}
		creds = append(creds, cred)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return creds, nil
}

// DeleteCredential 删除认证信息
func (s *SQLiteDB) DeleteCredential(username, password string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	_, err := s.db.Exec("DELETE FROM credentials WHERE username = ? AND password = ?", username, password)
	return err
}
