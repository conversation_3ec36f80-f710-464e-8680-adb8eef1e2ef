package sqlite

import (
	"database/sql"

	"github.com/2to3rebuild/internal/database"
)

// BeginTx 开始事务
func (s *SQLiteDB) BeginTx() (database.Transaction, error) {
	s.mu.Lock()
	defer s.mu.Unlock()

	tx, err := s.db.Begin()
	if err != nil {
		return nil, err
	}
	return &sqliteTransaction{tx: tx}, nil
}

// sqliteTransaction SQLite事务实现
type sqliteTransaction struct {
	tx *sql.Tx
}

// Commit 提交事务
func (t *sqliteTransaction) Commit() error {
	return t.tx.Commit()
}

// Rollback 回滚事务
func (t *sqliteTransaction) Rollback() error {
	return t.tx.Rollback()
}
