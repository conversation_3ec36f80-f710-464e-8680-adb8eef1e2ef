package sqlite

import (
	"database/sql"
	"strings"

	"github.com/2to3rebuild/internal/database"
)

// SaveProxy 保存代理信息
func (s *SQLiteDB) SaveProxy(proxy *database.Proxy) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	stmt, err := s.db.Prepare(`
INSERT OR REPLACE INTO proxies (
ip, port, username, password, auth_type, auth_required,
status, last_check, latency, created_at
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, COALESCE(?, CURRENT_TIMESTAMP))
`)
	if err != nil {
		return err
	}
	defer stmt.Close()

	_, err = stmt.Exec(
		proxy.IP, proxy.Port, proxy.Username, proxy.Password,
		proxy.AuthType, proxy.AuthRequired, proxy.Status,
		proxy.LastCheck, proxy.Latency, proxy.CreatedAt,
	)
	return err
}

// GetProxy 获取代理信息
func (s *SQLiteDB) GetProxy(ip, port string) (*database.Proxy, error) {
	s.mu.Lock()
	defer s.mu.Unlock()

	proxy := &database.Proxy{}
	err := s.db.QueryRow(`
SELECT id, ip, port, username, password, auth_type,
auth_required, status, last_check, latency, created_at
FROM proxies WHERE ip = ? AND port = ?
`, ip, port).Scan(
		&proxy.ID, &proxy.IP, &proxy.Port, &proxy.Username,
		&proxy.Password, &proxy.AuthType, &proxy.AuthRequired, &proxy.Status,
		&proxy.LastCheck, &proxy.Latency, &proxy.CreatedAt,
	)

	if err == sql.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}
	return proxy, nil
}

// ListProxies 列出所有代理
func (s *SQLiteDB) ListProxies(filters map[string]interface{}) ([]*database.Proxy, error) {
	s.mu.Lock()
	defer s.mu.Unlock()

	baseQuery := `
SELECT id, ip, port, username, password, auth_type,
auth_required, status, last_check, latency, created_at
FROM proxies
`
	var conditions []string
	var args []interface{}

	// 处理过滤条件
	if len(filters) > 0 {
		for key, value := range filters {
			switch key {
			case "status":
				if status, ok := value.(string); ok && status != "所有" && status != "" {
					conditions = append(conditions, "status = ?")
					args = append(args, status)
				}
			case "auth_required":
				if authRequired, ok := value.(bool); ok {
					conditions = append(conditions, "auth_required = ?")
					args = append(args, authRequired)
				}
			case "auth_type":
				if authType, ok := value.(string); ok && authType != "" {
					conditions = append(conditions, "auth_type = ?")
					args = append(args, authType)
				}
			case "max_latency":
				if maxLatency, ok := value.(int64); ok && maxLatency > 0 {
					conditions = append(conditions, "latency <= ?")
					args = append(args, maxLatency)
				}
			case "min_latency":
				if minLatency, ok := value.(int64); ok && minLatency >= 0 {
					conditions = append(conditions, "latency >= ?")
					args = append(args, minLatency)
				}
			case "ip_like":
				if ipPattern, ok := value.(string); ok && ipPattern != "" {
					conditions = append(conditions, "ip LIKE ?")
					args = append(args, "%"+ipPattern+"%")
				}
			case "port":
				if port, ok := value.(string); ok && port != "" {
					conditions = append(conditions, "port = ?")
					args = append(args, port)
				}
			case "limit":
				// 这个会在后面处理
			case "order_by":
				// 这个会在后面处理
			}
		}
	}

	// 构建WHERE子句
	query := baseQuery
	if len(conditions) > 0 {
		query += " WHERE " + strings.Join(conditions, " AND ")
	}

	// 处理排序
	if orderBy, ok := filters["order_by"].(string); ok && orderBy != "" {
		query += " ORDER BY " + orderBy
	} else {
		// 默认按延迟排序（可用代理优先）
		query += " ORDER BY status DESC, latency ASC"
	}

	// 处理限制
	if limit, ok := filters["limit"].(int); ok && limit > 0 {
		query += " LIMIT ?"
		args = append(args, limit)
	}

	// 执行查询
	rows, err := s.db.Query(query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var proxies []*database.Proxy
	for rows.Next() {
		proxy := &database.Proxy{}
		err := rows.Scan(
			&proxy.ID, &proxy.IP, &proxy.Port, &proxy.Username,
			&proxy.Password, &proxy.AuthType, &proxy.AuthRequired, &proxy.Status,
			&proxy.LastCheck, &proxy.Latency, &proxy.CreatedAt,
		)
		if err != nil {
			return nil, err
		}
		proxies = append(proxies, proxy)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return proxies, nil
}

// DeleteProxy 删除代理
func (s *SQLiteDB) DeleteProxy(ip, port string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	_, err := s.db.Exec("DELETE FROM proxies WHERE ip = ? AND port = ?", ip, port)
	return err
}

// UpdateProxyStatus 更新代理状态
func (s *SQLiteDB) UpdateProxyStatus(ip, port, status string, latency int64) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	_, err := s.db.Exec(`
UPDATE proxies
SET status = ?, latency = ?, last_check = CURRENT_TIMESTAMP
WHERE ip = ? AND port = ?
`, status, latency, ip, port)
	return err
}
