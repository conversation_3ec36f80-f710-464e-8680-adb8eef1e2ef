package database

import (
	"time"
)

// Proxy 代理信息结构体
type Proxy struct {
	ID           int64
	IP           string
	Port         string
	Username     string
	Password     string
	AuthType     string // noauth, auth
	AuthRequired bool
	Status       string
	LastCheck    time.Time
	Latency      int64
	CreatedAt    time.Time
}

// Credential 认证信息结构体
type Credential struct {
	ID        int64
	Username  string
	Password  string
	CreatedAt time.Time
}

// PotentialProxy 可能可用的代理结构体
type PotentialProxy struct {
	ID           int64
	IP           string
	Port         string
	Source       string
	Location     string
	DiscoveredAt time.Time
	Tested       bool
	CreatedAt    time.Time
}

// ProxyStorage 代理存储接口
type ProxyStorage interface {
	// 代理相关操作
	SaveProxy(*Proxy) error
	GetProxy(ip, port string) (*Proxy, error)
	ListProxies(filters map[string]interface{}) ([]*Proxy, error)
	DeleteProxy(ip, port string) error
	UpdateProxyStatus(ip, port, status string, latency int64) error

	// 数据库连接管理
	Connect() error
	Close() error
}

// CredentialStorage 认证信息存储接口
type CredentialStorage interface {
	// 认证信息操作
	SaveCredential(*Credential) error
	GetAllCredentials() ([]*Credential, error)
	DeleteCredential(username, password string) error

	// 数据库连接管理
	Connect() error
	Close() error
}

// PotentialProxyStorage 可能可用代理存储接口
type PotentialProxyStorage interface {
	// 可能可用代理操作
	SavePotentialProxy(*PotentialProxy) error
	GetPotentialProxies(tested bool, limit int) ([]*PotentialProxy, error)
	MarkPotentialProxyTested(ip, port string, tested bool) error
	ImportPotentialProxies(proxies []*PotentialProxy) (int, error)

	// 数据库连接管理
	Connect() error
	Close() error
}

// Transaction 事务接口
type Transaction interface {
	Commit() error
	Rollback() error
}

// Storage 统一存储接口
type Storage interface {
	ProxyStorage
	CredentialStorage
	PotentialProxyStorage
	BeginTx() (Transaction, error)
}
