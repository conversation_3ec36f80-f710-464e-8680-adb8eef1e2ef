package config

import (
	"encoding/json"
	"fmt"
	"os"
	"time"
)

// Credential 存储代理认证信息
type Credential struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

// Config 配置结构体
type Config struct {
	Database struct {
		Path     string `json:"path"`
		PoolSize int    `json:"pool_size"`
	} `json:"database"`

	Proxy struct {
		TimeoutSeconds int          `json:"timeout_seconds"`
		RetryConfig    RetryConfig  `json:"retry_config"`
		TestTargets    []string     `json:"test_targets"`
		Credentials    []Credential `json:"credentials"`
	} `json:"proxy"`

	Output struct {
		Formats []string          `json:"formats"`
		Files   map[string]string `json:"file_mapping"`
	} `json:"output"`
}

// RetryConfig 重试配置
type RetryConfig struct {
	MaxAttempts int           `json:"max_attempts"`
	Interval    time.Duration `json:"interval"`
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	cfg := &Config{}

	// 设置默认数据库配置
	cfg.Database.Path = "proxies.db"
	cfg.Database.PoolSize = 10

	// 设置默认代理配置
	cfg.Proxy.TimeoutSeconds = 3
	cfg.Proxy.TestTargets = []string{"ipinfo.io:80"}
	cfg.Proxy.RetryConfig = RetryConfig{
		MaxAttempts: 2,
		Interval:    time.Second,
	}
	cfg.Proxy.Credentials = []Credential{
		{
			Username: "user",
			Password: "pass",
		},
	}

	// 设置默认输出配置
	cfg.Output.Formats = []string{"3", "30", "multi", "noauth"}
	cfg.Output.Files = map[string]string{
		"3":      "original.txt",
		"30":     "delay.txt",
		"multi":  "duplicate.txt",
		"noauth": "noauth.txt",
	}

	return cfg
}

// LoadConfig 从文件加载配置
func LoadConfig(path string) (*Config, error) {
	data, err := os.ReadFile(path)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	cfg := DefaultConfig()
	if err := json.Unmarshal(data, cfg); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	return cfg, nil
}

// Save 保存配置到文件
func (c *Config) Save(path string) error {
	data, err := json.MarshalIndent(c, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化配置失败: %v", err)
	}

	if err := os.WriteFile(path, data, 0644); err != nil {
		return fmt.Errorf("保存配置文件失败: %v", err)
	}

	return nil
}

// Validate 验证配置
func (c *Config) Validate() error {
	if c.Database.Path == "" {
		return fmt.Errorf("数据库路径不能为空")
	}

	if c.Database.PoolSize <= 0 {
		return fmt.Errorf("数据库连接池大小必须大于0")
	}

	if c.Proxy.TimeoutSeconds <= 0 {
		return fmt.Errorf("代理超时时间必须大于0")
	}

	if len(c.Proxy.TestTargets) == 0 {
		return fmt.Errorf("必须指定至少一个测试目标")
	}

	if c.Proxy.RetryConfig.MaxAttempts <= 0 {
		return fmt.Errorf("最大重试次数必须大于0")
	}

	if c.Proxy.RetryConfig.Interval <= 0 {
		return fmt.Errorf("重试间隔必须大于0")
	}

	// 验证认证信息
	for i, cred := range c.Proxy.Credentials {
		if cred.Username == "" {
			return fmt.Errorf("认证信息 #%d 的用户名不能为空", i+1)
		}
		if cred.Password == "" {
			return fmt.Errorf("认证信息 #%d 的密码不能为空", i+1)
		}
	}

	return nil
}

// GetTimeoutSeconds 获取超时时间
func (c *Config) GetTimeoutSeconds() int {
	return c.Proxy.TimeoutSeconds
}

// SetTimeoutSeconds 设置超时时间
func (c *Config) SetTimeoutSeconds(seconds int) {
	c.Proxy.TimeoutSeconds = seconds
}

// GetTestTarget 获取测试目标
func (c *Config) GetTestTarget() string {
	if len(c.Proxy.TestTargets) > 0 {
		return c.Proxy.TestTargets[0]
	}
	return ""
}

// SetTestTarget 设置测试目标
func (c *Config) SetTestTarget(target string) {
	if len(c.Proxy.TestTargets) > 0 {
		c.Proxy.TestTargets[0] = target
	} else {
		c.Proxy.TestTargets = []string{target}
	}
}

// GetCredentials 获取认证信息
func (c *Config) GetCredentials() []Credential {
	return c.Proxy.Credentials
}

// AddCredential 添加认证信息
func (c *Config) AddCredential(username, password string) {
	c.Proxy.Credentials = append(c.Proxy.Credentials, Credential{
		Username: username,
		Password: password,
	})
}

// RemoveCredential 删除认证信息
func (c *Config) RemoveCredential(username, password string) bool {
	for i, cred := range c.Proxy.Credentials {
		if cred.Username == username && cred.Password == password {
			c.Proxy.Credentials = append(c.Proxy.Credentials[:i], c.Proxy.Credentials[i+1:]...)
			return true
		}
	}
	return false
}
