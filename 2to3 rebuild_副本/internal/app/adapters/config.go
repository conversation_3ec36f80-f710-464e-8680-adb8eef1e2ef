// Package adapters 提供适配器实现
package adapters

import (
	"github.com/2to3rebuild/internal/config"
)

// ConfigAdapter 配置适配器，实现ui.Config接口
type ConfigAdapter struct {
	Config     *config.Config
	ConfigPath string
}

// NewConfigAdapter 创建配置适配器
func NewConfigAdapter(cfg *config.Config, configPath string) *ConfigAdapter {
	return &ConfigAdapter{
		Config:     cfg,
		ConfigPath: configPath,
	}
}

// GetTimeoutSeconds 获取超时时间
func (ca *ConfigAdapter) GetTimeoutSeconds() int {
	return ca.Config.Proxy.TimeoutSeconds
}

// SetTimeoutSeconds 设置超时时间
func (ca *ConfigAdapter) SetTimeoutSeconds(seconds int) {
	ca.Config.Proxy.TimeoutSeconds = seconds
}

// GetTestTarget 获取测试目标
func (ca *ConfigAdapter) GetTestTarget() string {
	if len(ca.Config.Proxy.TestTargets) > 0 {
		return ca.Config.Proxy.TestTargets[0]
	}
	return "ipinfo.io:80"
}

// SetTestTarget 设置测试目标
func (ca *ConfigAdapter) SetTestTarget(target string) {
	if len(ca.Config.Proxy.TestTargets) > 0 {
		ca.Config.Proxy.TestTargets[0] = target
	} else {
		ca.Config.Proxy.TestTargets = []string{target}
	}
}

// Save 保存配置
func (ca *ConfigAdapter) Save(path string) error {
	// 如果路径为空，使用默认路径
	if path == "" {
		path = ca.ConfigPath
	}

	// 保存配置
	return ca.Config.Save(path)
}
