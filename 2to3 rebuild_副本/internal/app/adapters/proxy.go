package adapters

import (
	"fmt"
	"time"

	"github.com/2to3rebuild/internal/database"
	"github.com/2to3rebuild/internal/proxy"
	"github.com/2to3rebuild/internal/ui"
)

// ProxyTesterAdapter 代理测试器适配器，实现ui.ProxyTester接口
type ProxyTesterAdapter struct {
	Tester *proxy.DefaultTester
}

// NewProxyTesterAdapter 创建代理测试器适配器
func NewProxyTesterAdapter(tester *proxy.DefaultTester) *ProxyTesterAdapter {
	return &ProxyTesterAdapter{
		Tester: tester,
	}
}

// TestNoAuth 测试无认证代理
func (pta *ProxyTesterAdapter) TestNoAuth(ip, port string) (*ui.Proxy, error) {
	result, err := pta.Tester.TestNoAuth(ip, port)
	if err != nil {
		return nil, fmt.Errorf("测试无认证代理失败: %v", err)
	}

	if result.Status != "可用" {
		return &ui.Proxy{
			IP:        result.IP,
			Port:      result.Port,
			Status:    result.Status,
			AuthType:  "noauth",
			LastCheck: result.TestedAt,
		}, nil
	}

	return &ui.Proxy{
		IP:        result.IP,
		Port:      result.Port,
		Status:    result.Status,
		Latency:   result.Latency,
		AuthType:  "noauth",
		LastCheck: result.TestedAt,
	}, nil
}

// TestWithAuth 测试带认证代理
func (pta *ProxyTesterAdapter) TestWithAuth(ip, port, username, password string) (*ui.Proxy, error) {
	result, err := pta.Tester.TestWithAuth(ip, port, username, password)
	if err != nil {
		return nil, fmt.Errorf("测试带认证代理失败: %v", err)
	}

	if result.Status != "可用" {
		return &ui.Proxy{
			IP:        result.IP,
			Port:      result.Port,
			Username:  username,
			Password:  password,
			Status:    result.Status,
			AuthType:  "auth",
			LastCheck: result.TestedAt,
		}, nil
	}

	return &ui.Proxy{
		IP:        result.IP,
		Port:      result.Port,
		Username:  username,
		Password:  password,
		Status:    result.Status,
		Latency:   result.Latency,
		AuthType:  "auth",
		LastCheck: result.TestedAt,
	}, nil
}

// TestAllAuth 测试所有认证信息
func (pta *ProxyTesterAdapter) TestAllAuth(ip, port string) (*ui.Proxy, error) {
	// TestProxy方法会先尝试无认证，然后尝试所有认证方式
	result, err := pta.Tester.TestProxy(ip, port)
	if err != nil {
		return nil, fmt.Errorf("测试代理失败: %v", err)
	}

	// 根据测试结果设置认证类型
	authType := "noauth"
	if result.AuthRequired {
		authType = "auth"
	}

	// 转换为UI层的Proxy对象
	return &ui.Proxy{
		IP:        result.IP,
		Port:      result.Port,
		Username:  result.Username,
		Password:  result.Password,
		Status:    result.Status,
		Latency:   result.Latency,
		AuthType:  authType,
		LastCheck: result.TestedAt,
	}, nil
}

// SaveTestResult 保存测试结果
func (pta *ProxyTesterAdapter) SaveTestResult(proxy *ui.Proxy) error {
	// 创建数据库代理对象
	dbProxy := &database.Proxy{
		IP:        proxy.IP,
		Port:      proxy.Port,
		Username:  proxy.Username,
		Password:  proxy.Password,
		Status:    proxy.Status,
		Latency:   proxy.Latency,
		LastCheck: time.Now(),
	}

	// 设置认证类型
	if proxy.AuthType == "auth" {
		dbProxy.AuthType = "auth"
		dbProxy.AuthRequired = true
	} else {
		dbProxy.AuthType = "noauth"
		dbProxy.AuthRequired = false
	}

	// 直接保存到数据库
	return pta.Tester.DB().SaveProxy(dbProxy)
}

// GetProxies 获取代理列表
func (pta *ProxyTesterAdapter) GetProxies(filters map[string]interface{}) ([]*ui.Proxy, error) {
	// 从数据库获取代理列表
	dbProxies, err := pta.Tester.DB().ListProxies(filters)
	if err != nil {
		return nil, fmt.Errorf("获取代理列表失败: %v", err)
	}

	// 转换为UI层的Proxy对象
	var uiProxies []*ui.Proxy
	for _, dbProxy := range dbProxies {
		uiProxy := &ui.Proxy{
			IP:           dbProxy.IP,
			Port:         dbProxy.Port,
			Username:     dbProxy.Username,
			Password:     dbProxy.Password,
			Status:       dbProxy.Status,
			Latency:      dbProxy.Latency,
			LastCheck:    dbProxy.LastCheck,
			CreatedAt:    dbProxy.CreatedAt,
			AuthRequired: dbProxy.AuthRequired,
			AuthType:     dbProxy.AuthType,
		}
		uiProxies = append(uiProxies, uiProxy)
	}

	return uiProxies, nil
}
