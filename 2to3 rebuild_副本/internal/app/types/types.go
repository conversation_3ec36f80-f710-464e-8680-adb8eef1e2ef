// Package types 提供应用程序类型定义
package types

import (
	"time"

	"github.com/2to3rebuild/internal/proxy"
	"github.com/2to3rebuild/internal/ui"
)

// TestResult 测试结果
type TestResult struct {
	IP           string
	Port         string
	Username     string
	Password     string
	Status       string    // 成功/失败
	Error        string    // 错误信息
	Latency      int64     // 延迟（毫秒）
	TestedAt     time.Time // 测试时间
	RetryCount   int       // 重试次数
	AuthRequired bool      // 是否需要认证
	AuthSuccess  bool      // 认证是否成功
}

// ToProxyTestResult 转换为proxy.TestResult
func (tr *TestResult) ToProxyTestResult() *proxy.TestResult {
	return &proxy.TestResult{
		IP:           tr.IP,
		Port:         tr.Port,
		Username:     tr.Username,
		Password:     tr.Password,
		Status:       tr.Status,
		Error:        tr.Error,
		Latency:      tr.Latency,
		TestedAt:     tr.TestedAt,
		RetryCount:   tr.RetryCount,
		AuthRequired: tr.AuthRequired,
		AuthSuccess:  tr.AuthSuccess,
	}
}

// FromUIProxy 从ui.Proxy创建TestResult
func FromUIProxy(proxy *ui.Proxy) *TestResult {
	return &TestResult{
		IP:           proxy.IP,
		Port:         proxy.Port,
		Username:     proxy.Username,
		Password:     proxy.Password,
		Status:       proxy.Status,
		Latency:      proxy.Latency,
		TestedAt:     time.Now(),
		RetryCount:   0,
		AuthRequired: proxy.AuthType == "auth",
		AuthSuccess:  proxy.Status == "可用" && proxy.AuthType == "auth",
		Error:        "",
	}
}
