// Package core 提供应用程序核心功能
package core

import (
	"fmt"

	"github.com/2to3rebuild/internal/app/adapters"
	"github.com/2to3rebuild/internal/config"
	"github.com/2to3rebuild/internal/database"
	"github.com/2to3rebuild/internal/database/sqlite"
	"github.com/2to3rebuild/internal/proxy"
	"github.com/2to3rebuild/internal/ui"
)

// App 应用程序结构体
type App struct {
	Config      *config.Config
	DB          database.Storage
	ProxyTester *proxy.DefaultTester
	Menu        *ui.Menu
}

// NewApp 创建应用程序实例
func NewApp(configPath, dbPath string) (*App, error) {
	// 加载配置
	cfg, err := config.LoadConfig(configPath)
	if err != nil {
		return nil, fmt.Errorf("加载配置失败: %v", err)
	}

	// 初始化数据库
	db := sqlite.NewSQLiteDB(dbPath)
	if err := db.Connect(); err != nil {
		return nil, fmt.Errorf("连接数据库失败: %v", err)
	}

	// 创建代理测试器
	tester := proxy.NewDefaultTester(cfg, db)

	// 创建配置适配器
	configAdapter := adapters.NewConfigAdapter(cfg, configPath)

	// 创建代理测试器适配器
	testerAdapter := adapters.NewProxyTesterAdapter(tester)

	// 创建菜单
	menu := ui.NewMenu(testerAdapter, configAdapter)

	return &App{
		Config:      cfg,
		DB:          db,
		ProxyTester: tester,
		Menu:        menu,
	}, nil
}

// Run 运行应用程序
func (a *App) Run(interactive bool, testIP, testPort string) error {
	// 如果启用了交互式模式，显示交互式菜单
	if interactive {
		return a.Menu.Run()
	}

	// 如果提供了测试IP和端口，进行测试
	if testIP != "" && testPort != "" {
		fmt.Printf("开始测试代理 %s:%s\n", testIP, testPort)

		// 使用 TestProxy 函数测试代理，它会尝试所有认证方式
		result, err := a.ProxyTester.TestProxy(testIP, testPort)
		if err != nil {
			return fmt.Errorf("测试代理失败: %v", err)
		}

		// 打印测试结果
		fmt.Printf("测试结果 - IP: %s, 端口: %s, 状态: %s", result.IP, result.Port, result.Status)
		if result.Status == "可用" {
			fmt.Printf(", 延迟: %dms", result.Latency)
			if result.AuthRequired {
				fmt.Printf(", 认证类型: auth, 用户名: %s", result.Username)
			} else {
				fmt.Printf(", 认证类型: noauth")
			}
		}
		fmt.Println()

		return nil
	}

	fmt.Println("SOCKS5代理测试工具初始化完成")
	fmt.Println("使用 -ip 和 -port 参数来测试代理")
	fmt.Println("或使用 -i 参数启动交互式菜单")
	return nil
}

// Close 关闭应用程序
func (a *App) Close() error {
	if a.DB != nil {
		return a.DB.Close()
	}
	return nil
}
