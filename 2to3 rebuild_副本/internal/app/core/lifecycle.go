package core

import (
	"fmt"
	"os"
	"os/signal"
	"syscall"
)

// SetupSignalHandling 设置信号处理
func (a *App) SetupSignalHandling() {
	// 创建信号通道
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 启动协程处理信号
	go func() {
		sig := <-sigChan
		fmt.Printf("\n接收到信号: %v\n", sig)
		fmt.Println("正在关闭应用程序...")

		// 关闭应用程序
		if err := a.Close(); err != nil {
			fmt.Printf("关闭应用程序时出错: %v\n", err)
			os.Exit(1)
		}

		fmt.Println("应用程序已安全关闭")
		os.Exit(0)
	}()
}

// Initialize 初始化应用程序
func (a *App) Initialize() error {
	// 设置信号处理
	a.SetupSignalHandling()

	// 数据库表已经在 Connect 方法中初始化
	// 不需要额外调用 InitTables 方法

	return nil
}

// Shutdown 关闭应用程序
func (a *App) Shutdown() error {
	fmt.Println("正在关闭应用程序...")

	// 关闭数据库连接
	if err := a.Close(); err != nil {
		return fmt.Errorf("关闭数据库连接失败: %v", err)
	}

	fmt.Println("应用程序已安全关闭")
	return nil
}
