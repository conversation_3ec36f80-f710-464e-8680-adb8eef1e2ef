#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
可能可用代理测试工具

这个脚本用于测试数据库中的可能可用代理，并将可用的代理保存到代理表中。
它会从potential_proxies表中获取未测试的代理，通过API测试它们，
然后将测试结果保存到proxies表中。

用法:
    python test_potential_proxies.py [选项]

选项:
    --db DB             指定数据库文件路径，默认为 proxies.db
    --api-port PORT     API服务器端口，默认为 8080
    --batch-size SIZE   每批测试的代理数量，默认为 10
    --concurrent NUM    并发测试数量，默认为 5
    --limit LIMIT       限制测试数量，默认为全部测试
    --help              显示帮助信息
"""

import argparse
import datetime
import json
import sqlite3
import sys
import time
import urllib.request
import urllib.error
import urllib.parse
import threading
import queue


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="可能可用代理测试工具")
    parser.add_argument("--db", default="proxies.db", help="数据库文件路径")
    parser.add_argument("--api-port", type=int, default=8080, help="API服务器端口")
    parser.add_argument("--batch-size", type=int, default=10, help="每批测试的代理数量")
    parser.add_argument("--concurrent", type=int, default=5, help="并发测试数量")
    parser.add_argument("--limit", type=int, help="限制测试数量")
    return parser.parse_args()


def connect_db(db_path):
    """连接到SQLite数据库"""
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        return conn
    except sqlite3.Error as e:
        print(f"数据库连接错误: {e}")
        sys.exit(1)


def get_untested_proxies(conn, limit=None):
    """获取未测试的代理"""
    cursor = conn.cursor()
    
    query = """
    SELECT id, ip, port, source, location, discovered_at
    FROM potential_proxies
    WHERE tested = 0
    ORDER BY discovered_at DESC
    """
    
    if limit:
        query += f" LIMIT {limit}"
    
    try:
        cursor.execute(query)
        rows = cursor.fetchall()
        
        proxies = []
        for row in rows:
            proxies.append({
                'id': row['id'],
                'ip': row['ip'],
                'port': row['port'],
                'source': row['source'],
                'location': row['location']
            })
        
        return proxies
    except sqlite3.Error as e:
        print(f"获取未测试代理失败: {e}")
        return []


def mark_proxy_tested(conn, proxy_id):
    """标记代理为已测试"""
    cursor = conn.cursor()
    
    try:
        cursor.execute("""
        UPDATE potential_proxies 
        SET tested = 1 
        WHERE id = ?
        """, (proxy_id,))
        
        conn.commit()
        return True
    except sqlite3.Error as e:
        print(f"标记代理 ID:{proxy_id} 为已测试失败: {e}")
        conn.rollback()
        return False


def test_proxy_via_api(proxy, api_port, concurrent):
    """通过API测试代理"""
    url = f"http://localhost:{api_port}/api/proxies/test"
    data = {
        "proxies": [
            {
                "ip": proxy["ip"],
                "port": proxy["port"]
            }
        ],
        "concurrent": concurrent
    }
    
    try:
        req = urllib.request.Request(
            url,
            data=json.dumps(data).encode('utf-8'),
            headers={'Content-Type': 'application/json'}
        )
        
        with urllib.request.urlopen(req, timeout=60) as response:
            result = json.loads(response.read().decode('utf-8'))
            
            if result.get("success") and result.get("results"):
                proxy_result = result["results"][0]
                return {
                    "status": proxy_result.get("status"),
                    "latency": proxy_result.get("latency", 0),
                    "auth_required": proxy_result.get("auth_required", False),
                    "username": proxy_result.get("username", ""),
                    "password": proxy_result.get("password", "")
                }
    except Exception as e:
        print(f"测试代理 {proxy['ip']}:{proxy['port']} 失败: {e}")
    
    return None


def save_working_proxy(conn, proxy, test_result):
    """保存可用代理到数据库"""
    if test_result["status"] != "可用":
        return False
    
    cursor = conn.cursor()
    now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    try:
        cursor.execute("""
        INSERT OR REPLACE INTO proxies 
        (ip, port, username, password, auth_type, auth_required, status, last_check, latency) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            proxy["ip"],
            proxy["port"],
            test_result["username"],
            test_result["password"],
            "auth" if test_result["auth_required"] else "noauth",
            test_result["auth_required"],
            test_result["status"],
            now,
            test_result["latency"]
        ))
        
        conn.commit()
        return True
    except sqlite3.Error as e:
        print(f"保存可用代理 {proxy['ip']}:{proxy['port']} 失败: {e}")
        conn.rollback()
        return False


def worker(task_queue, result_queue, api_port, concurrent):
    """工作线程函数"""
    while True:
        proxy = task_queue.get()
        if proxy is None:
            task_queue.task_done()
            break
        
        print(f"测试代理: {proxy['ip']}:{proxy['port']}")
        test_result = test_proxy_via_api(proxy, api_port, concurrent)
        
        if test_result:
            result_queue.put((proxy, test_result))
            if test_result["status"] == "可用":
                print(f"代理 {proxy['ip']}:{proxy['port']} 可用，延迟: {test_result['latency']}ms")
                if test_result["auth_required"]:
                    print(f"认证信息: {test_result['username']}:{test_result['password']}")
        else:
            result_queue.put((proxy, {"status": "不可用"}))
        
        task_queue.task_done()


def process_results(conn, result_queue):
    """处理测试结果"""
    working_count = 0
    failed_count = 0
    
    while not result_queue.empty():
        proxy, test_result = result_queue.get()
        
        # 标记为已测试
        mark_proxy_tested(conn, proxy["id"])
        
        # 如果测试成功，保存到代理表
        if test_result["status"] == "可用":
            if save_working_proxy(conn, proxy, test_result):
                working_count += 1
        else:
            failed_count += 1
    
    return working_count, failed_count


def test_proxies_in_batches(conn, proxies, api_port, batch_size, concurrent):
    """分批测试代理"""
    total_working = 0
    total_failed = 0
    
    for i in range(0, len(proxies), batch_size):
        batch = proxies[i:i+batch_size]
        print(f"\n测试批次 {i//batch_size + 1}/{(len(proxies) + batch_size - 1)//batch_size}，共 {len(batch)} 个代理")
        
        # 创建任务队列和结果队列
        task_queue = queue.Queue()
        result_queue = queue.Queue()
        
        # 添加任务
        for proxy in batch:
            task_queue.put(proxy)
        
        # 创建工作线程
        threads = []
        for _ in range(min(concurrent, len(batch))):
            thread = threading.Thread(
                target=worker,
                args=(task_queue, result_queue, api_port, 1)
            )
            thread.start()
            threads.append(thread)
            task_queue.put(None)  # 添加结束标记
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 处理结果
        working, failed = process_results(conn, result_queue)
        total_working += working
        total_failed += failed
        
        print(f"批次完成: 可用 {working} 个, 不可用 {failed} 个")
        
        # 避免请求过快
        if i + batch_size < len(proxies):
            print("等待5秒后继续下一批...")
            time.sleep(5)
    
    return total_working, total_failed


def main():
    """主函数"""
    args = parse_args()
    
    # 连接数据库
    conn = connect_db(args.db)
    
    # 获取未测试的代理
    proxies = get_untested_proxies(conn, args.limit)
    print(f"找到 {len(proxies)} 个未测试的代理")
    
    if not proxies:
        print("没有未测试的代理，退出")
        conn.close()
        return
    
    # 测试代理
    print("开始测试代理...")
    working_count, failed_count = test_proxies_in_batches(
        conn, proxies, args.api_port, args.batch_size, args.concurrent
    )
    
    print(f"\n测试完成: 共测试 {len(proxies)} 个代理，可用 {working_count} 个，不可用 {failed_count} 个")
    
    conn.close()
    print("操作完成")


if __name__ == "__main__":
    main()
