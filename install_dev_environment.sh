#!/bin/bash

# 全栈开发环境安装脚本
# 适用于 macOS，处理网络问题并显示进度
# 使用本地代理加速下载

set -e  # 遇到错误立即退出

# 代理配置
PROXY_HOST="127.0.0.1"
PROXY_PORT="12334"
PROXY_URL="socks5://${PROXY_HOST}:${PROXY_PORT}"

# 设置代理环境变量
export http_proxy="$PROXY_URL"
export https_proxy="$PROXY_URL"
export HTTP_PROXY="$PROXY_URL"
export HTTPS_PROXY="$PROXY_URL"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 进度显示函数
show_progress() {
    local pid=$1
    local delay=0.1
    local spinstr='|/-\'
    echo -n " "
    while [ "$(ps a | awk '{print $1}' | grep $pid)" ]; do
        local temp=${spinstr#?}
        printf " [%c]  " "$spinstr"
        local spinstr=$temp${spinstr%"$temp"}
        sleep $delay
        printf "\b\b\b\b\b\b"
    done
    printf "    \b\b\b\b"
}

# 检查代理连接
check_network() {
    echo -e "${BLUE}🌐 检查代理连接 (${PROXY_HOST}:${PROXY_PORT})...${NC}"

    # 测试代理连接
    if curl -x "$PROXY_URL" --connect-timeout 10 -s -o /dev/null -w "%{http_code}" http://www.google.com | grep -q "200\|302"; then
        echo -e "${GREEN}✅ 代理连接正常${NC}"
    else
        echo -e "${YELLOW}⚠️  代理连接失败，尝试直连...${NC}"
        unset http_proxy https_proxy HTTP_PROXY HTTPS_PROXY
        if ! ping -c 1 google.com &> /dev/null; then
            echo -e "${RED}❌ 网络连接失败，请检查网络设置或代理配置${NC}"
            exit 1
        fi
        echo -e "${GREEN}✅ 直连网络正常${NC}"
    fi
}

# 安装 Homebrew（带重试机制）
install_homebrew() {
    if command -v brew &> /dev/null; then
        echo -e "${GREEN}✅ Homebrew 已安装${NC}"
        return 0
    fi
    
    echo -e "${BLUE}📦 安装 Homebrew...${NC}"
    
    # 使用代理和国内镜像源加速安装
    export HOMEBREW_INSTALL_FROM_API=1
    export HOMEBREW_API_DOMAIN="https://mirrors.tuna.tsinghua.edu.cn/homebrew-bottles/api"
    export HOMEBREW_BOTTLE_DOMAIN="https://mirrors.tuna.tsinghua.edu.cn/homebrew-bottles"
    export HOMEBREW_BREW_GIT_REMOTE="https://mirrors.tuna.tsinghua.edu.cn/git/homebrew/brew.git"
    export HOMEBREW_CORE_GIT_REMOTE="https://mirrors.tuna.tsinghua.edu.cn/git/homebrew/homebrew-core.git"

    # 为 curl 和 git 配置代理
    if [ -n "$http_proxy" ]; then
        export HOMEBREW_CURLRC=1
        echo "proxy = $PROXY_URL" > ~/.curlrc
        git config --global http.proxy "$PROXY_URL"
        git config --global https.proxy "$PROXY_URL"
    fi
    
    # 重试机制
    local max_attempts=3
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        echo -e "${YELLOW}📥 尝试安装 Homebrew (第 $attempt 次)...${NC}"
        
        if /bin/bash -c "$(curl -fsSL https://gitee.com/ineo6/homebrew-install/raw/master/install.sh)"; then
            echo -e "${GREEN}✅ Homebrew 安装成功${NC}"
            
            # 添加到 PATH
            echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zprofile
            eval "$(/opt/homebrew/bin/brew shellenv)"
            
            return 0
        else
            echo -e "${RED}❌ 第 $attempt 次安装失败${NC}"
            attempt=$((attempt + 1))
            if [ $attempt -le $max_attempts ]; then
                echo -e "${YELLOW}⏳ 等待 5 秒后重试...${NC}"
                sleep 5
            fi
        fi
    done
    
    echo -e "${RED}❌ Homebrew 安装失败，请手动安装${NC}"
    exit 1
}

# 安装 Go
install_go() {
    echo -e "${BLUE}🐹 安装 Go...${NC}"
    
    if command -v go &> /dev/null; then
        local current_version=$(go version | awk '{print $3}' | sed 's/go//')
        echo -e "${GREEN}✅ Go 已安装 (版本: $current_version)${NC}"
        return 0
    fi
    
    brew install go &
    show_progress $!
    wait
    
    if command -v go &> /dev/null; then
        echo -e "${GREEN}✅ Go 安装成功 ($(go version))${NC}"
        
        # 配置 Go 环境变量
        echo 'export GOPATH=$HOME/go' >> ~/.zprofile
        echo 'export PATH=$PATH:$GOPATH/bin' >> ~/.zprofile
        export GOPATH=$HOME/go
        export PATH=$PATH:$GOPATH/bin
        
        # 配置 Go 代理（加速模块下载）
        go env -w GOPROXY=https://goproxy.cn,direct
        go env -w GOSUMDB=sum.golang.google.cn
        
    else
        echo -e "${RED}❌ Go 安装失败${NC}"
        exit 1
    fi
}

# 安装 Python
install_python() {
    echo -e "${BLUE}🐍 安装 Python...${NC}"
    
    if command -v python3 &> /dev/null; then
        local current_version=$(python3 --version | awk '{print $2}')
        echo -e "${GREEN}✅ Python3 已安装 (版本: $current_version)${NC}"
    else
        brew install python@3.11 &
        show_progress $!
        wait
        
        if command -v python3 &> /dev/null; then
            echo -e "${GREEN}✅ Python3 安装成功 ($(python3 --version))${NC}"
        else
            echo -e "${RED}❌ Python3 安装失败${NC}"
            exit 1
        fi
    fi
    
    # 安装 virtualenv
    echo -e "${BLUE}📦 安装 Python virtualenv...${NC}"
    python3 -m pip install --user virtualenv
    echo -e "${GREEN}✅ virtualenv 安装完成${NC}"
}

# 安装 Node.js
install_nodejs() {
    echo -e "${BLUE}📗 安装 Node.js...${NC}"
    
    if command -v node &> /dev/null; then
        local current_version=$(node --version)
        echo -e "${GREEN}✅ Node.js 已安装 (版本: $current_version)${NC}"
        return 0
    fi
    
    brew install node@18 &
    show_progress $!
    wait
    
    if command -v node &> /dev/null; then
        echo -e "${GREEN}✅ Node.js 安装成功 ($(node --version))${NC}"
        
        # 配置 npm 国内镜像
        npm config set registry https://registry.npmmirror.com
        echo -e "${GREEN}✅ npm 镜像源已配置为国内源${NC}"
        
    else
        echo -e "${RED}❌ Node.js 安装失败${NC}"
        exit 1
    fi
}

# 安装 Vue.js 开发工具
install_vue_tools() {
    echo -e "${BLUE}🖖 安装 Vue.js 开发工具...${NC}"
    
    # 安装 Vite
    npm install -g create-vite &
    show_progress $!
    wait
    
    # 安装 Vue CLI (可选)
    npm install -g @vue/cli &
    show_progress $!
    wait
    
    echo -e "${GREEN}✅ Vue.js 开发工具安装完成${NC}"
}

# 安装 SQLite 和数据库工具
install_database_tools() {
    echo -e "${BLUE}🗄️ 安装数据库工具...${NC}"
    
    # SQLite3 通常已预装在 macOS 上
    if command -v sqlite3 &> /dev/null; then
        echo -e "${GREEN}✅ SQLite3 已安装 ($(sqlite3 --version))${NC}"
    else
        brew install sqlite &
        show_progress $!
        wait
    fi
    
    # 安装 DB Browser for SQLite
    echo -e "${BLUE}📊 安装 DB Browser for SQLite...${NC}"
    brew install --cask db-browser-for-sqlite &
    show_progress $!
    wait
    
    echo -e "${GREEN}✅ 数据库工具安装完成${NC}"
}

# 安装 Docker 和开发工具
install_dev_tools() {
    echo -e "${BLUE}🐳 安装开发工具...${NC}"
    
    # 安装 Docker Desktop
    echo -e "${BLUE}📦 安装 Docker Desktop...${NC}"
    brew install --cask docker &
    show_progress $!
    wait
    
    # 安装 Postman
    echo -e "${BLUE}📮 安装 Postman...${NC}"
    brew install --cask postman &
    show_progress $!
    wait
    
    echo -e "${GREEN}✅ 开发工具安装完成${NC}"
}

# 验证安装
verify_installation() {
    echo -e "${BLUE}🔍 验证安装结果...${NC}"
    echo ""
    
    # 检查各个工具
    tools=("brew" "git" "go" "python3" "node" "npm" "sqlite3")
    
    for tool in "${tools[@]}"; do
        if command -v $tool &> /dev/null; then
            case $tool in
                "go") version=$(go version | awk '{print $3}') ;;
                "python3") version=$(python3 --version | awk '{print $2}') ;;
                "node") version=$(node --version) ;;
                "npm") version=$(npm --version) ;;
                "sqlite3") version=$(sqlite3 --version | awk '{print $1}') ;;
                *) version=$($tool --version 2>/dev/null | head -n1 | awk '{print $NF}' || echo "已安装") ;;
            esac
            echo -e "${GREEN}✅ $tool: $version${NC}"
        else
            echo -e "${RED}❌ $tool: 未安装${NC}"
        fi
    done
    
    echo ""
    echo -e "${GREEN}🎉 环境安装完成！${NC}"
    echo -e "${YELLOW}💡 建议重启终端或运行 'source ~/.zprofile' 来加载环境变量${NC}"
}

# 主函数
main() {
    echo -e "${BLUE}🚀 开始安装全栈开发环境...${NC}"
    echo ""
    
    check_network
    install_homebrew
    install_go
    install_python
    install_nodejs
    install_vue_tools
    install_database_tools
    install_dev_tools
    verify_installation
    
    echo ""
    echo -e "${GREEN}✨ 全栈开发环境安装完成！${NC}"
}

# 运行主函数
main "$@"
