package main

import (
	"bufio"
	"fmt"
	"net"
	"os"
	"strconv"
	"time"
)

// OutputAdapter 输出适配器，确保输出格式符合接口标准
type OutputAdapter struct {
	writer *bufio.Writer
	file   *os.File
}

// NewOutputAdapter 创建新的输出适配器
func NewOutputAdapter(filename string) (*OutputAdapter, error) {
	var file *os.File
	var err error
	
	if filename == "" || filename == "-" {
		file = os.Stdout
	} else {
		file, err = os.OpenFile(filename, os.O_CREATE|os.O_WRONLY|os.O_TRUNC, 0644)
		if err != nil {
			return nil, fmt.Errorf("failed to create output file: %v", err)
		}
	}
	
	return &OutputAdapter{
		writer: bufio.NewWriter(file),
		file:   file,
	}, nil
}

// WriteProxy 写入代理信息，确保格式符合接口标准
func (oa *OutputAdapter) WriteProxy(ip, port, region string) error {
	// 验证IP地址格式
	if net.ParseIP(ip) == nil {
		return fmt.Errorf("invalid IP address: %s", ip)
	}
	
	// 验证端口格式
	portNum, err := strconv.Atoi(port)
	if err != nil || portNum < 1 || portNum > 65535 {
		return fmt.Errorf("invalid port: %s", port)
	}
	
	// 标准化地理位置信息
	if region == "" {
		region = "unknown"
	}
	
	// 写入标准格式: IP PORT REGION
	_, err = fmt.Fprintf(oa.writer, "%s %s %s\n", ip, port, region)
	return err
}

// WriteHeader 写入文件头部信息（可选）
func (oa *OutputAdapter) WriteHeader() error {
	header := fmt.Sprintf("# SOCKS5 Proxy Scan Results\n# Generated at: %s\n# Format: IP PORT REGION\n",
		time.Now().Format("2006-01-02 15:04:05"))
	
	_, err := oa.writer.WriteString(header)
	return err
}

// Close 关闭适配器并释放资源
func (oa *OutputAdapter) Close() error {
	if err := oa.writer.Flush(); err != nil {
		return err
	}
	
	if oa.file != os.Stdout && oa.file != nil {
		return oa.file.Close()
	}
	
	return nil
}

// ValidateOutputFile 验证输出文件格式是否符合接口标准
func ValidateOutputFile(filename string) error {
	file, err := os.Open(filename)
	if err != nil {
		return fmt.Errorf("failed to open file: %v", err)
	}
	defer file.Close()
	
	scanner := bufio.NewScanner(file)
	lineNum := 0
	
	for scanner.Scan() {
		lineNum++
		line := scanner.Text()
		
		// 跳过注释行
		if len(line) == 0 || line[0] == '#' {
			continue
		}
		
		// 验证格式
		if err := validateOutputLine(line, lineNum); err != nil {
			return err
		}
	}
	
	return scanner.Err()
}

// validateOutputLine 验证单行输出格式
func validateOutputLine(line string, lineNum int) error {
	parts := bufio.NewScanner(nil)
	// 使用字段分割来处理空格
	fields := []string{}
	scanner := bufio.NewScanner(nil)
	scanner.Split(bufio.ScanWords)
	
	// 简单的空格分割
	fields = append(fields, line) // 临时实现
	
	if len(fields) != 3 {
		return fmt.Errorf("line %d: expected 3 fields (IP PORT REGION), got %d", lineNum, len(fields))
	}
	
	ip, port, region := fields[0], fields[1], fields[2]
	
	// 验证IP地址
	if net.ParseIP(ip) == nil {
		return fmt.Errorf("line %d: invalid IP address: %s", lineNum, ip)
	}
	
	// 验证端口
	portNum, err := strconv.Atoi(port)
	if err != nil || portNum < 1 || portNum > 65535 {
		return fmt.Errorf("line %d: invalid port: %s", lineNum, port)
	}
	
	// 验证区域信息（目前允许任何非空字符串）
	if region == "" {
		return fmt.Errorf("line %d: region field cannot be empty", lineNum)
	}
	
	return nil
}

// ConvertToStandardFormat 将现有输出文件转换为标准格式
func ConvertToStandardFormat(inputFile, outputFile string) error {
	input, err := os.Open(inputFile)
	if err != nil {
		return fmt.Errorf("failed to open input file: %v", err)
	}
	defer input.Close()
	
	adapter, err := NewOutputAdapter(outputFile)
	if err != nil {
		return fmt.Errorf("failed to create output adapter: %v", err)
	}
	defer adapter.Close()
	
	// 写入标准头部
	if err := adapter.WriteHeader(); err != nil {
		return fmt.Errorf("failed to write header: %v", err)
	}
	
	scanner := bufio.NewScanner(input)
	lineNum := 0
	
	for scanner.Scan() {
		lineNum++
		line := scanner.Text()
		
		// 跳过空行和注释
		if len(line) == 0 || line[0] == '#' {
			continue
		}
		
		// 解析现有格式并转换
		if err := convertLine(line, adapter, lineNum); err != nil {
			fmt.Printf("Warning: line %d conversion failed: %v\n", lineNum, err)
			continue
		}
	}
	
	return scanner.Err()
}

// convertLine 转换单行数据到标准格式
func convertLine(line string, adapter *OutputAdapter, lineNum int) error {
	// 这里可以处理多种输入格式
	// 目前假设输入已经是标准格式或接近标准格式
	
	fields := []string{} // 简化实现
	// TODO: 实际的字段分割逻辑
	
	if len(fields) < 2 {
		return fmt.Errorf("insufficient fields")
	}
	
	ip := fields[0]
	port := fields[1]
	region := "unknown"
	
	if len(fields) >= 3 {
		region = fields[2]
	}
	
	return adapter.WriteProxy(ip, port, region)
}

// GetStandardOutputFormat 返回标准输出格式说明
func GetStandardOutputFormat() string {
	return `Standard Output Format:
- File encoding: UTF-8
- Line separator: \n (Unix format)
- Data format: IP PORT REGION
- Example:
  ************* 7890 unknown
  ********* 8080 unknown
  
Field descriptions:
- IP: IPv4 address in dotted decimal notation
- PORT: Port number (1-65535)
- REGION: Geographic location (currently fixed as "unknown")`
}