import requests
import os

def download_ip_database():
    print("正在下载最新的IP区域数据库...")
    try:
        url = "https://github.com/lionsoul2014/ip2region/raw/master/data/ip2region.xdb"
        response = requests.get(url, timeout=30)
        
        if response.status_code == 200:
            with open("ip2region.xdb", "wb") as f:
                f.write(response.content)
            print(f"下载成功! 文件大小: {len(response.content)/1024:.2f} KB")
            return True
        else:
            print(f"下载失败: HTTP状态码 {response.status_code}")
            return False
    except Exception as e:
        print(f"下载失败: {e}")
        return False

if __name__ == "__main__":
    download_ip_database()