package main

import (
	"os"
	"sync"

	"github.com/lionsoul2014/ip2region/binding/golang/xdb"
)

var (
	regionSeracher     *xdb.Searcher
	regionSeracherInit sync.Once
)

func regionInit() {
	regionSeracherInit.Do(func() {
		// 直接尝试使用本地文件，不检查GitHub
		var err error
		if _, statErr := os.Stat(*regionDBPath); statErr == nil {
			regionSeracher, err = xdb.NewWithFileOnly(*regionDBPath)
			if err != nil {
				log.Error("regionSeracherInit NewWithFileOnly", "err", err)
			} else {
				log.Info("regionSeracherInit using local database", "path", *regionDBPath)
			}
			return // 无论成功失败都返回，不再尝试下载
		} else {
			log.Error("regionSeracherInit local database not found", "path", *regionDBPath, "err", statErr)
		}
	})
}

func ipRegion(ip string) (string, error) {
	regionInit()
	// 检查regionSeracher是否为nil
	if regionSeracher == nil {
		return "unknown", nil // 返回unknown而不是错误，避免程序中断
	}
	region, err := regionSeracher.SearchByStr(ip)
	if err != nil {
		log.Error("ipRegion SearchByStr", "err", err)
		return "unknown", nil // 返回unknown而不是错误，避免程序中断
	}
	return region, nil
}
