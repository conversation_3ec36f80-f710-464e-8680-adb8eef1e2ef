#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import time
import json
import requests
from typing import Tuple, Dict, List, Optional
import argparse
from concurrent.futures import ThreadPoolExecutor

def query_ip_online(ip: str) -> str:
    """使用在线API查询IP地址的地理位置信息"""
    try:
        # 使用免费的IP-API服务
        response = requests.get(f"http://ip-api.com/json/{ip}?lang=zh-CN", timeout=5)
        data = response.json()
        
        if data.get("status") == "success":
            country = data.get("country", "")
            region = data.get("regionName", "")
            city = data.get("city", "")
            isp = data.get("isp", "")
            
            location_parts = []
            if country:
                location_parts.append(country)
            if region:
                location_parts.append(region)
            if city:
                location_parts.append(city)
            if isp:
                location_parts.append(isp)
            
            return "|".join(location_parts) if location_parts else "未知位置"
        else:
            return "未知位置"
    except Exception as e:
        return f"查询失败: {str(e)}"

def process_ip_file(input_file: str, output_file: str, threads: int = 5, delay: float = 0.5) -> None:
    """处理IP地址文件，查询地理位置信息并输出到新文件"""
    try:
        # 读取IP地址列表
        with open(input_file, 'r') as f:
            ip_list = [line.strip() for line in f if line.strip()]
        
        total_ips = len(ip_list)
        print(f"共读取 {total_ips} 个IP地址")
        
        # 定义查询函数
        def query_ip(ip: str) -> Tuple[str, str]:
            try:
                location = query_ip_online(ip)
                # 添加延迟以避免API限制
                time.sleep(delay)
                return ip, location
            except Exception as e:
                return ip, f"查询失败: {str(e)}"
        
        # 使用线程池并行查询
        results = []
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=threads) as executor:
            for i, result in enumerate(executor.map(query_ip, ip_list), 1):
                results.append(result)
                if i % 10 == 0 or i == total_ips:
                    elapsed = time.time() - start_time
                    print(f"已处理 {i}/{total_ips} 个IP ({i/total_ips*100:.1f}%), 耗时: {elapsed:.2f}秒")
        
        # 统计成功和失败的查询
        success_count = sum(1 for _, loc in results if not loc.startswith("查询失败") and loc != "未知位置")
        
        # 写入结果到输出文件
        with open(output_file, 'w', encoding='utf-8') as f:
            for ip, location in results:
                f.write(f"{ip}\t{location}\n")
        
        elapsed = time.time() - start_time
        print(f"处理完成! 总耗时: {elapsed:.2f}秒, 平均速度: {total_ips/elapsed:.1f}IP/秒")
        print(f"成功查询: {success_count}/{total_ips} ({success_count/total_ips*100:.1f}%)")
        print(f"结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"处理失败: {e}")
        sys.exit(1)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="批量查询IP地址的地理位置信息(在线API版)")
    parser.add_argument("-i", "--input", required=True, help="输入文件路径，包含IP地址列表(每行一个)")
    parser.add_argument("-o", "--output", required=True, help="输出文件路径，将包含IP地址及其地理位置信息")
    parser.add_argument("-t", "--threads", type=int, default=5, help="并行查询的线程数(默认为5)")
    parser.add_argument("-d", "--delay", type=float, default=0.5, help="每次查询的延迟时间(秒)，避免API限制(默认为0.5)")
    
    args = parser.parse_args()
    
    # 检查输入文件是否存在
    if not os.path.exists(args.input):
        print(f"错误: 输入文件不存在: {args.input}")
        sys.exit(1)
    
    # 处理IP地址文件
    process_ip_file(args.input, args.output, args.threads, args.delay)

if __name__ == "__main__":
    main()