def generate_config():
    print("请输入CIDR地址（每行一个，输入'done'结束）：")
    cidrs = []
    index = 1
    
    while True:
        try:
            line = input(f"CIDR {index}: ").strip()
            if line.lower() == 'done':
                break
            
            # 简单验证CIDR格式
            if '/' in line:
                ip, mask = line.split('/')
                if mask.isdigit() and 0 <= int(mask) <= 32:
                    cidrs.append(line)
                    index += 1
                else:
                    print("无效的CIDR格式，掩码必须在0-32之间")
            else:
                print("无效的CIDR格式，请使用类似 '***********/24' 的格式")
        except KeyboardInterrupt:
            print("\n已取消输入")
            return
        except Exception as e:
            print(f"输入错误: {e}")
    
    # 获取输出文件名
    output_file = input("请输入扫描结果输出文件名（直接回车默认为 result.txt）: ").strip()
    if not output_file:
        output_file = "result.txt"
    
    # 生成配置文件
    with open('config.ini', 'w', encoding='utf-8') as f:
        f.write("[CIDR]\n")
        for i, cidr in enumerate(cidrs, 1):
            f.write(f"cidr{i}={cidr}\n")
        
        f.write("\n[OUTPUT]\n")
        f.write(f"file={output_file}\n")
    
    print(f"\n配置文件已生成：config.ini")
    print(f"共添加了 {len(cidrs)} 个CIDR地址")

if __name__ == "__main__":
    generate_config() 