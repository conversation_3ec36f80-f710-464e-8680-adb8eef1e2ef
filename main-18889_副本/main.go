package main

import (
	"flag"
	"fmt"
	"io"
	"log/slog"
	"net"
	"os"
	"strconv"
	"strings"
	"sync/atomic"
	"time"
)

var Version string = "0.1.2"

var (
	outfile = flag.String("o", "", "result output file")
	output  io.Writer

	limit    = flag.Int("limit", 5, "thread limit")
	ports    []string
	portsStr = flag.String("ports", "7890-7893,10810", "ports, support range like 8888-8897")

	region        = flag.String("region", "", "filter region")
	excludeRegion = flag.String("exclude_region", "", "exclude region")
	regionDBPath  = flag.String("region_db_path", "./ip2region.xdb", "region db path")

	connectTimeout  = flag.Int("connect_timeout", 2, "ip connect timeout")
	connectDeadline = flag.Int("connect_deadline", 1, "connect read write timeout")

	count atomic.Int32

	q   = flag.Bool("q", false, "quiet log")
	log *slog.Logger

	configFile = flag.String("c", "./config.ini", "config file path")
)

func main() {
	flag.Parse()

	config, err := LoadConfig(*configFile)
	if err != nil {
		log.Error("load config failed", "err", err)
		return
	}

	if config.Output != "" && *outfile == "" {
		*outfile = config.Output
	}

	ports, err = parsePorts(*portsStr)
	if err != nil {
		log.Error("parse ports failed", "err", err)
		return
	}

	if *q {
		log = newLogger(slog.LevelError)
	} else {
		log = newLogger(slog.LevelInfo)
	}

	var ips []string
	for _, cidr := range config.CIDRs {
		expandedIPs, err := expandCIDR(cidr)
		if err != nil {
			log.Error("expand CIDR failed", "cidr", cidr, "err", err)
			continue
		}
		ips = append(ips, expandedIPs...)
	}

	outputFile := *outfile
	if outputFile == "" || outputFile == "-" {
		outputFile = "print"
		output = os.Stdout
	} else {
		f, err := os.OpenFile(outputFile, os.O_CREATE|os.O_WRONLY|os.O_TRUNC|os.O_SYNC, 0644)
		if err != nil {
			log.Error("open output file", "err", err)
			return
		}
		defer f.Close()
		output = f
	}

	log.Info("info", "Version", Version, "HomePage", "https://github.com/Rehtt/scanSocks5")
	log.Info("config", "ThreadLimit", *limit, "Ports", ports, "FilterRegion", *region, "Output", outputFile)

	thread := NewThread(*limit)

	for _, ip := range ips {
		if ip == "" {
			continue
		}
		handleScan(thread, ip)
	}

	thread.Wait()
	slog.Info("done", "count", count.Load())
}

func handleScan(t *Thread, ip string) {
	r, err := ipRegion(ip)
	if err != nil || r == "" {
		// 区域查询失败时继续执行，不过滤区域
		r = "unknown"
	}

	if *region != "" && !strings.Contains(r, *region) {
		return
	}
	if *excludeRegion != "" && strings.Contains(r, *excludeRegion) {
		return
	}
	for _, port := range ports {
		t.Run(func(arg map[string]any) {
			ip := arg["ip"].(string)
			port := arg["port"].(string)
			if scan(ip, port) {
				fmt.Fprintln(output, ip, port, r)
				count.Add(1)
			}
		}, map[string]any{
			"ip":   ip,
			"port": port,
		})
	}
}

func scan(ip, port string) bool {
	addr := ip + ":" + port
	n, err := net.DialTimeout("tcp", addr, time.Second*time.Duration(*connectTimeout))
	if err != nil {
		return false
	}
	n.SetDeadline(time.Now().Add(time.Second * time.Duration(*connectDeadline)))
	defer n.Close()
	n.Write([]byte("\x05\x01\x00"))
	tmp := make([]byte, 2)
	n.Read(tmp)
	if tmp[0] == 5 {
		return true
	}
	return false
}

func expandCIDR(cidr string) ([]string, error) {
	ip, ipnet, err := net.ParseCIDR(cidr)
	if err != nil {
		return nil, err
	}

	var ips []string
	for ip := ip.Mask(ipnet.Mask); ipnet.Contains(ip); inc(ip) {
		ips = append(ips, ip.String())
	}
	return ips, nil
}

func inc(ip net.IP) {
	for j := len(ip) - 1; j >= 0; j-- {
		ip[j]++
		if ip[j] > 0 {
			break
		}
	}
}

func parsePorts(portsStr string) ([]string, error) {
	var result []string
	parts := strings.Split(portsStr, ",")

	for _, part := range parts {
		part = strings.TrimSpace(part)
		if part == "" {
			continue
		}

		if strings.Contains(part, "-") {
			rangeParts := strings.Split(part, "-")
			if len(rangeParts) != 2 {
				return nil, fmt.Errorf("invalid port range format: %s", part)
			}

			start, err := strconv.Atoi(strings.TrimSpace(rangeParts[0]))
			if err != nil {
				return nil, fmt.Errorf("invalid start port: %s", rangeParts[0])
			}

			end, err := strconv.Atoi(strings.TrimSpace(rangeParts[1]))
			if err != nil {
				return nil, fmt.Errorf("invalid end port: %s", rangeParts[1])
			}

			if start > end || start < 1 || end > 65535 {
				return nil, fmt.Errorf("invalid port range: %d-%d", start, end)
			}

			for port := start; port <= end; port++ {
				result = append(result, strconv.Itoa(port))
			}
		} else {
			port, err := strconv.Atoi(part)
			if err != nil {
				return nil, fmt.Errorf("invalid port: %s", part)
			}
			if port < 1 || port > 65535 {
				return nil, fmt.Errorf("port out of range: %d", port)
			}
			result = append(result, part)
		}
	}

	return result, nil
}
