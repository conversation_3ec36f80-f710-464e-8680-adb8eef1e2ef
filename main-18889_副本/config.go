package main

import (
	"fmt"
	"net"

	"gopkg.in/ini.v1"
)

type Config struct {
	CIDRs  []string
	Output string
}

func LoadConfig(path string) (*Config, error) {
	cfg, err := ini.Load(path)
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %v", err)
	}

	config := &Config{}

	// 验证配置文件格式
	if !cfg.HasSection("CIDR") {
		return nil, fmt.Errorf("invalid config file: missing [CIDR] section")
	}
	if !cfg.HasSection("OUTPUT") {
		return nil, fmt.Errorf("invalid config file: missing [OUTPUT] section")
	}

	// 读取所有CIDR配置
	cidrSection := cfg.Section("CIDR")
	if len(cidrSection.Keys()) == 0 {
		return nil, fmt.Errorf("invalid config file: no CIDR entries found")
	}

	for _, key := range cidrSection.Keys() {
		cidr := key.Value()
		if _, _, err := net.ParseCIDR(cidr); err == nil {
			config.CIDRs = append(config.CIDRs, cidr)
		} else {
			return nil, fmt.Errorf("invalid CIDR format in config: %s", cidr)
		}
	}

	if len(config.CIDRs) == 0 {
		return nil, fmt.Errorf("no valid CIDR entries found in config")
	}

	// 读取输出配置
	if outputSection := cfg.Section("OUTPUT"); outputSection != nil {
		config.Output = outputSection.Key("file").String()
		if config.Output == "" {
			return nil, fmt.Errorf("invalid config file: empty output file path")
		}
	}

	return config, nil
}
