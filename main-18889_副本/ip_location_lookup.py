#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import struct
import time
from typing import Tuple, Optional, List, Dict
import argparse
from concurrent.futures import ThreadPoolExecutor

class XdbSearcher:
    """IP地址查询类，使用ip2region.xdb数据库"""
    
    def __init__(self, db_path: str):
        self.db_file = None
        self.header_len = 0
        self.vector_index = None
        self.content_buff = None
        
        # 检查数据库文件是否存在
        if not os.path.exists(db_path):
            raise FileNotFoundError(f"数据库文件不存在: {db_path}")
        
        # 读取数据库文件
        try:
            with open(db_path, "rb") as f:
                self.db_file = f.read()
                
            # 解析头部信息
            self.header_len = self._get_int(0)
            self.vector_index = self.db_file[8:self.header_len]
            self.content_buff = self.db_file[self.header_len:]
        except Exception as e:
            raise RuntimeError(f"初始化XdbSearcher失败: {e}")
    
    def _get_int(self, offset: int) -> int:
        """从数据库文件中读取一个整数"""
        return struct.unpack("<L", self.db_file[offset:offset+4])[0]
    
    def _search_index(self, ip: int) -> Tuple[int, int]:
        """搜索IP索引"""
        il, ih = 0, int(len(self.vector_index)/8) - 1
        
        while il <= ih:
            im = int((il + ih) / 2)
            offset = im * 8
            
            # 读取当前索引的IP段
            start_ip = self._get_int(offset)
            end_ip_offset = self._get_int(offset + 4)
            end_ip = self._get_int(end_ip_offset)
            
            # 判断IP是否在当前段内
            if ip >= start_ip and ip <= end_ip:
                data_len = self._get_int(end_ip_offset + 4)
                data_offset = self._get_int(end_ip_offset + 8)
                return data_offset, data_len
            
            if ip < start_ip:
                ih = im - 1
            else:
                il = im + 1
        
        raise ValueError("IP地址未找到")
    
    def search(self, ip: str) -> str:
        """查询IP地址的地理位置信息"""
        try:
            # 将IP地址转换为整数
            ip_parts = ip.split('.')
            if len(ip_parts) != 4:
                return "无效的IPv4地址"
            
            try:
                ip_int = (int(ip_parts[0]) << 24) | (int(ip_parts[1]) << 16) | (int(ip_parts[2]) << 8) | int(ip_parts[3])
            except ValueError:
                return "IP地址格式错误"
            
            # 搜索索引
            data_offset, data_len = self._search_index(ip_int)
            
            # 读取地理位置信息
            location = self.content_buff[data_offset:data_offset+data_len].decode('utf-8')
            if not location or location.strip() == "":
                return "未知位置"
            return location
        except Exception as e:
            return f"查询失败: {str(e)}"

def process_ip_file(input_file: str, output_file: str, db_path: str, threads: int = 10) -> None:
    """处理IP地址文件，查询地理位置信息并输出到新文件"""
    try:
        # 初始化IP查询器
        searcher = XdbSearcher(db_path)
        
        # 读取IP地址列表
        with open(input_file, 'r') as f:
            ip_list = [line.strip() for line in f if line.strip()]
        
        total_ips = len(ip_list)
        print(f"共读取 {total_ips} 个IP地址")
        
        # 定义查询函数
        def query_ip(ip: str) -> Tuple[str, str]:
            try:
                location = searcher.search(ip)
                return ip, location
            except Exception as e:
                return ip, f"查询失败: {e}"
        
        # 使用线程池并行查询
        results = []
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=threads) as executor:
            for i, result in enumerate(executor.map(query_ip, ip_list), 1):
                results.append(result)
                if i % 1000 == 0 or i == total_ips:
                    elapsed = time.time() - start_time
                    print(f"已处理 {i}/{total_ips} 个IP ({i/total_ips*100:.1f}%), 耗时: {elapsed:.2f}秒, 速度: {i/elapsed:.1f}IP/秒")
        
        # 写入结果到输出文件
        with open(output_file, 'w', encoding='utf-8') as f:
            for ip, location in results:
                f.write(f"{ip}\t{location}\n")
        
        elapsed = time.time() - start_time
        print(f"处理完成! 总耗时: {elapsed:.2f}秒, 平均速度: {total_ips/elapsed:.1f}IP/秒")
        print(f"结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"处理失败: {e}")
        sys.exit(1)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="批量查询IP地址的地理位置信息")
    parser.add_argument("-i", "--input", required=True, help="输入文件路径，包含IP地址列表(每行一个)")
    parser.add_argument("-o", "--output", required=True, help="输出文件路径，将包含IP地址及其地理位置信息")
    parser.add_argument("-d", "--db", default="ip2region.xdb", help="ip2region.xdb数据库文件路径(默认为当前目录下的ip2region.xdb)")
    parser.add_argument("-t", "--threads", type=int, default=10, help="并行查询的线程数(默认为10)")
    
    args = parser.parse_args()
    
    # 检查输入文件是否存在
    if not os.path.exists(args.input):
        print(f"错误: 输入文件不存在: {args.input}")
        sys.exit(1)
    
    # 检查数据库文件是否存在
    if not os.path.exists(args.db):
        print(f"错误: 数据库文件不存在: {args.db}")
        sys.exit(1)
    
    # 处理IP地址文件
    process_ip_file(args.input, args.output, args.db, args.threads)

if __name__ == "__main__":
    main()
