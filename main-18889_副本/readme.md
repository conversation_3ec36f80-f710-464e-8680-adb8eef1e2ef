# SOCKS5代理端口扫描工具

**项目定位**: 专用网络扫描微服务  
**版本**: v0.1.2  
**语言**: Go  
**复杂度**: 低  
**维护性**: 高  

## 项目概述

这是一个轻量级的SOCKS5代理端口扫描工具，专门用于发现网络中潜在的SOCKS5代理服务。作为代理发现工作流的第一环节，本工具提供高效的并发扫描能力，支持CIDR网段批量扫描和端口范围扫描。

## 核心功能

- **CIDR网段扫描**: 支持批量扫描指定网段内的所有IP地址
- **多端口范围扫描**: 支持端口范围表达式 (如 7890-7893,10810)
- **高并发处理**: 可配置的并发线程数，提升扫描效率
- **SOCKS5协议验证**: 通过标准SOCKS5握手验证代理可用性
- **灵活输出**: 支持文件输出和标准输出
- **地理位置过滤**: 可按地理位置过滤扫描结果 (需要额外配置)

## 技术特点

- **单一职责**: 专注于代理发现，功能聚焦
- **轻量级设计**: 最小化依赖，易于集成和部署
- **无状态架构**: 支持水平扩展和微服务集成
- **标准化输出**: 便于与其他工具链集成

## 安装

### 编译安装
```shell
git clone https://github.com/Rehtt/scanSocks5
cd scanSocks5
go build -o scanSocks5
```

### 依赖说明
- Go 1.21.5+
- gopkg.in/ini.v1 (配置文件解析)

## 配置文件

配置文件采用INI格式，包含CIDR网段和输出设置：

```ini
[CIDR]
cidr1 = ***********/24
cidr2 = 10.0.0.0/16
cidr3 = **********/12

[OUTPUT]
file = ip_output.txt
```

## 使用方法

### 基础用法
```shell
# 使用默认配置扫描
./scanSocks5 -c config.ini

# 自定义端口和并发数
./scanSocks5 -c config.ini -ports 7890-7893,10810 -limit 20

# 输出到指定文件
./scanSocks5 -c config.ini -o scan_results.txt

# 静默模式运行
./scanSocks5 -c config.ini -q
```

### 高级用法
```shell
# 配合zmap进行大规模扫描
zmap -n 10000000 -p 7890 -o discovered_ips.txt
# 然后对发现的IP进行SOCKS5验证
./scanSocks5 -c config.ini -ports 7890,8080,1080

# 管道方式处理
zmap -n 1000000 -p 7890 -q -o - | ./scanSocks5 -c config.ini -ports 7890-7893
```

## 命令行参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| -c | string | ./config.ini | 配置文件路径 |
| -o | string | - | 结果输出文件，空则输出到标准输出 |
| -limit | int | 5 | 最大并发线程数 |
| -ports | string | 7890-7893,10810 | 扫描端口范围 |
| -connect_timeout | int | 2 | TCP连接超时时间(秒) |
| -connect_deadline | int | 1 | 读写操作超时时间(秒) |
| -q | bool | false | 静默模式，仅输出错误日志 |

## 输出格式

标准输出格式为每行一个发现的代理：
```
IP PORT REGION
************* 7890 unknown
********* 8080 unknown
```

## 与工作流集成

本工具作为SOCKS5代理管理工作流的第一环节：

```
1. 扫描发现 (本工具)
   ↓
2. 代理验证 (2to3 18889工具)
   ↓  
3. 数据管理 (2to3 rebuild工具)
```

### 数据流标准化

输出格式与下游工具兼容：
- **输出**: `IP PORT REGION` 格式
- **下游输入**: 2to3 18889工具可直接处理前两列数据

## 性能优化

### 扫描策略
- 合理设置并发数(-limit)，避免网络拥塞
- 根据网络环境调整超时参数
- 大规模扫描时建议分批处理

### 资源管理
- 自动管理TCP连接生命周期
- 内存占用低，适合长时间运行
- 支持优雅退出和资源清理

## 已知限制

1. **地理位置功能**: 当前版本已禁用地理位置查询以避免网络依赖
2. **协议支持**: 仅支持SOCKS5协议检测
3. **认证检测**: 不检测代理认证类型，仅验证连通性

## 故障排除

### 常见问题
1. **配置文件错误**: 检查INI格式和CIDR语法
2. **网络超时**: 调整connect_timeout和connect_deadline参数
3. **并发过高**: 降低limit参数避免系统资源耗尽

### 调试模式
```shell
# 启用详细日志
./scanSocks5 -c config.ini

# 检查配置文件
./scanSocks5 -c config.ini -limit 1
```

## 开发说明

### 代码结构
- `main.go`: 主程序逻辑和命令行处理
- `config.go`: 配置文件解析和验证
- `region.go`: 地理位置查询(当前禁用)
- `thread.go`: 并发控制和线程管理

### 扩展开发
- 支持插件式协议检测
- 可扩展输出格式
- 可集成更多地理位置数据源

## 更新日志

### v0.1.2 (当前版本)
- ✅ 修复网络依赖问题，暂时禁用地理位置查询
- ✅ 改进错误处理和超时控制
- ✅ 优化TCP连接管理
- ✅ 完善文档和使用说明
- ✅ 标准化输出格式

### 后续计划
- [ ] 重新启用地理位置查询功能
- [ ] 支持IPv6扫描
- [ ] 添加扫描进度显示
- [ ] 支持结果缓存和去重
