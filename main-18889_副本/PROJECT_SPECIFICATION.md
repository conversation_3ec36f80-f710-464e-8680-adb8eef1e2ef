# SOCKS5代理端口扫描工具 - 项目规格说明

## 项目定位

### 核心定位
- **类型**: 专用网络扫描微服务
- **定位**: 轻量级工具，单一职责
- **角色**: 代理发现工作流的第一环节
- **复杂度**: 低（聚焦核心功能）
- **维护性**: 高（最小化依赖）

### 设计理念
- **单一职责原则**: 专注于SOCKS5代理端口发现
- **轻量级设计**: 最小化资源占用和依赖
- **无状态架构**: 支持水平扩展和微服务集成
- **标准化输出**: 便于与下游工具集成

## 详细需求分析

### 功能性需求

#### FR-001 CIDR网段扫描
- **需求描述**: 支持标准CIDR格式的网段批量扫描
- **输入格式**: 配置文件中的CIDR列表
- **处理逻辑**: 自动展开CIDR为完整IP列表
- **验证要求**: CIDR格式验证，无效格式拒绝
- **示例**: `***********/24` -> `***********-254`

#### FR-002 多端口范围扫描
- **需求描述**: 支持灵活的端口范围表达式
- **格式支持**: 
  - 单端口: `7890`
  - 端口范围: `7890-7893` 
  - 混合格式: `7890-7893,10810,8080`
- **验证要求**: 端口范围1-65535，格式验证
- **优化需求**: 避免重复端口，自动排序

#### FR-003 SOCKS5协议验证
- **需求描述**: 通过标准SOCKS5握手验证代理可用性
- **协议实现**: 发送 `\x05\x01\x00` 握手请求
- **响应验证**: 检查返回的第一字节是否为5
- **超时控制**: 可配置的连接和读写超时
- **错误处理**: 优雅处理网络错误和超时

#### FR-004 并发扫描控制
- **需求描述**: 高效的并发扫描机制
- **线程管理**: 可配置的最大并发数
- **资源控制**: 防止系统资源耗尽
- **负载均衡**: 工作负载均匀分配
- **性能优化**: 最大化扫描效率

#### FR-005 灵活输出机制
- **需求描述**: 支持多种输出方式
- **输出选项**: 文件输出、标准输出
- **格式标准**: `IP PORT REGION` 格式
- **性能要求**: 实时输出，避免内存积累
- **同步要求**: 输出顺序可控

### 非功能性需求

#### NFR-001 性能要求
- **扫描速度**: 支持大规模网段快速扫描
- **内存占用**: 低内存占用，适合资源受限环境
- **CPU利用**: 高效的CPU利用率
- **网络优化**: 避免网络拥塞和限制

#### NFR-002 可靠性要求
- **错误恢复**: 单个IP失败不影响整体扫描
- **超时处理**: 合理的超时机制防止卡死
- **资源管理**: 自动管理网络连接生命周期
- **异常处理**: 优雅处理各种异常情况

#### NFR-003 可配置性要求
- **配置文件**: 支持INI格式配置文件
- **命令行**: 丰富的命令行参数支持
- **参数覆盖**: 命令行参数覆盖配置文件
- **验证机制**: 配置参数有效性验证

#### NFR-004 集成性要求
- **标准化接口**: 与其他工具兼容的输入输出格式
- **无依赖部署**: 单文件部署，最小化运行依赖
- **跨平台支持**: 支持主流操作系统
- **容器化**: 支持Docker容器部署

## 技术规格

### 架构设计
```
┌─────────────────────────────────────────┐
│                Main程序                  │
├─────────────────────────────────────────┤
│  配置管理  │  命令行解析  │  日志系统   │
├─────────────────────────────────────────┤
│        并发控制器 (Thread Manager)        │
├─────────────────────────────────────────┤
│  CIDR扩展  │  端口解析  │  扫描引擎   │
├─────────────────────────────────────────┤
│     网络层 (TCP连接 + SOCKS5协议)        │
└─────────────────────────────────────────┘
```

### 数据流设计
```
配置文件(INI) + 命令行参数
         ↓
    配置验证和合并
         ↓
    CIDR展开为IP列表
         ↓
    端口范围解析
         ↓
    并发任务分发
         ↓
   SOCKS5协议测试
         ↓
    结果收集和输出
```

### 接口规格

#### 输入接口
1. **配置文件接口**
   ```ini
   [CIDR]
   cidr1 = ***********/24
   cidr2 = 10.0.0.0/16
   
   [OUTPUT]
   file = scan_results.txt
   ```

2. **命令行接口**
   ```bash
   scanSocks5 -c config.ini -ports 7890-7893 -limit 20 -o output.txt
   ```

#### 输出接口
```
# 标准输出格式
IP PORT REGION
***********00 7890 unknown
********* 8080 unknown
```

### 错误处理策略

#### 配置错误
- **无效CIDR**: 记录错误，跳过该条目
- **端口超范围**: 记录错误，使用默认端口
- **文件不存在**: 使用默认配置继续运行

#### 网络错误
- **连接超时**: 记录调试信息，标记为不可用
- **连接拒绝**: 静默处理，标记为不可用
- **网络不可达**: 记录警告，继续其他IP

#### 系统错误
- **资源耗尽**: 动态降低并发数
- **磁盘空间不足**: 切换到标准输出
- **权限不足**: 记录错误，提示用户

## 扩展规划

### 近期扩展 (1-3个月)
- **IPv6支持**: 扩展支持IPv6网段扫描
- **协议扩展**: 支持HTTP/HTTPS代理检测
- **结果缓存**: 避免重复扫描相同目标
- **进度显示**: 实时显示扫描进度

### 中期扩展 (3-6个月)
- **地理位置**: 重新启用IP地理位置查询
- **质量评估**: 基于响应时间的代理质量评分
- **智能调度**: 根据网络状况动态调整参数
- **结果分析**: 扫描结果统计和分析

### 长期扩展 (6个月以上)
- **分布式扫描**: 支持多节点协作扫描
- **机器学习**: 基于历史数据优化扫描策略
- **Web界面**: 提供Web管理界面
- **API服务**: 提供RESTful API接口

## 约束和限制

### 技术约束
- **Go版本**: 要求Go 1.21.5+
- **网络依赖**: 当前版本不依赖外部网络服务
- **平台限制**: 依赖TCP socket，需要网络访问权限

### 业务约束
- **扫描范围**: 仅限于用户有权扫描的网段
- **使用场景**: 主要用于合法的网络管理和代理服务管理
- **性能限制**: 受网络带宽和目标响应速度限制

### 法律约束
- **合规要求**: 必须在合法授权范围内使用
- **隐私保护**: 不收集或存储敏感信息
- **责任免责**: 用户需承担使用责任

## 质量保证

### 测试策略
- **单元测试**: 核心功能模块单元测试
- **集成测试**: 完整扫描流程集成测试
- **性能测试**: 大规模网段扫描性能测试
- **兼容性测试**: 多平台兼容性验证

### 监控指标
- **扫描成功率**: 成功扫描的IP占比
- **扫描速度**: 每秒处理的IP数量
- **资源使用**: CPU和内存使用情况
- **错误率**: 各类错误的发生频率

这个规格文档明确了项目的定位、需求、技术实现和发展规划，为后续的开发和维护提供了清晰的指导。